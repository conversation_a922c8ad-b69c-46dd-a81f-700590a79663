### Notepad++ raylib config files

This folder includes some useful files to config Notepad++ for raylib.

#### raylib functions autocomplete - c_raylib.xml

Autocomplete information for Notepad++. The contents of this file should be copied inside raylib\Notepad++\plugins\APIs\c.xml file.

This file has been automatically generated using the provided tool: `raylib_npp_parser`

This simple tool basically parses raylib.h header for functions starting by RLAPI, extracts all required information and generates de Notepad++ autocomplete XML equivalent.

To use the tool, just drag and drop raylib.h over raylib_npp_parser program.

#### Notepad++ NppExec compilation scripts - npes_saved.txt

A series of scripts for Notepad++ NppExec plugin to compile raylib library and examples.


