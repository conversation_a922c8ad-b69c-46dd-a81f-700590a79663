        <!-------------------------------------------------------------------------------------- -->
        <!-- Window and Graphics Device Functions (Module: core) -->
        <!-------------------------------------------------------------------------------------- -->

        <!-- Window-related functions -->
        <KeyWord name="InitWindow" func="yes">
            <Overload retVal="void" descr="Initialize window and OpenGL context">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="const char *title" />
            </Overload>
        </KeyWord>
        <KeyWord name="WindowShouldClose" func="yes">
            <Overload retVal="bool" descr="Check if KEY_ESCAPE pressed or Close icon pressed"></Overload>
        </KeyWord>
        <KeyWord name="CloseWindow" func="yes">
            <Overload retVal="void" descr="Close window and unload OpenGL context"></Overload>
        </KeyWord>
        <KeyWord name="IsWindowReady" func="yes">
            <Overload retVal="bool" descr="Check if window has been initialized successfully"></Overload>
        </KeyWord>
        <KeyWord name="IsWindowFullscreen" func="yes">
            <Overload retVal="bool" descr="Check if window is currently fullscreen"></Overload>
        </KeyWord>
        <KeyWord name="IsWindowHidden" func="yes">
            <Overload retVal="bool" descr="Check if window is currently hidden (only PLATFORM_DESKTOP)"></Overload>
        </KeyWord>
        <KeyWord name="IsWindowMinimized" func="yes">
            <Overload retVal="bool" descr="Check if window is currently minimized (only PLATFORM_DESKTOP)"></Overload>
        </KeyWord>
        <KeyWord name="IsWindowMaximized" func="yes">
            <Overload retVal="bool" descr="Check if window is currently maximized (only PLATFORM_DESKTOP)"></Overload>
        </KeyWord>
        <KeyWord name="IsWindowFocused" func="yes">
            <Overload retVal="bool" descr="Check if window is currently focused (only PLATFORM_DESKTOP)"></Overload>
        </KeyWord>
        <KeyWord name="IsWindowResized" func="yes">
            <Overload retVal="bool" descr="Check if window has been resized last frame"></Overload>
        </KeyWord>
        <KeyWord name="IsWindowState" func="yes">
            <Overload retVal="bool" descr="Check if one specific window flag is enabled">
                <Param name="unsigned int flag" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetWindowState" func="yes">
            <Overload retVal="void" descr="Set window configuration state using flags (only PLATFORM_DESKTOP)">
                <Param name="unsigned int flags" />
            </Overload>
        </KeyWord>
        <KeyWord name="ClearWindowState" func="yes">
            <Overload retVal="void" descr="Clear window configuration state flags">
                <Param name="unsigned int flags" />
            </Overload>
        </KeyWord>
        <KeyWord name="ToggleFullscreen" func="yes">
            <Overload retVal="void" descr="Toggle window state: fullscreen/windowed (only PLATFORM_DESKTOP)"></Overload>
        </KeyWord>
        <KeyWord name="MaximizeWindow" func="yes">
            <Overload retVal="void" descr="Set window state: maximized, if resizable (only PLATFORM_DESKTOP)"></Overload>
        </KeyWord>
        <KeyWord name="MinimizeWindow" func="yes">
            <Overload retVal="void" descr="Set window state: minimized, if resizable (only PLATFORM_DESKTOP)"></Overload>
        </KeyWord>
        <KeyWord name="RestoreWindow" func="yes">
            <Overload retVal="void" descr="Set window state: not minimized/maximized (only PLATFORM_DESKTOP)"></Overload>
        </KeyWord>
        <KeyWord name="SetWindowIcon" func="yes">
            <Overload retVal="void" descr="Set icon for window (single image, RGBA 32bit, only PLATFORM_DESKTOP)">
                <Param name="Image image" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetWindowIcons" func="yes">
            <Overload retVal="void" descr="Set icon for window (multiple images, RGBA 32bit, only PLATFORM_DESKTOP)">
                <Param name="Image *images" />
                <Param name="int count" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetWindowTitle" func="yes">
            <Overload retVal="void" descr="Set title for window (only PLATFORM_DESKTOP)">
                <Param name="const char *title" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetWindowPosition" func="yes">
            <Overload retVal="void" descr="Set window position on screen (only PLATFORM_DESKTOP)">
                <Param name="int x" />
                <Param name="int y" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetWindowMonitor" func="yes">
            <Overload retVal="void" descr="Set monitor for the current window (fullscreen mode)">
                <Param name="int monitor" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetWindowMinSize" func="yes">
            <Overload retVal="void" descr="Set window minimum dimensions (for FLAG_WINDOW_RESIZABLE)">
                <Param name="int width" />
                <Param name="int height" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetWindowSize" func="yes">
            <Overload retVal="void" descr="Set window dimensions">
                <Param name="int width" />
                <Param name="int height" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetWindowOpacity" func="yes">
            <Overload retVal="void" descr="Set window opacity [0.0f..1.0f] (only PLATFORM_DESKTOP)">
                <Param name="float opacity" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetWindowHandle" func="yes">
            <Overload retVal="void" descr="Get native window handle"></Overload>
        </KeyWord>
        <KeyWord name="GetScreenWidth" func="yes">
            <Overload retVal="int" descr="Get current screen width"></Overload>
        </KeyWord>
        <KeyWord name="GetScreenHeight" func="yes">
            <Overload retVal="int" descr="Get current screen height"></Overload>
        </KeyWord>
        <KeyWord name="GetRenderWidth" func="yes">
            <Overload retVal="int" descr="Get current render width (it considers HiDPI)"></Overload>
        </KeyWord>
        <KeyWord name="GetRenderHeight" func="yes">
            <Overload retVal="int" descr="Get current render height (it considers HiDPI)"></Overload>
        </KeyWord>
        <KeyWord name="GetMonitorCount" func="yes">
            <Overload retVal="int" descr="Get number of connected monitors"></Overload>
        </KeyWord>
        <KeyWord name="GetCurrentMonitor" func="yes">
            <Overload retVal="int" descr="Get current connected monitor"></Overload>
        </KeyWord>
        <KeyWord name="GetMonitorPosition" func="yes">
            <Overload retVal="Vector2" descr="Get specified monitor position">
                <Param name="int monitor" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetMonitorWidth" func="yes">
            <Overload retVal="int" descr="Get specified monitor width (current video mode used by monitor)">
                <Param name="int monitor" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetMonitorHeight" func="yes">
            <Overload retVal="int" descr="Get specified monitor height (current video mode used by monitor)">
                <Param name="int monitor" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetMonitorPhysicalWidth" func="yes">
            <Overload retVal="int" descr="Get specified monitor physical width in millimetres">
                <Param name="int monitor" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetMonitorPhysicalHeight" func="yes">
            <Overload retVal="int" descr="Get specified monitor physical height in millimetres">
                <Param name="int monitor" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetMonitorRefreshRate" func="yes">
            <Overload retVal="int" descr="Get specified monitor refresh rate">
                <Param name="int monitor" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetWindowPosition" func="yes">
            <Overload retVal="Vector2" descr="Get window position XY on monitor"></Overload>
        </KeyWord>
        <KeyWord name="GetWindowScaleDPI" func="yes">
            <Overload retVal="Vector2" descr="Get window scale DPI factor"></Overload>
        </KeyWord>
        <KeyWord name="GetMonitorName" func="yes">
            <Overload retVal="const char" descr="Get the human-readable, UTF-8 encoded name of the primary monitor">
                <Param name="int monitor" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetClipboardText" func="yes">
            <Overload retVal="void" descr="Set clipboard text content">
                <Param name="const char *text" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetClipboardText" func="yes">
            <Overload retVal="const char" descr="Get clipboard text content"></Overload>
        </KeyWord>
        <KeyWord name="EnableEventWaiting" func="yes">
            <Overload retVal="void" descr="Enable waiting for events on EndDrawing(), no automatic event polling"></Overload>
        </KeyWord>
        <KeyWord name="DisableEventWaiting" func="yes">
            <Overload retVal="void" descr="Disable waiting for events on EndDrawing(), automatic events polling"></Overload>
        </KeyWord>

        <!-- Custom frame control functions -->
        <!-- NOTE: Those functions are intended for advance users that want full control over the frame processing -->
        <!-- By default EndDrawing() does this job: draws everything + SwapScreenBuffer() + manage frame timing + PollInputEvents() -->
        <!-- To avoid that behaviour and control frame processes manually, enable in config.h: SUPPORT_CUSTOM_FRAME_CONTROL -->
        <KeyWord name="SwapScreenBuffer" func="yes">
            <Overload retVal="void" descr="Swap back buffer with front buffer (screen drawing)"></Overload>
        </KeyWord>
        <KeyWord name="PollInputEvents" func="yes">
            <Overload retVal="void" descr="Register all input events"></Overload>
        </KeyWord>
        <KeyWord name="WaitTime" func="yes">
            <Overload retVal="void" descr="Wait for some time (halt program execution)">
                <Param name="double seconds" />
            </Overload>
        </KeyWord>

        <!-- Cursor-related functions -->
        <KeyWord name="ShowCursor" func="yes">
            <Overload retVal="void" descr="Shows cursor"></Overload>
        </KeyWord>
        <KeyWord name="HideCursor" func="yes">
            <Overload retVal="void" descr="Hides cursor"></Overload>
        </KeyWord>
        <KeyWord name="IsCursorHidden" func="yes">
            <Overload retVal="bool" descr="Check if cursor is not visible"></Overload>
        </KeyWord>
        <KeyWord name="EnableCursor" func="yes">
            <Overload retVal="void" descr="Enables cursor (unlock cursor)"></Overload>
        </KeyWord>
        <KeyWord name="DisableCursor" func="yes">
            <Overload retVal="void" descr="Disables cursor (lock cursor)"></Overload>
        </KeyWord>
        <KeyWord name="IsCursorOnScreen" func="yes">
            <Overload retVal="bool" descr="Check if cursor is on the screen"></Overload>
        </KeyWord>

        <!-- Drawing-related functions -->
        <KeyWord name="ClearBackground" func="yes">
            <Overload retVal="void" descr="Set background color (framebuffer clear color)">
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="BeginDrawing" func="yes">
            <Overload retVal="void" descr="Setup canvas (framebuffer) to start drawing"></Overload>
        </KeyWord>
        <KeyWord name="EndDrawing" func="yes">
            <Overload retVal="void" descr="End canvas drawing and swap buffers (double buffering)"></Overload>
        </KeyWord>
        <KeyWord name="BeginMode2D" func="yes">
            <Overload retVal="void" descr="Begin 2D mode with custom camera (2D)">
                <Param name="Camera2D camera" />
            </Overload>
        </KeyWord>
        <KeyWord name="EndMode2D" func="yes">
            <Overload retVal="void" descr="Ends 2D mode with custom camera"></Overload>
        </KeyWord>
        <KeyWord name="BeginMode3D" func="yes">
            <Overload retVal="void" descr="Begin 3D mode with custom camera (3D)">
                <Param name="Camera3D camera" />
            </Overload>
        </KeyWord>
        <KeyWord name="EndMode3D" func="yes">
            <Overload retVal="void" descr="Ends 3D mode and returns to default 2D orthographic mode"></Overload>
        </KeyWord>
        <KeyWord name="BeginTextureMode" func="yes">
            <Overload retVal="void" descr="Begin drawing to render texture">
                <Param name="RenderTexture2D target" />
            </Overload>
        </KeyWord>
        <KeyWord name="EndTextureMode" func="yes">
            <Overload retVal="void" descr="Ends drawing to render texture"></Overload>
        </KeyWord>
        <KeyWord name="BeginShaderMode" func="yes">
            <Overload retVal="void" descr="Begin custom shader drawing">
                <Param name="Shader shader" />
            </Overload>
        </KeyWord>
        <KeyWord name="EndShaderMode" func="yes">
            <Overload retVal="void" descr="End custom shader drawing (use default shader)"></Overload>
        </KeyWord>
        <KeyWord name="BeginBlendMode" func="yes">
            <Overload retVal="void" descr="Begin blending mode (alpha, additive, multiplied, subtract, custom)">
                <Param name="int mode" />
            </Overload>
        </KeyWord>
        <KeyWord name="EndBlendMode" func="yes">
            <Overload retVal="void" descr="End blending mode (reset to default: alpha blending)"></Overload>
        </KeyWord>
        <KeyWord name="BeginScissorMode" func="yes">
            <Overload retVal="void" descr="Begin scissor mode (define screen area for following drawing)">
                <Param name="int x" />
                <Param name="int y" />
                <Param name="int width" />
                <Param name="int height" />
            </Overload>
        </KeyWord>
        <KeyWord name="EndScissorMode" func="yes">
            <Overload retVal="void" descr="End scissor mode"></Overload>
        </KeyWord>
        <KeyWord name="BeginVrStereoMode" func="yes">
            <Overload retVal="void" descr="Begin stereo rendering (requires VR simulator)">
                <Param name="VrStereoConfig config" />
            </Overload>
        </KeyWord>
        <KeyWord name="EndVrStereoMode" func="yes">
            <Overload retVal="void" descr="End stereo rendering (requires VR simulator)"></Overload>
        </KeyWord>

        <!-- VR stereo config functions for VR simulator -->
        <KeyWord name="LoadVrStereoConfig" func="yes">
            <Overload retVal="VrStereoConfig" descr="Load VR stereo config for VR simulator device parameters">
                <Param name="VrDeviceInfo device" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadVrStereoConfig" func="yes">
            <Overload retVal="void" descr="Unload VR stereo config">
                <Param name="VrStereoConfig config" />
            </Overload>
        </KeyWord>

        <!-- Shader management functions -->
        <!-- NOTE: Shader functionality is not available on OpenGL 1.1 -->
        <KeyWord name="LoadShader" func="yes">
            <Overload retVal="Shader" descr="Load shader from files and bind default locations">
                <Param name="const char *vsFileName" />
                <Param name="const char *fsFileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadShaderFromMemory" func="yes">
            <Overload retVal="Shader" descr="Load shader from code strings and bind default locations">
                <Param name="const char *vsCode" />
                <Param name="const char *fsCode" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsShaderReady" func="yes">
            <Overload retVal="bool" descr="Check if a shader is ready">
                <Param name="Shader shader" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetShaderLocation" func="yes">
            <Overload retVal="int" descr="Get shader uniform location">
                <Param name="Shader shader" />
                <Param name="const char *uniformName" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetShaderLocationAttrib" func="yes">
            <Overload retVal="int" descr="Get shader attribute location">
                <Param name="Shader shader" />
                <Param name="const char *attribName" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetShaderValue" func="yes">
            <Overload retVal="void" descr="Set shader uniform value">
                <Param name="Shader shader" />
                <Param name="int locIndex" />
                <Param name="const void *value" />
                <Param name="int uniformType" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetShaderValueV" func="yes">
            <Overload retVal="void" descr="Set shader uniform value vector">
                <Param name="Shader shader" />
                <Param name="int locIndex" />
                <Param name="const void *value" />
                <Param name="int uniformType" />
                <Param name="int count" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetShaderValueMatrix" func="yes">
            <Overload retVal="void" descr="Set shader uniform value (matrix 4x4)">
                <Param name="Shader shader" />
                <Param name="int locIndex" />
                <Param name="Matrix mat" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetShaderValueTexture" func="yes">
            <Overload retVal="void" descr="Set shader uniform value for texture (sampler2d)">
                <Param name="Shader shader" />
                <Param name="int locIndex" />
                <Param name="Texture2D texture" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadShader" func="yes">
            <Overload retVal="void" descr="Unload shader from GPU memory (VRAM)">
                <Param name="Shader shader" />
            </Overload>
        </KeyWord>

        <!-- Screen-space-related functions -->
        <KeyWord name="GetMouseRay" func="yes">
            <Overload retVal="Ray" descr="Get a ray trace from mouse position">
                <Param name="Vector2 mousePosition" />
                <Param name="Camera camera" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetCameraMatrix" func="yes">
            <Overload retVal="Matrix" descr="Get camera transform matrix (view matrix)">
                <Param name="Camera camera" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetCameraMatrix2D" func="yes">
            <Overload retVal="Matrix" descr="Get camera 2d transform matrix">
                <Param name="Camera2D camera" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetWorldToScreen" func="yes">
            <Overload retVal="Vector2" descr="Get the screen space position for a 3d world space position">
                <Param name="Vector3 position" />
                <Param name="Camera camera" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetScreenToWorld2D" func="yes">
            <Overload retVal="Vector2" descr="Get the world space position for a 2d camera screen space position">
                <Param name="Vector2 position" />
                <Param name="Camera2D camera" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetWorldToScreenEx" func="yes">
            <Overload retVal="Vector2" descr="Get size position for a 3d world space position">
                <Param name="Vector3 position" />
                <Param name="Camera camera" />
                <Param name="int width" />
                <Param name="int height" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetWorldToScreen2D" func="yes">
            <Overload retVal="Vector2" descr="Get the screen space position for a 2d camera world space position">
                <Param name="Vector2 position" />
                <Param name="Camera2D camera" />
            </Overload>
        </KeyWord>

        <!-- Timing-related functions -->
        <KeyWord name="SetTargetFPS" func="yes">
            <Overload retVal="void" descr="Set target FPS (maximum)">
                <Param name="int fps" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetFPS" func="yes">
            <Overload retVal="int" descr="Get current FPS"></Overload>
        </KeyWord>
        <KeyWord name="GetFrameTime" func="yes">
            <Overload retVal="float" descr="Get time in seconds for last frame drawn (delta time)"></Overload>
        </KeyWord>
        <KeyWord name="GetTime" func="yes">
            <Overload retVal="double" descr="Get elapsed time in seconds since InitWindow()"></Overload>
        </KeyWord>

        <!-- Misc. functions -->
        <KeyWord name="GetRandomValue" func="yes">
            <Overload retVal="int" descr="Get a random value between min and max (both included)">
                <Param name="int min" />
                <Param name="int max" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetRandomSeed" func="yes">
            <Overload retVal="void" descr="Set the seed for the random number generator">
                <Param name="unsigned int seed" />
            </Overload>
        </KeyWord>
        <KeyWord name="TakeScreenshot" func="yes">
            <Overload retVal="void" descr="Takes a screenshot of current screen (filename extension defines format)">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetConfigFlags" func="yes">
            <Overload retVal="void" descr="Setup init configuration flags (view FLAGS)">
                <Param name="unsigned int flags" />
            </Overload>
        </KeyWord>

        <KeyWord name="TraceLog" func="yes">
            <Overload retVal="void" descr="Show trace log messages (LOG_DEBUG, LOG_INFO, LOG_WARNING, LOG_ERROR...)">
                <Param name="int logLevel" />
                <Param name="const char *text" />
                <Param name="..." />
            </Overload>
        </KeyWord>
        <KeyWord name="SetTraceLogLevel" func="yes">
            <Overload retVal="void" descr="Set the current threshold (minimum) log level">
                <Param name="int logLevel" />
            </Overload>
        </KeyWord>
        <KeyWord name="MemAlloc" func="yes">
            <Overload retVal="void" descr="Internal memory allocator">
                <Param name="unsigned int size" />
            </Overload>
        </KeyWord>
        <KeyWord name="MemRealloc" func="yes">
            <Overload retVal="void" descr="Internal memory reallocator"></Overload>
        </KeyWord>
        <KeyWord name="MemFree" func="yes">
            <Overload retVal="void" descr="Internal memory free"></Overload>
        </KeyWord>

        <KeyWord name="OpenURL" func="yes">
            <Overload retVal="void" descr="Open URL with default system browser (if available)">
                <Param name="const char *url" />
            </Overload>
        </KeyWord>

        <!-- Set custom callbacks -->
        <!-- WARNING: Callbacks setup is intended for advance users -->
        <KeyWord name="SetTraceLogCallback" func="yes">
            <Overload retVal="void" descr="Set custom trace log">
                <Param name="TraceLogCallback callback" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetLoadFileDataCallback" func="yes">
            <Overload retVal="void" descr="Set custom file binary data loader">
                <Param name="LoadFileDataCallback callback" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetSaveFileDataCallback" func="yes">
            <Overload retVal="void" descr="Set custom file binary data saver">
                <Param name="SaveFileDataCallback callback" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetLoadFileTextCallback" func="yes">
            <Overload retVal="void" descr="Set custom file text data loader">
                <Param name="LoadFileTextCallback callback" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetSaveFileTextCallback" func="yes">
            <Overload retVal="void" descr="Set custom file text data saver">
                <Param name="SaveFileTextCallback callback" />
            </Overload>
        </KeyWord>

        <!-- Files management functions -->
        <KeyWord name="char *LoadFileData" func="yes">
            <Overload retVal="unsigned" descr="Load file data as byte array (read)">
                <Param name="const char *fileName" />
                <Param name="unsigned int *bytesRead" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadFileData" func="yes">
            <Overload retVal="void" descr="Unload file data allocated by LoadFileData()">
                <Param name="unsigned char *data" />
            </Overload>
        </KeyWord>
        <KeyWord name="SaveFileData" func="yes">
            <Overload retVal="bool" descr="Save data to file from byte array (write), returns true on success">
                <Param name="const char *fileName" /></Overload>
        </KeyWord>
        <KeyWord name="ExportDataAsCode" func="yes">
            <Overload retVal="bool" descr="Export data to code (.h), returns true on success">
                <Param name="const unsigned char" />
                <Param name="unsigned int size" />
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadFileText" func="yes">
            <Overload retVal="char" descr="Load text data from file (read), returns a '\0' terminated string">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadFileText" func="yes">
            <Overload retVal="void" descr="Unload file text data allocated by LoadFileText()">
                <Param name="char *text" />
            </Overload>
        </KeyWord>
        <KeyWord name="SaveFileText" func="yes">
            <Overload retVal="bool" descr="Save text data to file (write), string must be '\0' terminated, returns true on success">
                <Param name="const char *fileName" />
                <Param name="char *text" />
            </Overload>
        </KeyWord>
        <KeyWord name="FileExists" func="yes">
            <Overload retVal="bool" descr="Check if file exists">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="DirectoryExists" func="yes">
            <Overload retVal="bool" descr="Check if a directory path exists">
                <Param name="const char *dirPath" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsFileExtension" func="yes">
            <Overload retVal="bool" descr="Check file extension (including point: .png, .wav)">
                <Param name="const char *fileName" />
                <Param name="const char *ext" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetFileLength" func="yes">
            <Overload retVal="int" descr="Get file length in bytes (NOTE: GetFileSize() conflicts with windows.h)">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetFileExtension" func="yes">
            <Overload retVal="const char" descr="Get pointer to extension for a filename string (includes dot: '.png')">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetFileName" func="yes">
            <Overload retVal="const char" descr="Get pointer to filename for a path string">
                <Param name="const char *filePath" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetFileNameWithoutExt" func="yes">
            <Overload retVal="const char" descr="Get filename string without extension (uses static string)">
                <Param name="const char *filePath" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetDirectoryPath" func="yes">
            <Overload retVal="const char" descr="Get full path for a given fileName with path (uses static string)">
                <Param name="const char *filePath" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetPrevDirectoryPath" func="yes">
            <Overload retVal="const char" descr="Get previous directory path for a given path (uses static string)">
                <Param name="const char *dirPath" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetWorkingDirectory" func="yes">
            <Overload retVal="const char" descr="Get current working directory (uses static string)"></Overload>
        </KeyWord>
        <KeyWord name="GetApplicationDirectory" func="yes">
            <Overload retVal="const char" descr="Get the directory if the running application (uses static string)"></Overload>
        </KeyWord>
        <KeyWord name="ChangeDirectory" func="yes">
            <Overload retVal="bool" descr="Change working directory, return true on success">
                <Param name="const char *dir" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsPathFile" func="yes">
            <Overload retVal="bool" descr="Check if a given path is a file or a directory">
                <Param name="const char *path" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadDirectoryFiles" func="yes">
            <Overload retVal="FilePathList" descr="Load directory filepaths">
                <Param name="const char *dirPath" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadDirectoryFilesEx" func="yes">
            <Overload retVal="FilePathList" descr="Load directory filepaths with extension filtering and recursive directory scan">
                <Param name="const char *basePath" />
                <Param name="const char *filter" />
                <Param name="bool scanSubdirs" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadDirectoryFiles" func="yes">
            <Overload retVal="void" descr="Unload filepaths">
                <Param name="FilePathList files" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsFileDropped" func="yes">
            <Overload retVal="bool" descr="Check if a file has been dropped into window"></Overload>
        </KeyWord>
        <KeyWord name="LoadDroppedFiles" func="yes">
            <Overload retVal="FilePathList" descr="Load dropped filepaths"></Overload>
        </KeyWord>
        <KeyWord name="UnloadDroppedFiles" func="yes">
            <Overload retVal="void" descr="Unload dropped filepaths">
                <Param name="FilePathList files" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetFileModTime" func="yes">
            <Overload retVal="long" descr="Get file modification time (last write time)">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>

        <!-- Compression/Encoding functionality -->
        <KeyWord name="char *CompressData" func="yes">
            <Overload retVal="unsigned" descr="Compress data (DEFLATE algorithm), memory must be MemFree()">
                <Param name="const unsigned char" />
                <Param name="int dataSize" />
                <Param name="int *compDataSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="char *DecompressData" func="yes">
            <Overload retVal="unsigned" descr="Decompress data (DEFLATE algorithm), memory must be MemFree()">
                <Param name="const unsigned char" />
                <Param name="int compDataSize" />
                <Param name="int *dataSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="EncodeDataBase64" func="yes">
            <Overload retVal="char" descr="Encode data to Base64 string, memory must be MemFree()">
                <Param name="const unsigned char" />
                <Param name="int dataSize" />
                <Param name="int *outputSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="char *DecodeDataBase64" func="yes">
            <Overload retVal="unsigned" descr="Decode Base64 string data, memory must be MemFree()">
                <Param name="const unsigned char" />
                <Param name="int *outputSize" />
            </Overload>
        </KeyWord>

        <!-------------------------------------------------------------------------------------- -->
        <!-- Input Handling Functions (Module: core) -->
        <!-------------------------------------------------------------------------------------- -->

        <!-- Input-related functions: keyboard -->
        <KeyWord name="IsKeyPressed" func="yes">
            <Overload retVal="bool" descr="Check if a key has been pressed once">
                <Param name="int key" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsKeyDown" func="yes">
            <Overload retVal="bool" descr="Check if a key is being pressed">
                <Param name="int key" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsKeyReleased" func="yes">
            <Overload retVal="bool" descr="Check if a key has been released once">
                <Param name="int key" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsKeyUp" func="yes">
            <Overload retVal="bool" descr="Check if a key is NOT being pressed">
                <Param name="int key" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetExitKey" func="yes">
            <Overload retVal="void" descr="Set a custom key to exit program (default is ESC)">
                <Param name="int key" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetKeyPressed" func="yes">
            <Overload retVal="int" descr="Get key pressed (keycode), call it multiple times for keys queued, returns 0 when the queue is empty"></Overload>
        </KeyWord>
        <KeyWord name="GetCharPressed" func="yes">
            <Overload retVal="int" descr="Get char pressed (unicode), call it multiple times for chars queued, returns 0 when the queue is empty"></Overload>
        </KeyWord>

        <!-- Input-related functions: gamepads -->
        <KeyWord name="IsGamepadAvailable" func="yes">
            <Overload retVal="bool" descr="Check if a gamepad is available">
                <Param name="int gamepad" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetGamepadName" func="yes">
            <Overload retVal="const char" descr="Get gamepad internal name id">
                <Param name="int gamepad" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsGamepadButtonPressed" func="yes">
            <Overload retVal="bool" descr="Check if a gamepad button has been pressed once">
                <Param name="int gamepad" />
                <Param name="int button" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsGamepadButtonDown" func="yes">
            <Overload retVal="bool" descr="Check if a gamepad button is being pressed">
                <Param name="int gamepad" />
                <Param name="int button" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsGamepadButtonReleased" func="yes">
            <Overload retVal="bool" descr="Check if a gamepad button has been released once">
                <Param name="int gamepad" />
                <Param name="int button" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsGamepadButtonUp" func="yes">
            <Overload retVal="bool" descr="Check if a gamepad button is NOT being pressed">
                <Param name="int gamepad" />
                <Param name="int button" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetGamepadButtonPressed" func="yes">
            <Overload retVal="int" descr="Get the last gamepad button pressed"></Overload>
        </KeyWord>
        <KeyWord name="GetGamepadAxisCount" func="yes">
            <Overload retVal="int" descr="Get gamepad axis count for a gamepad">
                <Param name="int gamepad" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetGamepadAxisMovement" func="yes">
            <Overload retVal="float" descr="Get axis movement value for a gamepad axis">
                <Param name="int gamepad" />
                <Param name="int axis" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetGamepadMappings" func="yes">
            <Overload retVal="int" descr="Set internal gamepad mappings (SDL_GameControllerDB)">
                <Param name="const char *mappings" />
            </Overload>
        </KeyWord>

        <!-- Input-related functions: mouse -->
        <KeyWord name="IsMouseButtonPressed" func="yes">
            <Overload retVal="bool" descr="Check if a mouse button has been pressed once">
                <Param name="int button" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsMouseButtonDown" func="yes">
            <Overload retVal="bool" descr="Check if a mouse button is being pressed">
                <Param name="int button" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsMouseButtonReleased" func="yes">
            <Overload retVal="bool" descr="Check if a mouse button has been released once">
                <Param name="int button" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsMouseButtonUp" func="yes">
            <Overload retVal="bool" descr="Check if a mouse button is NOT being pressed">
                <Param name="int button" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetMouseX" func="yes">
            <Overload retVal="int" descr="Get mouse position X"></Overload>
        </KeyWord>
        <KeyWord name="GetMouseY" func="yes">
            <Overload retVal="int" descr="Get mouse position Y"></Overload>
        </KeyWord>
        <KeyWord name="GetMousePosition" func="yes">
            <Overload retVal="Vector2" descr="Get mouse position XY"></Overload>
        </KeyWord>
        <KeyWord name="GetMouseDelta" func="yes">
            <Overload retVal="Vector2" descr="Get mouse delta between frames"></Overload>
        </KeyWord>
        <KeyWord name="SetMousePosition" func="yes">
            <Overload retVal="void" descr="Set mouse position XY">
                <Param name="int x" />
                <Param name="int y" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetMouseOffset" func="yes">
            <Overload retVal="void" descr="Set mouse offset">
                <Param name="int offsetX" />
                <Param name="int offsetY" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetMouseScale" func="yes">
            <Overload retVal="void" descr="Set mouse scaling">
                <Param name="float scaleX" />
                <Param name="float scaleY" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetMouseWheelMove" func="yes">
            <Overload retVal="float" descr="Get mouse wheel movement for X or Y, whichever is larger"></Overload>
        </KeyWord>
        <KeyWord name="GetMouseWheelMoveV" func="yes">
            <Overload retVal="Vector2" descr="Get mouse wheel movement for both X and Y"></Overload>
        </KeyWord>
        <KeyWord name="SetMouseCursor" func="yes">
            <Overload retVal="void" descr="Set mouse cursor">
                <Param name="int cursor" />
            </Overload>
        </KeyWord>

        <!-- Input-related functions: touch -->
        <KeyWord name="GetTouchX" func="yes">
            <Overload retVal="int" descr="Get touch position X for touch point 0 (relative to screen size)"></Overload>
        </KeyWord>
        <KeyWord name="GetTouchY" func="yes">
            <Overload retVal="int" descr="Get touch position Y for touch point 0 (relative to screen size)"></Overload>
        </KeyWord>
        <KeyWord name="GetTouchPosition" func="yes">
            <Overload retVal="Vector2" descr="Get touch position XY for a touch point index (relative to screen size)">
                <Param name="int index" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetTouchPointId" func="yes">
            <Overload retVal="int" descr="Get touch point identifier for given index">
                <Param name="int index" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetTouchPointCount" func="yes">
            <Overload retVal="int" descr="Get number of touch points"></Overload>
        </KeyWord>

        <!-------------------------------------------------------------------------------------- -->
        <!-- Gestures and Touch Handling Functions (Module: rgestures) -->
        <!-------------------------------------------------------------------------------------- -->
        <KeyWord name="SetGesturesEnabled" func="yes">
            <Overload retVal="void" descr="Enable a set of gestures using flags">
                <Param name="unsigned int flags" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsGestureDetected" func="yes">
            <Overload retVal="bool" descr="Check if a gesture have been detected">
                <Param name="int gesture" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetGestureDetected" func="yes">
            <Overload retVal="int" descr="Get latest detected gesture"></Overload>
        </KeyWord>
        <KeyWord name="GetGestureHoldDuration" func="yes">
            <Overload retVal="float" descr="Get gesture hold time in milliseconds"></Overload>
        </KeyWord>
        <KeyWord name="GetGestureDragVector" func="yes">
            <Overload retVal="Vector2" descr="Get gesture drag vector"></Overload>
        </KeyWord>
        <KeyWord name="GetGestureDragAngle" func="yes">
            <Overload retVal="float" descr="Get gesture drag angle"></Overload>
        </KeyWord>
        <KeyWord name="GetGesturePinchVector" func="yes">
            <Overload retVal="Vector2" descr="Get gesture pinch delta"></Overload>
        </KeyWord>
        <KeyWord name="GetGesturePinchAngle" func="yes">
            <Overload retVal="float" descr="Get gesture pinch angle"></Overload>
        </KeyWord>

        <!-------------------------------------------------------------------------------------- -->
        <!-- Camera System Functions (Module: rcamera) -->
        <!-------------------------------------------------------------------------------------- -->
        <KeyWord name="UpdateCamera" func="yes">
            <Overload retVal="void" descr="Update camera position for selected mode">
                <Param name="Camera *camera" />
                <Param name="int mode" />
            </Overload>
        </KeyWord>
        <KeyWord name="UpdateCameraPro" func="yes">
            <Overload retVal="void" descr="Update camera movement/rotation">
                <Param name="Camera *camera" />
                <Param name="Vector3 movement" />
                <Param name="Vector3 rotation" />
                <Param name="float zoom" />
            </Overload>
        </KeyWord>

        <!-------------------------------------------------------------------------------------- -->
        <!-- Basic Shapes Drawing Functions (Module: shapes) -->
        <!-------------------------------------------------------------------------------------- -->
        <!-- Set texture and rectangle to be used on shapes drawing -->
        <!-- NOTE: It can be useful when using basic shapes and one single font, -->
        <!-- defining a font char white rectangle would allow drawing everything in a single draw call -->
        <KeyWord name="SetShapesTexture" func="yes">
            <Overload retVal="void" descr="Set texture and rectangle to be used on shapes drawing">
                <Param name="Texture2D texture" />
                <Param name="Rectangle source" />
            </Overload>
        </KeyWord>

        <!-- Basic shapes drawing functions -->
        <KeyWord name="DrawPixel" func="yes">
            <Overload retVal="void" descr="Draw a pixel">
                <Param name="int posX" />
                <Param name="int posY" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawPixelV" func="yes">
            <Overload retVal="void" descr="Draw a pixel (Vector version)">
                <Param name="Vector2 position" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawLine" func="yes">
            <Overload retVal="void" descr="Draw a line">
                <Param name="int startPosX" />
                <Param name="int startPosY" />
                <Param name="int endPosX" />
                <Param name="int endPosY" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawLineV" func="yes">
            <Overload retVal="void" descr="Draw a line (Vector version)">
                <Param name="Vector2 startPos" />
                <Param name="Vector2 endPos" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawLineEx" func="yes">
            <Overload retVal="void" descr="Draw a line defining thickness">
                <Param name="Vector2 startPos" />
                <Param name="Vector2 endPos" />
                <Param name="float thick" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawLineBezier" func="yes">
            <Overload retVal="void" descr="Draw a line using cubic-bezier curves in-out">
                <Param name="Vector2 startPos" />
                <Param name="Vector2 endPos" />
                <Param name="float thick" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawLineBezierQuad" func="yes">
            <Overload retVal="void" descr="Draw line using quadratic bezier curves with a control point">
                <Param name="Vector2 startPos" />
                <Param name="Vector2 endPos" />
                <Param name="Vector2 controlPos" />
                <Param name="float thick" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawLineBezierCubic" func="yes">
            <Overload retVal="void" descr="Draw line using cubic bezier curves with 2 control points">
                <Param name="Vector2 startPos" />
                <Param name="Vector2 endPos" />
                <Param name="Vector2 startControlPos" />
                <Param name="Vector2 endControlPos" />
                <Param name="float thick" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawLineStrip" func="yes">
            <Overload retVal="void" descr="Draw lines sequence">
                <Param name="Vector2 *points" />
                <Param name="int pointCount" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCircle" func="yes">
            <Overload retVal="void" descr="Draw a color-filled circle">
                <Param name="int centerX" />
                <Param name="int centerY" />
                <Param name="float radius" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCircleSector" func="yes">
            <Overload retVal="void" descr="Draw a piece of a circle">
                <Param name="Vector2 center" />
                <Param name="float radius" />
                <Param name="float startAngle" />
                <Param name="float endAngle" />
                <Param name="int segments" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCircleSectorLines" func="yes">
            <Overload retVal="void" descr="Draw circle sector outline">
                <Param name="Vector2 center" />
                <Param name="float radius" />
                <Param name="float startAngle" />
                <Param name="float endAngle" />
                <Param name="int segments" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCircleGradient" func="yes">
            <Overload retVal="void" descr="Draw a gradient-filled circle">
                <Param name="int centerX" />
                <Param name="int centerY" />
                <Param name="float radius" />
                <Param name="Color color1" />
                <Param name="Color color2" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCircleV" func="yes">
            <Overload retVal="void" descr="Draw a color-filled circle (Vector version)">
                <Param name="Vector2 center" />
                <Param name="float radius" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCircleLines" func="yes">
            <Overload retVal="void" descr="Draw circle outline">
                <Param name="int centerX" />
                <Param name="int centerY" />
                <Param name="float radius" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawEllipse" func="yes">
            <Overload retVal="void" descr="Draw ellipse">
                <Param name="int centerX" />
                <Param name="int centerY" />
                <Param name="float radiusH" />
                <Param name="float radiusV" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawEllipseLines" func="yes">
            <Overload retVal="void" descr="Draw ellipse outline">
                <Param name="int centerX" />
                <Param name="int centerY" />
                <Param name="float radiusH" />
                <Param name="float radiusV" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRing" func="yes">
            <Overload retVal="void" descr="Draw ring">
                <Param name="Vector2 center" />
                <Param name="float innerRadius" />
                <Param name="float outerRadius" />
                <Param name="float startAngle" />
                <Param name="float endAngle" />
                <Param name="int segments" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRingLines" func="yes">
            <Overload retVal="void" descr="Draw ring outline">
                <Param name="Vector2 center" />
                <Param name="float innerRadius" />
                <Param name="float outerRadius" />
                <Param name="float startAngle" />
                <Param name="float endAngle" />
                <Param name="int segments" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectangle" func="yes">
            <Overload retVal="void" descr="Draw a color-filled rectangle">
                <Param name="int posX" />
                <Param name="int posY" />
                <Param name="int width" />
                <Param name="int height" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectangleV" func="yes">
            <Overload retVal="void" descr="Draw a color-filled rectangle (Vector version)">
                <Param name="Vector2 position" />
                <Param name="Vector2 size" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectangleRec" func="yes">
            <Overload retVal="void" descr="Draw a color-filled rectangle">
                <Param name="Rectangle rec" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectanglePro" func="yes">
            <Overload retVal="void" descr="Draw a color-filled rectangle with pro parameters">
                <Param name="Rectangle rec" />
                <Param name="Vector2 origin" />
                <Param name="float rotation" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectangleGradientV" func="yes">
            <Overload retVal="void" descr="Draw a vertical-gradient-filled rectangle">
                <Param name="int posX" />
                <Param name="int posY" />
                <Param name="int width" />
                <Param name="int height" />
                <Param name="Color color1" />
                <Param name="Color color2" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectangleGradientH" func="yes">
            <Overload retVal="void" descr="Draw a horizontal-gradient-filled rectangle">
                <Param name="int posX" />
                <Param name="int posY" />
                <Param name="int width" />
                <Param name="int height" />
                <Param name="Color color1" />
                <Param name="Color color2" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectangleGradientEx" func="yes">
            <Overload retVal="void" descr="Draw a gradient-filled rectangle with custom vertex colors">
                <Param name="Rectangle rec" />
                <Param name="Color col1" />
                <Param name="Color col2" />
                <Param name="Color col3" />
                <Param name="Color col4" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectangleLines" func="yes">
            <Overload retVal="void" descr="Draw rectangle outline">
                <Param name="int posX" />
                <Param name="int posY" />
                <Param name="int width" />
                <Param name="int height" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectangleLinesEx" func="yes">
            <Overload retVal="void" descr="Draw rectangle outline with extended parameters">
                <Param name="Rectangle rec" />
                <Param name="float lineThick" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectangleRounded" func="yes">
            <Overload retVal="void" descr="Draw rectangle with rounded edges">
                <Param name="Rectangle rec" />
                <Param name="float roundness" />
                <Param name="int segments" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRectangleRoundedLines" func="yes">
            <Overload retVal="void" descr="Draw rectangle with rounded edges outline">
                <Param name="Rectangle rec" />
                <Param name="float roundness" />
                <Param name="int segments" />
                <Param name="float lineThick" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTriangle" func="yes">
            <Overload retVal="void" descr="Draw a color-filled triangle (vertex in counter-clockwise order!)">
                <Param name="Vector2 v1" />
                <Param name="Vector2 v2" />
                <Param name="Vector2 v3" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTriangleLines" func="yes">
            <Overload retVal="void" descr="Draw triangle outline (vertex in counter-clockwise order!)">
                <Param name="Vector2 v1" />
                <Param name="Vector2 v2" />
                <Param name="Vector2 v3" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTriangleFan" func="yes">
            <Overload retVal="void" descr="Draw a triangle fan defined by points (first vertex is the center)">
                <Param name="Vector2 *points" />
                <Param name="int pointCount" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTriangleStrip" func="yes">
            <Overload retVal="void" descr="Draw a triangle strip defined by points">
                <Param name="Vector2 *points" />
                <Param name="int pointCount" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawPoly" func="yes">
            <Overload retVal="void" descr="Draw a regular polygon (Vector version)">
                <Param name="Vector2 center" />
                <Param name="int sides" />
                <Param name="float radius" />
                <Param name="float rotation" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawPolyLines" func="yes">
            <Overload retVal="void" descr="Draw a polygon outline of n sides">
                <Param name="Vector2 center" />
                <Param name="int sides" />
                <Param name="float radius" />
                <Param name="float rotation" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawPolyLinesEx" func="yes">
            <Overload retVal="void" descr="Draw a polygon outline of n sides with extended parameters">
                <Param name="Vector2 center" />
                <Param name="int sides" />
                <Param name="float radius" />
                <Param name="float rotation" />
                <Param name="float lineThick" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>

        <!-- Basic shapes collision detection functions -->
        <KeyWord name="CheckCollisionRecs" func="yes">
            <Overload retVal="bool" descr="Check collision between two rectangles">
                <Param name="Rectangle rec1" />
                <Param name="Rectangle rec2" />
            </Overload>
        </KeyWord>
        <KeyWord name="CheckCollisionCircles" func="yes">
            <Overload retVal="bool" descr="Check collision between two circles">
                <Param name="Vector2 center1" />
                <Param name="float radius1" />
                <Param name="Vector2 center2" />
                <Param name="float radius2" />
            </Overload>
        </KeyWord>
        <KeyWord name="CheckCollisionCircleRec" func="yes">
            <Overload retVal="bool" descr="Check collision between circle and rectangle">
                <Param name="Vector2 center" />
                <Param name="float radius" />
                <Param name="Rectangle rec" />
            </Overload>
        </KeyWord>
        <KeyWord name="CheckCollisionPointRec" func="yes">
            <Overload retVal="bool" descr="Check if point is inside rectangle">
                <Param name="Vector2 point" />
                <Param name="Rectangle rec" />
            </Overload>
        </KeyWord>
        <KeyWord name="CheckCollisionPointCircle" func="yes">
            <Overload retVal="bool" descr="Check if point is inside circle">
                <Param name="Vector2 point" />
                <Param name="Vector2 center" />
                <Param name="float radius" />
            </Overload>
        </KeyWord>
        <KeyWord name="CheckCollisionPointTriangle" func="yes">
            <Overload retVal="bool" descr="Check if point is inside a triangle">
                <Param name="Vector2 point" />
                <Param name="Vector2 p1" />
                <Param name="Vector2 p2" />
                <Param name="Vector2 p3" />
            </Overload>
        </KeyWord>
        <KeyWord name="CheckCollisionPointPoly" func="yes">
            <Overload retVal="bool" descr="Check if point is within a polygon described by array of vertices">
                <Param name="Vector2 point" />
                <Param name="Vector2 *points" />
                <Param name="int pointCount" />
            </Overload>
        </KeyWord>
        <KeyWord name="CheckCollisionLines" func="yes">
            <Overload retVal="bool" descr="Check the collision between two lines defined by two points each, returns collision point by reference">
                <Param name="Vector2 startPos1" />
                <Param name="Vector2 endPos1" />
                <Param name="Vector2 startPos2" />
                <Param name="Vector2 endPos2" />
                <Param name="Vector2 *collisionPoint" />
            </Overload>
        </KeyWord>
        <KeyWord name="CheckCollisionPointLine" func="yes">
            <Overload retVal="bool" descr="Check if point belongs to line created between two points [p1] and [p2] with defined margin in pixels [threshold]">
                <Param name="Vector2 point" />
                <Param name="Vector2 p1" />
                <Param name="Vector2 p2" />
                <Param name="int threshold" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetCollisionRec" func="yes">
            <Overload retVal="Rectangle" descr="Get collision rectangle for two rectangles collision">
                <Param name="Rectangle rec1" />
                <Param name="Rectangle rec2" />
            </Overload>
        </KeyWord>

        <!-------------------------------------------------------------------------------------- -->
        <!-- Texture Loading and Drawing Functions (Module: textures) -->
        <!-------------------------------------------------------------------------------------- -->

        <!-- Image loading functions -->
        <!-- NOTE: These functions do not require GPU access -->
        <KeyWord name="LoadImage" func="yes">
            <Overload retVal="Image" descr="Load image from file into CPU memory (RAM)">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadImageRaw" func="yes">
            <Overload retVal="Image" descr="Load image from RAW file data">
                <Param name="const char *fileName" />
                <Param name="int width" />
                <Param name="int height" />
                <Param name="int format" />
                <Param name="int headerSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadImageAnim" func="yes">
            <Overload retVal="Image" descr="Load image sequence from file (frames appended to image.data)">
                <Param name="const char *fileName" />
                <Param name="int *frames" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadImageFromMemory" func="yes">
            <Overload retVal="Image" descr="Load image from memory buffer, fileType refers to extension: i.e. '.png'">
                <Param name="const char *fileType" />
                <Param name="const unsigned char" />
                <Param name="int dataSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadImageFromTexture" func="yes">
            <Overload retVal="Image" descr="Load image from GPU texture data">
                <Param name="Texture2D texture" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadImageFromScreen" func="yes">
            <Overload retVal="Image" descr="Load image from screen buffer and (screenshot)"></Overload>
        </KeyWord>
        <KeyWord name="IsImageReady" func="yes">
            <Overload retVal="bool" descr="Check if an image is ready">
                <Param name="Image image" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadImage" func="yes">
            <Overload retVal="void" descr="Unload image from CPU memory (RAM)">
                <Param name="Image image" />
            </Overload>
        </KeyWord>
        <KeyWord name="ExportImage" func="yes">
            <Overload retVal="bool" descr="Export image data to file, returns true on success">
                <Param name="Image image" />
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="ExportImageAsCode" func="yes">
            <Overload retVal="bool" descr="Export image as code file defining an array of bytes, returns true on success">
                <Param name="Image image" />
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>

        <!-- Image generation functions -->
        <KeyWord name="GenImageColor" func="yes">
            <Overload retVal="Image" descr="Generate image: plain color">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenImageGradientV" func="yes">
            <Overload retVal="Image" descr="Generate image: vertical gradient">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="Color top" />
                <Param name="Color bottom" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenImageGradientH" func="yes">
            <Overload retVal="Image" descr="Generate image: horizontal gradient">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="Color left" />
                <Param name="Color right" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenImageGradientRadial" func="yes">
            <Overload retVal="Image" descr="Generate image: radial gradient">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="float density" />
                <Param name="Color inner" />
                <Param name="Color outer" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenImageChecked" func="yes">
            <Overload retVal="Image" descr="Generate image: checked">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="int checksX" />
                <Param name="int checksY" />
                <Param name="Color col1" />
                <Param name="Color col2" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenImageWhiteNoise" func="yes">
            <Overload retVal="Image" descr="Generate image: white noise">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="float factor" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenImagePerlinNoise" func="yes">
            <Overload retVal="Image" descr="Generate image: perlin noise">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="int offsetX" />
                <Param name="int offsetY" />
                <Param name="float scale" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenImageCellular" func="yes">
            <Overload retVal="Image" descr="Generate image: cellular algorithm, bigger tileSize means bigger cells">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="int tileSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenImageText" func="yes">
            <Overload retVal="Image" descr="Generate image: grayscale image from text data">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="const char *text" />
            </Overload>
        </KeyWord>

        <!-- Image manipulation functions -->
        <KeyWord name="ImageCopy" func="yes">
            <Overload retVal="Image" descr="Create an image duplicate (useful for transformations)">
                <Param name="Image image" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageFromImage" func="yes">
            <Overload retVal="Image" descr="Create an image from another image piece">
                <Param name="Image image" />
                <Param name="Rectangle rec" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageText" func="yes">
            <Overload retVal="Image" descr="Create an image from text (default font)">
                <Param name="const char *text" />
                <Param name="int fontSize" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageTextEx" func="yes">
            <Overload retVal="Image" descr="Create an image from text (custom sprite font)">
                <Param name="Font font" />
                <Param name="const char *text" />
                <Param name="float fontSize" />
                <Param name="float spacing" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageFormat" func="yes">
            <Overload retVal="void" descr="Convert image data to desired format">
                <Param name="Image *image" />
                <Param name="int newFormat" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageToPOT" func="yes">
            <Overload retVal="void" descr="Convert image to POT (power-of-two)">
                <Param name="Image *image" />
                <Param name="Color fill" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageCrop" func="yes">
            <Overload retVal="void" descr="Crop an image to a defined rectangle">
                <Param name="Image *image" />
                <Param name="Rectangle crop" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageAlphaCrop" func="yes">
            <Overload retVal="void" descr="Crop image depending on alpha value">
                <Param name="Image *image" />
                <Param name="float threshold" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageAlphaClear" func="yes">
            <Overload retVal="void" descr="Clear alpha channel to desired color">
                <Param name="Image *image" />
                <Param name="Color color" />
                <Param name="float threshold" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageAlphaMask" func="yes">
            <Overload retVal="void" descr="Apply alpha mask to image">
                <Param name="Image *image" />
                <Param name="Image alphaMask" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageAlphaPremultiply" func="yes">
            <Overload retVal="void" descr="Premultiply alpha channel">
                <Param name="Image *image" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageBlurGaussian" func="yes">
            <Overload retVal="void" descr="Apply Gaussian blur using a box blur approximation">
                <Param name="Image *image" />
                <Param name="int blurSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageResize" func="yes">
            <Overload retVal="void" descr="Resize image (Bicubic scaling algorithm)">
                <Param name="Image *image" />
                <Param name="int newWidth" />
                <Param name="int newHeight" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageResizeNN" func="yes">
            <Overload retVal="void" descr="Resize image (Nearest-Neighbor scaling algorithm)">
                <Param name="Image *image" />
                <Param name="int newWidth" />
                <Param name="int newHeight" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageResizeCanvas" func="yes">
            <Overload retVal="void" descr="Resize canvas and fill with color">
                <Param name="Image *image" />
                <Param name="int newWidth" />
                <Param name="int newHeight" />
                <Param name="int offsetX" />
                <Param name="int offsetY" />
                <Param name="Color fill" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageMipmaps" func="yes">
            <Overload retVal="void" descr="Compute all mipmap levels for a provided image">
                <Param name="Image *image" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDither" func="yes">
            <Overload retVal="void" descr="Dither image data to 16bpp or lower (Floyd-Steinberg dithering)">
                <Param name="Image *image" />
                <Param name="int rBpp" />
                <Param name="int gBpp" />
                <Param name="int bBpp" />
                <Param name="int aBpp" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageFlipVertical" func="yes">
            <Overload retVal="void" descr="Flip image vertically">
                <Param name="Image *image" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageFlipHorizontal" func="yes">
            <Overload retVal="void" descr="Flip image horizontally">
                <Param name="Image *image" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageRotateCW" func="yes">
            <Overload retVal="void" descr="Rotate image clockwise 90deg">
                <Param name="Image *image" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageRotateCCW" func="yes">
            <Overload retVal="void" descr="Rotate image counter-clockwise 90deg">
                <Param name="Image *image" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageColorTint" func="yes">
            <Overload retVal="void" descr="Modify image color: tint">
                <Param name="Image *image" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageColorInvert" func="yes">
            <Overload retVal="void" descr="Modify image color: invert">
                <Param name="Image *image" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageColorGrayscale" func="yes">
            <Overload retVal="void" descr="Modify image color: grayscale">
                <Param name="Image *image" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageColorContrast" func="yes">
            <Overload retVal="void" descr="Modify image color: contrast (-100 to 100)">
                <Param name="Image *image" />
                <Param name="float contrast" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageColorBrightness" func="yes">
            <Overload retVal="void" descr="Modify image color: brightness (-255 to 255)">
                <Param name="Image *image" />
                <Param name="int brightness" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageColorReplace" func="yes">
            <Overload retVal="void" descr="Modify image color: replace color">
                <Param name="Image *image" />
                <Param name="Color color" />
                <Param name="Color replace" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadImageColors" func="yes">
            <Overload retVal="Color" descr="Load color data from image as a Color array (RGBA - 32bit)">
                <Param name="Image image" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadImagePalette" func="yes">
            <Overload retVal="Color" descr="Load colors palette from image as a Color array (RGBA - 32bit)">
                <Param name="Image image" />
                <Param name="int maxPaletteSize" />
                <Param name="int *colorCount" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadImageColors" func="yes">
            <Overload retVal="void" descr="Unload color data loaded with LoadImageColors()">
                <Param name="Color *colors" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadImagePalette" func="yes">
            <Overload retVal="void" descr="Unload colors palette loaded with LoadImagePalette()">
                <Param name="Color *colors" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetImageAlphaBorder" func="yes">
            <Overload retVal="Rectangle" descr="Get image alpha border rectangle">
                <Param name="Image image" />
                <Param name="float threshold" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetImageColor" func="yes">
            <Overload retVal="Color" descr="Get image pixel color at (x, y) position">
                <Param name="Image image" />
                <Param name="int x" />
                <Param name="int y" />
            </Overload>
        </KeyWord>

        <!-- Image drawing functions -->
        <!-- NOTE: Image software-rendering functions (CPU) -->
        <KeyWord name="ImageClearBackground" func="yes">
            <Overload retVal="void" descr="Clear image background with given color">
                <Param name="Image *dst" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawPixel" func="yes">
            <Overload retVal="void" descr="Draw pixel within an image">
                <Param name="Image *dst" />
                <Param name="int posX" />
                <Param name="int posY" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawPixelV" func="yes">
            <Overload retVal="void" descr="Draw pixel within an image (Vector version)">
                <Param name="Image *dst" />
                <Param name="Vector2 position" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawLine" func="yes">
            <Overload retVal="void" descr="Draw line within an image">
                <Param name="Image *dst" />
                <Param name="int startPosX" />
                <Param name="int startPosY" />
                <Param name="int endPosX" />
                <Param name="int endPosY" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawLineV" func="yes">
            <Overload retVal="void" descr="Draw line within an image (Vector version)">
                <Param name="Image *dst" />
                <Param name="Vector2 start" />
                <Param name="Vector2 end" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawCircle" func="yes">
            <Overload retVal="void" descr="Draw a filled circle within an image">
                <Param name="Image *dst" />
                <Param name="int centerX" />
                <Param name="int centerY" />
                <Param name="int radius" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawCircleV" func="yes">
            <Overload retVal="void" descr="Draw a filled circle within an image (Vector version)">
                <Param name="Image *dst" />
                <Param name="Vector2 center" />
                <Param name="int radius" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawCircleLines" func="yes">
            <Overload retVal="void" descr="Draw circle outline within an image">
                <Param name="Image *dst" />
                <Param name="int centerX" />
                <Param name="int centerY" />
                <Param name="int radius" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawCircleLinesV" func="yes">
            <Overload retVal="void" descr="Draw circle outline within an image (Vector version)">
                <Param name="Image *dst" />
                <Param name="Vector2 center" />
                <Param name="int radius" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawRectangle" func="yes">
            <Overload retVal="void" descr="Draw rectangle within an image">
                <Param name="Image *dst" />
                <Param name="int posX" />
                <Param name="int posY" />
                <Param name="int width" />
                <Param name="int height" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawRectangleV" func="yes">
            <Overload retVal="void" descr="Draw rectangle within an image (Vector version)">
                <Param name="Image *dst" />
                <Param name="Vector2 position" />
                <Param name="Vector2 size" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawRectangleRec" func="yes">
            <Overload retVal="void" descr="Draw rectangle within an image">
                <Param name="Image *dst" />
                <Param name="Rectangle rec" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawRectangleLines" func="yes">
            <Overload retVal="void" descr="Draw rectangle lines within an image">
                <Param name="Image *dst" />
                <Param name="Rectangle rec" />
                <Param name="int thick" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDraw" func="yes">
            <Overload retVal="void" descr="Draw a source image within a destination image (tint applied to source)">
                <Param name="Image *dst" />
                <Param name="Image src" />
                <Param name="Rectangle srcRec" />
                <Param name="Rectangle dstRec" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawText" func="yes">
            <Overload retVal="void" descr="Draw text (using default font) within an image (destination)">
                <Param name="Image *dst" />
                <Param name="const char *text" />
                <Param name="int posX" />
                <Param name="int posY" />
                <Param name="int fontSize" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ImageDrawTextEx" func="yes">
            <Overload retVal="void" descr="Draw text (custom sprite font) within an image (destination)">
                <Param name="Image *dst" />
                <Param name="Font font" />
                <Param name="const char *text" />
                <Param name="Vector2 position" />
                <Param name="float fontSize" />
                <Param name="float spacing" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>

        <!-- Texture loading functions -->
        <!-- NOTE: These functions require GPU access -->
        <KeyWord name="LoadTexture" func="yes">
            <Overload retVal="Texture2D" descr="Load texture from file into GPU memory (VRAM)">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadTextureFromImage" func="yes">
            <Overload retVal="Texture2D" descr="Load texture from image data">
                <Param name="Image image" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadTextureCubemap" func="yes">
            <Overload retVal="TextureCubemap" descr="Load cubemap from image, multiple image cubemap layouts supported">
                <Param name="Image image" />
                <Param name="int layout" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadRenderTexture" func="yes">
            <Overload retVal="RenderTexture2D" descr="Load texture for rendering (framebuffer)">
                <Param name="int width" />
                <Param name="int height" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsTextureReady" func="yes">
            <Overload retVal="bool" descr="Check if a texture is ready">
                <Param name="Texture2D texture" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadTexture" func="yes">
            <Overload retVal="void" descr="Unload texture from GPU memory (VRAM)">
                <Param name="Texture2D texture" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsRenderTextureReady" func="yes">
            <Overload retVal="bool" descr="Check if a render texture is ready">
                <Param name="RenderTexture2D target" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadRenderTexture" func="yes">
            <Overload retVal="void" descr="Unload render texture from GPU memory (VRAM)">
                <Param name="RenderTexture2D target" />
            </Overload>
        </KeyWord>
        <KeyWord name="UpdateTexture" func="yes">
            <Overload retVal="void" descr="Update GPU texture with new data">
                <Param name="Texture2D texture" />
                <Param name="const void *pixels" />
            </Overload>
        </KeyWord>
        <KeyWord name="UpdateTextureRec" func="yes">
            <Overload retVal="void" descr="Update GPU texture rectangle with new data">
                <Param name="Texture2D texture" />
                <Param name="Rectangle rec" />
                <Param name="const void *pixels" />
            </Overload>
        </KeyWord>

        <!-- Texture configuration functions -->
        <KeyWord name="GenTextureMipmaps" func="yes">
            <Overload retVal="void" descr="Generate GPU mipmaps for a texture">
                <Param name="Texture2D *texture" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetTextureFilter" func="yes">
            <Overload retVal="void" descr="Set texture scaling filter mode">
                <Param name="Texture2D texture" />
                <Param name="int filter" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetTextureWrap" func="yes">
            <Overload retVal="void" descr="Set texture wrapping mode">
                <Param name="Texture2D texture" />
                <Param name="int wrap" />
            </Overload>
        </KeyWord>

        <!-- Texture drawing functions -->
        <KeyWord name="DrawTexture" func="yes">
            <Overload retVal="void" descr="Draw a Texture2D">
                <Param name="Texture2D texture" />
                <Param name="int posX" />
                <Param name="int posY" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTextureV" func="yes">
            <Overload retVal="void" descr="Draw a Texture2D with position defined as Vector2">
                <Param name="Texture2D texture" />
                <Param name="Vector2 position" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTextureEx" func="yes">
            <Overload retVal="void" descr="Draw a Texture2D with extended parameters">
                <Param name="Texture2D texture" />
                <Param name="Vector2 position" />
                <Param name="float rotation" />
                <Param name="float scale" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTextureRec" func="yes">
            <Overload retVal="void" descr="Draw a part of a texture defined by a rectangle">
                <Param name="Texture2D texture" />
                <Param name="Rectangle source" />
                <Param name="Vector2 position" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTexturePro" func="yes">
            <Overload retVal="void" descr="Draw a part of a texture defined by a rectangle with 'pro' parameters">
                <Param name="Texture2D texture" />
                <Param name="Rectangle source" />
                <Param name="Rectangle dest" />
                <Param name="Vector2 origin" />
                <Param name="float rotation" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTextureNPatch" func="yes">
            <Overload retVal="void" descr="Draws a texture (or part of it) that stretches or shrinks nicely">
                <Param name="Texture2D texture" />
                <Param name="NPatchInfo nPatchInfo" />
                <Param name="Rectangle dest" />
                <Param name="Vector2 origin" />
                <Param name="float rotation" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>

        <!-- Color/pixel related functions -->
        <KeyWord name="Fade" func="yes">
            <Overload retVal="Color" descr="Get color with alpha applied, alpha goes from 0.0f to 1.0f">
                <Param name="Color color" />
                <Param name="float alpha" />
            </Overload>
        </KeyWord>
        <KeyWord name="ColorToInt" func="yes">
            <Overload retVal="int" descr="Get hexadecimal value for a Color">
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ColorNormalize" func="yes">
            <Overload retVal="Vector4" descr="Get Color normalized as float [0..1]">
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ColorFromNormalized" func="yes">
            <Overload retVal="Color" descr="Get Color from normalized values [0..1]">
                <Param name="Vector4 normalized" />
            </Overload>
        </KeyWord>
        <KeyWord name="ColorToHSV" func="yes">
            <Overload retVal="Vector3" descr="Get HSV values for a Color, hue [0..360], saturation/value [0..1]">
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="ColorFromHSV" func="yes">
            <Overload retVal="Color" descr="Get a Color from HSV values, hue [0..360], saturation/value [0..1]">
                <Param name="float hue" />
                <Param name="float saturation" />
                <Param name="float value" />
            </Overload>
        </KeyWord>
        <KeyWord name="ColorTint" func="yes">
            <Overload retVal="Color" descr="Get color multiplied with another color">
                <Param name="Color color" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="ColorBrightness" func="yes">
            <Overload retVal="Color" descr="Get color with brightness correction, brightness factor goes from -1.0f to 1.0f">
                <Param name="Color color" />
                <Param name="float factor" />
            </Overload>
        </KeyWord>
        <KeyWord name="ColorContrast" func="yes">
            <Overload retVal="Color" descr="Get color with contrast correction, contrast values between -1.0f and 1.0f">
                <Param name="Color color" />
                <Param name="float contrast" />
            </Overload>
        </KeyWord>
        <KeyWord name="ColorAlpha" func="yes">
            <Overload retVal="Color" descr="Get color with alpha applied, alpha goes from 0.0f to 1.0f">
                <Param name="Color color" />
                <Param name="float alpha" />
            </Overload>
        </KeyWord>
        <KeyWord name="ColorAlphaBlend" func="yes">
            <Overload retVal="Color" descr="Get src alpha-blended into dst color with tint">
                <Param name="Color dst" />
                <Param name="Color src" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetColor" func="yes">
            <Overload retVal="Color" descr="Get Color structure from hexadecimal value">
                <Param name="unsigned int hexValue" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetPixelColor" func="yes">
            <Overload retVal="Color" descr="Get Color from a source pixel pointer of certain format"></Overload>
        </KeyWord>
        <KeyWord name="SetPixelColor" func="yes">
            <Overload retVal="void" descr="Set color formatted into destination pixel pointer"></Overload>
        </KeyWord>
        <KeyWord name="GetPixelDataSize" func="yes">
            <Overload retVal="int" descr="Get pixel data size in bytes for certain format">
                <Param name="int width" />
                <Param name="int height" />
                <Param name="int format" />
            </Overload>
        </KeyWord>

        <!-------------------------------------------------------------------------------------- -->
        <!-- Font Loading and Text Drawing Functions (Module: text) -->
        <!-------------------------------------------------------------------------------------- -->

        <!-- Font loading/unloading functions -->
        <KeyWord name="GetFontDefault" func="yes">
            <Overload retVal="Font" descr="Get the default Font"></Overload>
        </KeyWord>
        <KeyWord name="LoadFont" func="yes">
            <Overload retVal="Font" descr="Load font from file into GPU memory (VRAM)">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadFontEx" func="yes">
            <Overload retVal="Font" descr="Load font from file with extended parameters, use NULL for fontChars and 0 for glyphCount to load the default character set">
                <Param name="const char *fileName" />
                <Param name="int fontSize" />
                <Param name="int *fontChars" />
                <Param name="int glyphCount" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadFontFromImage" func="yes">
            <Overload retVal="Font" descr="Load font from Image (XNA style)">
                <Param name="Image image" />
                <Param name="Color key" />
                <Param name="int firstChar" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadFontFromMemory" func="yes">
            <Overload retVal="Font" descr="Load font from memory buffer, fileType refers to extension: i.e. '.ttf'">
                <Param name="const char *fileType" />
                <Param name="const unsigned char" />
                <Param name="int dataSize" />
                <Param name="int fontSize" />
                <Param name="int *fontChars" />
                <Param name="int glyphCount" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsFontReady" func="yes">
            <Overload retVal="bool" descr="Check if a font is ready">
                <Param name="Font font" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadFontData" func="yes">
            <Overload retVal="GlyphInfo" descr="Load font data for further use">
                <Param name="const unsigned char" />
                <Param name="int dataSize" />
                <Param name="int fontSize" />
                <Param name="int *fontChars" />
                <Param name="int glyphCount" />
                <Param name="int type" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenImageFontAtlas" func="yes">
            <Overload retVal="Image" descr="Generate image font atlas using chars info">
                <Param name="const GlyphInfo *chars" />
                <Param name="Rectangle **recs" />
                <Param name="int glyphCount" />
                <Param name="int fontSize" />
                <Param name="int padding" />
                <Param name="int packMethod" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadFontData" func="yes">
            <Overload retVal="void" descr="Unload font chars info data (RAM)">
                <Param name="GlyphInfo *chars" />
                <Param name="int glyphCount" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadFont" func="yes">
            <Overload retVal="void" descr="Unload font from GPU memory (VRAM)">
                <Param name="Font font" />
            </Overload>
        </KeyWord>
        <KeyWord name="ExportFontAsCode" func="yes">
            <Overload retVal="bool" descr="Export font as code file, returns true on success">
                <Param name="Font font" />
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>

        <!-- Text drawing functions -->
        <KeyWord name="DrawFPS" func="yes">
            <Overload retVal="void" descr="Draw current FPS">
                <Param name="int posX" />
                <Param name="int posY" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawText" func="yes">
            <Overload retVal="void" descr="Draw text (using default font)">
                <Param name="const char *text" />
                <Param name="int posX" />
                <Param name="int posY" />
                <Param name="int fontSize" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTextEx" func="yes">
            <Overload retVal="void" descr="Draw text using font and additional parameters">
                <Param name="Font font" />
                <Param name="const char *text" />
                <Param name="Vector2 position" />
                <Param name="float fontSize" />
                <Param name="float spacing" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTextPro" func="yes">
            <Overload retVal="void" descr="Draw text using Font and pro parameters (rotation)">
                <Param name="Font font" />
                <Param name="const char *text" />
                <Param name="Vector2 position" />
                <Param name="Vector2 origin" />
                <Param name="float rotation" />
                <Param name="float fontSize" />
                <Param name="float spacing" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTextCodepoint" func="yes">
            <Overload retVal="void" descr="Draw one character (codepoint)">
                <Param name="Font font" />
                <Param name="int codepoint" />
                <Param name="Vector2 position" />
                <Param name="float fontSize" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTextCodepoints" func="yes">
            <Overload retVal="void" descr="Draw multiple character (codepoint)">
                <Param name="Font font" />
                <Param name="const int *codepoints" />
                <Param name="int count" />
                <Param name="Vector2 position" />
                <Param name="float fontSize" />
                <Param name="float spacing" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>

        <!-- Text font info functions -->
        <KeyWord name="MeasureText" func="yes">
            <Overload retVal="int" descr="Measure string width for default font">
                <Param name="const char *text" />
                <Param name="int fontSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="MeasureTextEx" func="yes">
            <Overload retVal="Vector2" descr="Measure string size for Font">
                <Param name="Font font" />
                <Param name="const char *text" />
                <Param name="float fontSize" />
                <Param name="float spacing" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetGlyphIndex" func="yes">
            <Overload retVal="int" descr="Get glyph index position in font for a codepoint (unicode character), fallback to '?' if not found">
                <Param name="Font font" />
                <Param name="int codepoint" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetGlyphInfo" func="yes">
            <Overload retVal="GlyphInfo" descr="Get glyph font info data for a codepoint (unicode character), fallback to '?' if not found">
                <Param name="Font font" />
                <Param name="int codepoint" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetGlyphAtlasRec" func="yes">
            <Overload retVal="Rectangle" descr="Get glyph rectangle in font atlas for a codepoint (unicode character), fallback to '?' if not found">
                <Param name="Font font" />
                <Param name="int codepoint" />
            </Overload>
        </KeyWord>

        <!-- Text codepoints management functions (unicode characters) -->
        <KeyWord name="LoadUTF8" func="yes">
            <Overload retVal="char" descr="Load UTF-8 text encoded from codepoints array">
                <Param name="const int *codepoints" />
                <Param name="int length" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadUTF8" func="yes">
            <Overload retVal="void" descr="Unload UTF-8 text encoded from codepoints array">
                <Param name="char *text" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadCodepoints" func="yes">
            <Overload retVal="int" descr="Load all codepoints from a UTF-8 text string, codepoints count returned by parameter">
                <Param name="const char *text" />
                <Param name="int *count" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadCodepoints" func="yes">
            <Overload retVal="void" descr="Unload codepoints data from memory">
                <Param name="int *codepoints" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetCodepointCount" func="yes">
            <Overload retVal="int" descr="Get total number of codepoints in a UTF-8 encoded string">
                <Param name="const char *text" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetCodepoint" func="yes">
            <Overload retVal="int" descr="Get next codepoint in a UTF-8 encoded string, 0x3f('?') is returned on failure">
                <Param name="const char *text" />
                <Param name="int *codepointSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetCodepointNext" func="yes">
            <Overload retVal="int" descr="Get next codepoint in a UTF-8 encoded string, 0x3f('?') is returned on failure">
                <Param name="const char *text" />
                <Param name="int *codepointSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetCodepointPrevious" func="yes">
            <Overload retVal="int" descr="Get previous codepoint in a UTF-8 encoded string, 0x3f('?') is returned on failure">
                <Param name="const char *text" />
                <Param name="int *codepointSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="CodepointToUTF8" func="yes">
            <Overload retVal="const char" descr="Encode one codepoint into UTF-8 byte array (array length returned as parameter)">
                <Param name="int codepoint" />
                <Param name="int *utf8Size" />
            </Overload>
        </KeyWord>

        <!-- Text strings management functions (no UTF-8 strings, only byte chars) -->
        <!-- NOTE: Some strings allocate memory internally for returned strings, just be careful! -->
        <KeyWord name="TextCopy" func="yes">
            <Overload retVal="int" descr="Copy one string to another, returns bytes copied">
                <Param name="char *dst" />
                <Param name="const char *src" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextIsEqual" func="yes">
            <Overload retVal="bool" descr="Check if two text string are equal">
                <Param name="const char *text1" />
                <Param name="const char *text2" />
            </Overload>
        </KeyWord>
        <KeyWord name="int TextLength" func="yes">
            <Overload retVal="unsigned" descr="Get text length, checks for '\0' ending">
                <Param name="const char *text" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextFormat" func="yes">
            <Overload retVal="const char" descr="Text formatting with variables (sprintf() style)">
                <Param name="const char *text" />
                <Param name="..." />
            </Overload>
        </KeyWord>
        <KeyWord name="TextSubtext" func="yes">
            <Overload retVal="const char" descr="Get a piece of a text string">
                <Param name="const char *text" />
                <Param name="int position" />
                <Param name="int length" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextReplace" func="yes">
            <Overload retVal="char" descr="Replace text string (WARNING: memory must be freed!)">
                <Param name="char *text" />
                <Param name="const char *replace" />
                <Param name="const char *by" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextInsert" func="yes">
            <Overload retVal="char" descr="Insert text in a position (WARNING: memory must be freed!)">
                <Param name="const char *text" />
                <Param name="const char *insert" />
                <Param name="int position" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextJoin" func="yes">
            <Overload retVal="const char" descr="Join text strings with delimiter">
                <Param name="const char **textList" />
                <Param name="int count" />
                <Param name="const char *delimiter" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextSplit" func="yes">
            <Overload retVal="const char" descr="Split text into multiple strings">
                <Param name="const char *text" />
                <Param name="char delimiter" />
                <Param name="int *count" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextAppend" func="yes">
            <Overload retVal="void" descr="Append text at specific position and move cursor!">
                <Param name="char *text" />
                <Param name="const char *append" />
                <Param name="int *position" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextFindIndex" func="yes">
            <Overload retVal="int" descr="Find first text occurrence within a string">
                <Param name="const char *text" />
                <Param name="const char *find" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextToUpper" func="yes">
            <Overload retVal="const char" descr="Get upper case version of provided string">
                <Param name="const char *text" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextToLower" func="yes">
            <Overload retVal="const char" descr="Get lower case version of provided string">
                <Param name="const char *text" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextToPascal" func="yes">
            <Overload retVal="const char" descr="Get Pascal case notation version of provided string">
                <Param name="const char *text" />
            </Overload>
        </KeyWord>
        <KeyWord name="TextToInteger" func="yes">
            <Overload retVal="int" descr="Get integer value from text (negative values not supported)">
                <Param name="const char *text" />
            </Overload>
        </KeyWord>

        <!-------------------------------------------------------------------------------------- -->
        <!-- Basic 3d Shapes Drawing Functions (Module: models) -->
        <!-------------------------------------------------------------------------------------- -->

        <!-- Basic geometric 3D shapes drawing functions -->
        <KeyWord name="DrawLine3D" func="yes">
            <Overload retVal="void" descr="Draw a line in 3D world space">
                <Param name="Vector3 startPos" />
                <Param name="Vector3 endPos" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawPoint3D" func="yes">
            <Overload retVal="void" descr="Draw a point in 3D space, actually a small line">
                <Param name="Vector3 position" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCircle3D" func="yes">
            <Overload retVal="void" descr="Draw a circle in 3D world space">
                <Param name="Vector3 center" />
                <Param name="float radius" />
                <Param name="Vector3 rotationAxis" />
                <Param name="float rotationAngle" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTriangle3D" func="yes">
            <Overload retVal="void" descr="Draw a color-filled triangle (vertex in counter-clockwise order!)">
                <Param name="Vector3 v1" />
                <Param name="Vector3 v2" />
                <Param name="Vector3 v3" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawTriangleStrip3D" func="yes">
            <Overload retVal="void" descr="Draw a triangle strip defined by points">
                <Param name="Vector3 *points" />
                <Param name="int pointCount" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCube" func="yes">
            <Overload retVal="void" descr="Draw cube">
                <Param name="Vector3 position" />
                <Param name="float width" />
                <Param name="float height" />
                <Param name="float length" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCubeV" func="yes">
            <Overload retVal="void" descr="Draw cube (Vector version)">
                <Param name="Vector3 position" />
                <Param name="Vector3 size" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCubeWires" func="yes">
            <Overload retVal="void" descr="Draw cube wires">
                <Param name="Vector3 position" />
                <Param name="float width" />
                <Param name="float height" />
                <Param name="float length" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCubeWiresV" func="yes">
            <Overload retVal="void" descr="Draw cube wires (Vector version)">
                <Param name="Vector3 position" />
                <Param name="Vector3 size" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawSphere" func="yes">
            <Overload retVal="void" descr="Draw sphere">
                <Param name="Vector3 centerPos" />
                <Param name="float radius" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawSphereEx" func="yes">
            <Overload retVal="void" descr="Draw sphere with extended parameters">
                <Param name="Vector3 centerPos" />
                <Param name="float radius" />
                <Param name="int rings" />
                <Param name="int slices" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawSphereWires" func="yes">
            <Overload retVal="void" descr="Draw sphere wires">
                <Param name="Vector3 centerPos" />
                <Param name="float radius" />
                <Param name="int rings" />
                <Param name="int slices" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCylinder" func="yes">
            <Overload retVal="void" descr="Draw a cylinder/cone">
                <Param name="Vector3 position" />
                <Param name="float radiusTop" />
                <Param name="float radiusBottom" />
                <Param name="float height" />
                <Param name="int slices" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCylinderEx" func="yes">
            <Overload retVal="void" descr="Draw a cylinder with base at startPos and top at endPos">
                <Param name="Vector3 startPos" />
                <Param name="Vector3 endPos" />
                <Param name="float startRadius" />
                <Param name="float endRadius" />
                <Param name="int sides" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCylinderWires" func="yes">
            <Overload retVal="void" descr="Draw a cylinder/cone wires">
                <Param name="Vector3 position" />
                <Param name="float radiusTop" />
                <Param name="float radiusBottom" />
                <Param name="float height" />
                <Param name="int slices" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCylinderWiresEx" func="yes">
            <Overload retVal="void" descr="Draw a cylinder wires with base at startPos and top at endPos">
                <Param name="Vector3 startPos" />
                <Param name="Vector3 endPos" />
                <Param name="float startRadius" />
                <Param name="float endRadius" />
                <Param name="int sides" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCapsule" func="yes">
            <Overload retVal="void" descr="Draw a capsule with the center of its sphere caps at startPos and endPos">
                <Param name="Vector3 startPos" />
                <Param name="Vector3 endPos" />
                <Param name="float radius" />
                <Param name="int slices" />
                <Param name="int rings" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawCapsuleWires" func="yes">
            <Overload retVal="void" descr="Draw capsule wireframe with the center of its sphere caps at startPos and endPos">
                <Param name="Vector3 startPos" />
                <Param name="Vector3 endPos" />
                <Param name="float radius" />
                <Param name="int slices" />
                <Param name="int rings" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawPlane" func="yes">
            <Overload retVal="void" descr="Draw a plane XZ">
                <Param name="Vector3 centerPos" />
                <Param name="Vector2 size" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawRay" func="yes">
            <Overload retVal="void" descr="Draw a ray line">
                <Param name="Ray ray" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawGrid" func="yes">
            <Overload retVal="void" descr="Draw a grid (centered at (0, 0, 0))">
                <Param name="int slices" />
                <Param name="float spacing" />
            </Overload>
        </KeyWord>

        <!-------------------------------------------------------------------------------------- -->
        <!-- Model 3d Loading and Drawing Functions (Module: models) -->
        <!-------------------------------------------------------------------------------------- -->

        <!-- Model management functions -->
        <KeyWord name="LoadModel" func="yes">
            <Overload retVal="Model" descr="Load model from files (meshes and materials)">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadModelFromMesh" func="yes">
            <Overload retVal="Model" descr="Load model from generated mesh (default material)">
                <Param name="Mesh mesh" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsModelReady" func="yes">
            <Overload retVal="bool" descr="Check if a model is ready">
                <Param name="Model model" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadModel" func="yes">
            <Overload retVal="void" descr="Unload model (including meshes) from memory (RAM and/or VRAM)">
                <Param name="Model model" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetModelBoundingBox" func="yes">
            <Overload retVal="BoundingBox" descr="Compute model bounding box limits (considers all meshes)">
                <Param name="Model model" />
            </Overload>
        </KeyWord>

        <!-- Model drawing functions -->
        <KeyWord name="DrawModel" func="yes">
            <Overload retVal="void" descr="Draw a model (with texture if set)">
                <Param name="Model model" />
                <Param name="Vector3 position" />
                <Param name="float scale" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawModelEx" func="yes">
            <Overload retVal="void" descr="Draw a model with extended parameters">
                <Param name="Model model" />
                <Param name="Vector3 position" />
                <Param name="Vector3 rotationAxis" />
                <Param name="float rotationAngle" />
                <Param name="Vector3 scale" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawModelWires" func="yes">
            <Overload retVal="void" descr="Draw a model wires (with texture if set)">
                <Param name="Model model" />
                <Param name="Vector3 position" />
                <Param name="float scale" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawModelWiresEx" func="yes">
            <Overload retVal="void" descr="Draw a model wires (with texture if set) with extended parameters">
                <Param name="Model model" />
                <Param name="Vector3 position" />
                <Param name="Vector3 rotationAxis" />
                <Param name="float rotationAngle" />
                <Param name="Vector3 scale" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawBoundingBox" func="yes">
            <Overload retVal="void" descr="Draw bounding box (wires)">
                <Param name="BoundingBox box" />
                <Param name="Color color" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawBillboard" func="yes">
            <Overload retVal="void" descr="Draw a billboard texture">
                <Param name="Camera camera" />
                <Param name="Texture2D texture" />
                <Param name="Vector3 position" />
                <Param name="float size" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawBillboardRec" func="yes">
            <Overload retVal="void" descr="Draw a billboard texture defined by source">
                <Param name="Camera camera" />
                <Param name="Texture2D texture" />
                <Param name="Rectangle source" />
                <Param name="Vector3 position" />
                <Param name="Vector2 size" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawBillboardPro" func="yes">
            <Overload retVal="void" descr="Draw a billboard texture defined by source and rotation">
                <Param name="Camera camera" />
                <Param name="Texture2D texture" />
                <Param name="Rectangle source" />
                <Param name="Vector3 position" />
                <Param name="Vector3 up" />
                <Param name="Vector2 size" />
                <Param name="Vector2 origin" />
                <Param name="float rotation" />
                <Param name="Color tint" />
            </Overload>
        </KeyWord>

        <!-- Mesh management functions -->
        <KeyWord name="UploadMesh" func="yes">
            <Overload retVal="void" descr="Upload mesh vertex data in GPU and provide VAO/VBO ids">
                <Param name="Mesh *mesh" />
                <Param name="bool dynamic" />
            </Overload>
        </KeyWord>
        <KeyWord name="UpdateMeshBuffer" func="yes">
            <Overload retVal="void" descr="Update mesh vertex data in GPU for a specific buffer index">
                <Param name="Mesh mesh" />
                <Param name="int index" />
                <Param name="const void *data" />
                <Param name="int dataSize" />
                <Param name="int offset" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadMesh" func="yes">
            <Overload retVal="void" descr="Unload mesh data from CPU and GPU">
                <Param name="Mesh mesh" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawMesh" func="yes">
            <Overload retVal="void" descr="Draw a 3d mesh with material and transform">
                <Param name="Mesh mesh" />
                <Param name="Material material" />
                <Param name="Matrix transform" />
            </Overload>
        </KeyWord>
        <KeyWord name="DrawMeshInstanced" func="yes">
            <Overload retVal="void" descr="Draw multiple mesh instances with material and different transforms">
                <Param name="Mesh mesh" />
                <Param name="Material material" />
                <Param name="const Matrix *transforms" />
                <Param name="int instances" />
            </Overload>
        </KeyWord>
        <KeyWord name="ExportMesh" func="yes">
            <Overload retVal="bool" descr="Export mesh data to file, returns true on success">
                <Param name="Mesh mesh" />
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetMeshBoundingBox" func="yes">
            <Overload retVal="BoundingBox" descr="Compute mesh bounding box limits">
                <Param name="Mesh mesh" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshTangents" func="yes">
            <Overload retVal="void" descr="Compute mesh tangents">
                <Param name="Mesh *mesh" />
            </Overload>
        </KeyWord>

        <!-- Mesh generation functions -->
        <KeyWord name="GenMeshPoly" func="yes">
            <Overload retVal="Mesh" descr="Generate polygonal mesh">
                <Param name="int sides" />
                <Param name="float radius" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshPlane" func="yes">
            <Overload retVal="Mesh" descr="Generate plane mesh (with subdivisions)">
                <Param name="float width" />
                <Param name="float length" />
                <Param name="int resX" />
                <Param name="int resZ" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshCube" func="yes">
            <Overload retVal="Mesh" descr="Generate cuboid mesh">
                <Param name="float width" />
                <Param name="float height" />
                <Param name="float length" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshSphere" func="yes">
            <Overload retVal="Mesh" descr="Generate sphere mesh (standard sphere)">
                <Param name="float radius" />
                <Param name="int rings" />
                <Param name="int slices" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshHemiSphere" func="yes">
            <Overload retVal="Mesh" descr="Generate half-sphere mesh (no bottom cap)">
                <Param name="float radius" />
                <Param name="int rings" />
                <Param name="int slices" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshCylinder" func="yes">
            <Overload retVal="Mesh" descr="Generate cylinder mesh">
                <Param name="float radius" />
                <Param name="float height" />
                <Param name="int slices" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshCone" func="yes">
            <Overload retVal="Mesh" descr="Generate cone/pyramid mesh">
                <Param name="float radius" />
                <Param name="float height" />
                <Param name="int slices" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshTorus" func="yes">
            <Overload retVal="Mesh" descr="Generate torus mesh">
                <Param name="float radius" />
                <Param name="float size" />
                <Param name="int radSeg" />
                <Param name="int sides" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshKnot" func="yes">
            <Overload retVal="Mesh" descr="Generate trefoil knot mesh">
                <Param name="float radius" />
                <Param name="float size" />
                <Param name="int radSeg" />
                <Param name="int sides" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshHeightmap" func="yes">
            <Overload retVal="Mesh" descr="Generate heightmap mesh from image data">
                <Param name="Image heightmap" />
                <Param name="Vector3 size" />
            </Overload>
        </KeyWord>
        <KeyWord name="GenMeshCubicmap" func="yes">
            <Overload retVal="Mesh" descr="Generate cubes-based map mesh from image data">
                <Param name="Image cubicmap" />
                <Param name="Vector3 cubeSize" />
            </Overload>
        </KeyWord>

        <!-- Material loading/unloading functions -->
        <KeyWord name="LoadMaterials" func="yes">
            <Overload retVal="Material" descr="Load materials from model file">
                <Param name="const char *fileName" />
                <Param name="int *materialCount" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadMaterialDefault" func="yes">
            <Overload retVal="Material" descr="Load default material (Supports: DIFFUSE, SPECULAR, NORMAL maps)"></Overload>
        </KeyWord>
        <KeyWord name="IsMaterialReady" func="yes">
            <Overload retVal="bool" descr="Check if a material is ready">
                <Param name="Material material" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadMaterial" func="yes">
            <Overload retVal="void" descr="Unload material from GPU memory (VRAM)">
                <Param name="Material material" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetMaterialTexture" func="yes">
            <Overload retVal="void" descr="Set texture for a material map type (MATERIAL_MAP_DIFFUSE, MATERIAL_MAP_SPECULAR...)">
                <Param name="Material *material" />
                <Param name="int mapType" />
                <Param name="Texture2D texture" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetModelMeshMaterial" func="yes">
            <Overload retVal="void" descr="Set material for a mesh">
                <Param name="Model *model" />
                <Param name="int meshId" />
                <Param name="int materialId" />
            </Overload>
        </KeyWord>

        <!-- Model animations loading/unloading functions -->
        <KeyWord name="LoadModelAnimations" func="yes">
            <Overload retVal="ModelAnimation" descr="Load model animations from file">
                <Param name="const char *fileName" />
                <Param name="unsigned int *animCount" />
            </Overload>
        </KeyWord>
        <KeyWord name="UpdateModelAnimation" func="yes">
            <Overload retVal="void" descr="Update model animation pose">
                <Param name="Model model" />
                <Param name="ModelAnimation anim" />
                <Param name="int frame" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadModelAnimation" func="yes">
            <Overload retVal="void" descr="Unload animation data">
                <Param name="ModelAnimation anim" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadModelAnimations" func="yes">
            <Overload retVal="void" descr="Unload animation array data">
                <Param name="ModelAnimation *animations" />
                <Param name="unsigned int count" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsModelAnimationValid" func="yes">
            <Overload retVal="bool" descr="Check model animation skeleton match">
                <Param name="Model model" />
                <Param name="ModelAnimation anim" />
            </Overload>
        </KeyWord>

        <!-- Collision detection functions -->
        <KeyWord name="CheckCollisionSpheres" func="yes">
            <Overload retVal="bool" descr="Check collision between two spheres">
                <Param name="Vector3 center1" />
                <Param name="float radius1" />
                <Param name="Vector3 center2" />
                <Param name="float radius2" />
            </Overload>
        </KeyWord>
        <KeyWord name="CheckCollisionBoxes" func="yes">
            <Overload retVal="bool" descr="Check collision between two bounding boxes">
                <Param name="BoundingBox box1" />
                <Param name="BoundingBox box2" />
            </Overload>
        </KeyWord>
        <KeyWord name="CheckCollisionBoxSphere" func="yes">
            <Overload retVal="bool" descr="Check collision between box and sphere">
                <Param name="BoundingBox box" />
                <Param name="Vector3 center" />
                <Param name="float radius" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetRayCollisionSphere" func="yes">
            <Overload retVal="RayCollision" descr="Get collision info between ray and sphere">
                <Param name="Ray ray" />
                <Param name="Vector3 center" />
                <Param name="float radius" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetRayCollisionBox" func="yes">
            <Overload retVal="RayCollision" descr="Get collision info between ray and box">
                <Param name="Ray ray" />
                <Param name="BoundingBox box" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetRayCollisionMesh" func="yes">
            <Overload retVal="RayCollision" descr="Get collision info between ray and mesh">
                <Param name="Ray ray" />
                <Param name="Mesh mesh" />
                <Param name="Matrix transform" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetRayCollisionTriangle" func="yes">
            <Overload retVal="RayCollision" descr="Get collision info between ray and triangle">
                <Param name="Ray ray" />
                <Param name="Vector3 p1" />
                <Param name="Vector3 p2" />
                <Param name="Vector3 p3" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetRayCollisionQuad" func="yes">
            <Overload retVal="RayCollision" descr="Get collision info between ray and quad">
                <Param name="Ray ray" />
                <Param name="Vector3 p1" />
                <Param name="Vector3 p2" />
                <Param name="Vector3 p3" />
                <Param name="Vector3 p4" />
            </Overload>
        </KeyWord>

        <!-------------------------------------------------------------------------------------- -->
        <!-- Audio Loading and Playing Functions (Module: audio) -->
        <!-------------------------------------------------------------------------------------- -->

        <!-- Audio device management functions -->
        <KeyWord name="InitAudioDevice" func="yes">
            <Overload retVal="void" descr="Initialize audio device and context"></Overload>
        </KeyWord>
        <KeyWord name="CloseAudioDevice" func="yes">
            <Overload retVal="void" descr="Close the audio device and context"></Overload>
        </KeyWord>
        <KeyWord name="IsAudioDeviceReady" func="yes">
            <Overload retVal="bool" descr="Check if audio device has been initialized successfully"></Overload>
        </KeyWord>
        <KeyWord name="SetMasterVolume" func="yes">
            <Overload retVal="void" descr="Set master volume (listener)">
                <Param name="float volume" />
            </Overload>
        </KeyWord>

        <!-- Wave/Sound loading/unloading functions -->
        <KeyWord name="LoadWave" func="yes">
            <Overload retVal="Wave" descr="Load wave data from file">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadWaveFromMemory" func="yes">
            <Overload retVal="Wave" descr="Load wave from memory buffer, fileType refers to extension: i.e. '.wav'">
                <Param name="const char *fileType" />
                <Param name="const unsigned char" />
                <Param name="int dataSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsWaveReady" func="yes">
            <Overload retVal="bool" descr="Checks if wave data is ready">
                <Param name="Wave wave" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadSound" func="yes">
            <Overload retVal="Sound" descr="Load sound from file">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadSoundFromWave" func="yes">
            <Overload retVal="Sound" descr="Load sound from wave data">
                <Param name="Wave wave" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsSoundReady" func="yes">
            <Overload retVal="bool" descr="Checks if a sound is ready">
                <Param name="Sound sound" />
            </Overload>
        </KeyWord>
        <KeyWord name="UpdateSound" func="yes">
            <Overload retVal="void" descr="Update sound buffer with new data">
                <Param name="Sound sound" />
                <Param name="const void *data" />
                <Param name="int sampleCount" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadWave" func="yes">
            <Overload retVal="void" descr="Unload wave data">
                <Param name="Wave wave" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadSound" func="yes">
            <Overload retVal="void" descr="Unload sound">
                <Param name="Sound sound" />
            </Overload>
        </KeyWord>
        <KeyWord name="ExportWave" func="yes">
            <Overload retVal="bool" descr="Export wave data to file, returns true on success">
                <Param name="Wave wave" />
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="ExportWaveAsCode" func="yes">
            <Overload retVal="bool" descr="Export wave sample data to code (.h), returns true on success">
                <Param name="Wave wave" />
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>

        <!-- Wave/Sound management functions -->
        <KeyWord name="PlaySound" func="yes">
            <Overload retVal="void" descr="Play a sound">
                <Param name="Sound sound" />
            </Overload>
        </KeyWord>
        <KeyWord name="StopSound" func="yes">
            <Overload retVal="void" descr="Stop playing a sound">
                <Param name="Sound sound" />
            </Overload>
        </KeyWord>
        <KeyWord name="PauseSound" func="yes">
            <Overload retVal="void" descr="Pause a sound">
                <Param name="Sound sound" />
            </Overload>
        </KeyWord>
        <KeyWord name="ResumeSound" func="yes">
            <Overload retVal="void" descr="Resume a paused sound">
                <Param name="Sound sound" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsSoundPlaying" func="yes">
            <Overload retVal="bool" descr="Check if a sound is currently playing">
                <Param name="Sound sound" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetSoundVolume" func="yes">
            <Overload retVal="void" descr="Set volume for a sound (1.0 is max level)">
                <Param name="Sound sound" />
                <Param name="float volume" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetSoundPitch" func="yes">
            <Overload retVal="void" descr="Set pitch for a sound (1.0 is base level)">
                <Param name="Sound sound" />
                <Param name="float pitch" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetSoundPan" func="yes">
            <Overload retVal="void" descr="Set pan for a sound (0.5 is center)">
                <Param name="Sound sound" />
                <Param name="float pan" />
            </Overload>
        </KeyWord>
        <KeyWord name="WaveCopy" func="yes">
            <Overload retVal="Wave" descr="Copy a wave to a new wave">
                <Param name="Wave wave" />
            </Overload>
        </KeyWord>
        <KeyWord name="WaveCrop" func="yes">
            <Overload retVal="void" descr="Crop a wave to defined samples range">
                <Param name="Wave *wave" />
                <Param name="int initSample" />
                <Param name="int finalSample" />
            </Overload>
        </KeyWord>
        <KeyWord name="WaveFormat" func="yes">
            <Overload retVal="void" descr="Convert wave data to desired format">
                <Param name="Wave *wave" />
                <Param name="int sampleRate" />
                <Param name="int sampleSize" />
                <Param name="int channels" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadWaveSamples" func="yes">
            <Overload retVal="float" descr="Load samples data from wave as a 32bit float data array">
                <Param name="Wave wave" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadWaveSamples" func="yes">
            <Overload retVal="void" descr="Unload samples data loaded with LoadWaveSamples()">
                <Param name="float *samples" />
            </Overload>
        </KeyWord>

        <!-- Music management functions -->
        <KeyWord name="LoadMusicStream" func="yes">
            <Overload retVal="Music" descr="Load music stream from file">
                <Param name="const char *fileName" />
            </Overload>
        </KeyWord>
        <KeyWord name="LoadMusicStreamFromMemory" func="yes">
            <Overload retVal="Music" descr="Load music stream from data">
                <Param name="const char *fileType" />
                <Param name="const unsigned char" />
                <Param name="int dataSize" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsMusicReady" func="yes">
            <Overload retVal="bool" descr="Checks if a music stream is ready">
                <Param name="Music music" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadMusicStream" func="yes">
            <Overload retVal="void" descr="Unload music stream">
                <Param name="Music music" />
            </Overload>
        </KeyWord>
        <KeyWord name="PlayMusicStream" func="yes">
            <Overload retVal="void" descr="Start music playing">
                <Param name="Music music" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsMusicStreamPlaying" func="yes">
            <Overload retVal="bool" descr="Check if music is playing">
                <Param name="Music music" />
            </Overload>
        </KeyWord>
        <KeyWord name="UpdateMusicStream" func="yes">
            <Overload retVal="void" descr="Updates buffers for music streaming">
                <Param name="Music music" />
            </Overload>
        </KeyWord>
        <KeyWord name="StopMusicStream" func="yes">
            <Overload retVal="void" descr="Stop music playing">
                <Param name="Music music" />
            </Overload>
        </KeyWord>
        <KeyWord name="PauseMusicStream" func="yes">
            <Overload retVal="void" descr="Pause music playing">
                <Param name="Music music" />
            </Overload>
        </KeyWord>
        <KeyWord name="ResumeMusicStream" func="yes">
            <Overload retVal="void" descr="Resume playing paused music">
                <Param name="Music music" />
            </Overload>
        </KeyWord>
        <KeyWord name="SeekMusicStream" func="yes">
            <Overload retVal="void" descr="Seek music to a position (in seconds)">
                <Param name="Music music" />
                <Param name="float position" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetMusicVolume" func="yes">
            <Overload retVal="void" descr="Set volume for music (1.0 is max level)">
                <Param name="Music music" />
                <Param name="float volume" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetMusicPitch" func="yes">
            <Overload retVal="void" descr="Set pitch for a music (1.0 is base level)">
                <Param name="Music music" />
                <Param name="float pitch" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetMusicPan" func="yes">
            <Overload retVal="void" descr="Set pan for a music (0.5 is center)">
                <Param name="Music music" />
                <Param name="float pan" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetMusicTimeLength" func="yes">
            <Overload retVal="float" descr="Get music time length (in seconds)">
                <Param name="Music music" />
            </Overload>
        </KeyWord>
        <KeyWord name="GetMusicTimePlayed" func="yes">
            <Overload retVal="float" descr="Get current music time played (in seconds)">
                <Param name="Music music" />
            </Overload>
        </KeyWord>

        <!-- AudioStream management functions -->
        <KeyWord name="LoadAudioStream" func="yes">
            <Overload retVal="AudioStream" descr="Load audio stream (to stream raw audio pcm data)">
                <Param name="unsigned int sampleRate" />
                <Param name="unsigned int sampleSize" />
                <Param name="unsigned int channels" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsAudioStreamReady" func="yes">
            <Overload retVal="bool" descr="Checks if an audio stream is ready">
                <Param name="AudioStream stream" />
            </Overload>
        </KeyWord>
        <KeyWord name="UnloadAudioStream" func="yes">
            <Overload retVal="void" descr="Unload audio stream and free memory">
                <Param name="AudioStream stream" />
            </Overload>
        </KeyWord>
        <KeyWord name="UpdateAudioStream" func="yes">
            <Overload retVal="void" descr="Update audio stream buffers with data">
                <Param name="AudioStream stream" />
                <Param name="const void *data" />
                <Param name="int frameCount" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsAudioStreamProcessed" func="yes">
            <Overload retVal="bool" descr="Check if any audio stream buffers requires refill">
                <Param name="AudioStream stream" />
            </Overload>
        </KeyWord>
        <KeyWord name="PlayAudioStream" func="yes">
            <Overload retVal="void" descr="Play audio stream">
                <Param name="AudioStream stream" />
            </Overload>
        </KeyWord>
        <KeyWord name="PauseAudioStream" func="yes">
            <Overload retVal="void" descr="Pause audio stream">
                <Param name="AudioStream stream" />
            </Overload>
        </KeyWord>
        <KeyWord name="ResumeAudioStream" func="yes">
            <Overload retVal="void" descr="Resume audio stream">
                <Param name="AudioStream stream" />
            </Overload>
        </KeyWord>
        <KeyWord name="IsAudioStreamPlaying" func="yes">
            <Overload retVal="bool" descr="Check if audio stream is playing">
                <Param name="AudioStream stream" />
            </Overload>
        </KeyWord>
        <KeyWord name="StopAudioStream" func="yes">
            <Overload retVal="void" descr="Stop audio stream">
                <Param name="AudioStream stream" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetAudioStreamVolume" func="yes">
            <Overload retVal="void" descr="Set volume for audio stream (1.0 is max level)">
                <Param name="AudioStream stream" />
                <Param name="float volume" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetAudioStreamPitch" func="yes">
            <Overload retVal="void" descr="Set pitch for audio stream (1.0 is base level)">
                <Param name="AudioStream stream" />
                <Param name="float pitch" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetAudioStreamPan" func="yes">
            <Overload retVal="void" descr="Set pan for audio stream (0.5 is centered)">
                <Param name="AudioStream stream" />
                <Param name="float pan" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetAudioStreamBufferSizeDefault" func="yes">
            <Overload retVal="void" descr="Default size for new audio streams">
                <Param name="int size" />
            </Overload>
        </KeyWord>
        <KeyWord name="SetAudioStreamCallback" func="yes">
            <Overload retVal="void" descr="Audio thread callback to request new data">
                <Param name="AudioStream stream" />
                <Param name="AudioCallback callback" />
            </Overload>
        </KeyWord>

        <KeyWord name="AttachAudioStreamProcessor" func="yes">
            <Overload retVal="void" descr="Attach audio stream processor to stream">
                <Param name="AudioStream stream" />
                <Param name="AudioCallback processor" />
            </Overload>
        </KeyWord>
        <KeyWord name="DetachAudioStreamProcessor" func="yes">
            <Overload retVal="void" descr="Detach audio stream processor from stream">
                <Param name="AudioStream stream" />
                <Param name="AudioCallback processor" />
            </Overload>
        </KeyWord>

        <KeyWord name="AttachAudioMixedProcessor" func="yes">
            <Overload retVal="void" descr="Attach audio stream processor to the entire audio pipeline">
                <Param name="AudioCallback processor" />
            </Overload>
        </KeyWord>
        <KeyWord name="DetachAudioMixedProcessor" func="yes">
            <Overload retVal="void" descr="Detach audio stream processor from the entire audio pipeline">
                <Param name="AudioCallback processor" />
            </Overload>
        </KeyWord>


