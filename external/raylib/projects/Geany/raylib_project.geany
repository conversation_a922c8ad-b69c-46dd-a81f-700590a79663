[editor]
line_wrapping=false
line_break_column=72
auto_continue_multiline=true

[file_prefs]
final_new_line=true
ensure_convert_new_lines=false
strip_trailing_spaces=false
replace_tabs=true

[indentation]
indent_width=4
indent_type=0
indent_hard_tab_width=8
detect_indent=false
detect_indent_width=false
indent_mode=2

[project]
name=raylib_project
base_path=./
description=raylib project template
file_patterns=

[long line marker]
long_line_behaviour=1
long_line_column=72

[files]
current_page=0
FILE_NAME_0=0;C;0;EUTF-8;1;1;0;C%3A%5CGitHub%5Craylib%5Cprojects%5CGeany%5Ccore_basic_window.c;0;4

[build-menu]
filetypes=C;
EX_00_LB=_Execute
EX_00_CM="./%e"
EX_00_WD=
CFT_00_LB=_Compile
CFT_00_CM=raylib_compile_execute.bat %f
CFT_00_WD=
