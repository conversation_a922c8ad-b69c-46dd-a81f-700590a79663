# format=pipe 
InitWindow|void|(int width, int height, const char *title);|
WindowShouldClose|bool|(void);|
CloseWindow|void|(void);|
IsWindowReady|bool|(void);|
IsWindowMinimized|bool|(void);|
IsWindowResized|bool|(void);|
IsWindowHidden|bool|(void);|
ToggleFullscreen|void|(void);|
UnhideWindow|void|(void);|
HideWindow|void|(void);|
SetWindowIcon|void|(Image image);|
SetWindowTitle|void|(const char *title);|
SetWindowPosition|void|(int x, int y);|
SetWindowMonitor|void|(int monitor);|
SetWindowMinSize|void|(int width, int height);|
SetWindowSize|void|(int width, int height);|
GetWindowHandle|void *|(void);|
GetScreenWidth|int|(void);|
GetScreenHeight|int|(void);|
GetMonitorCount|int|(void);|
GetMonitorWidth|int|(int monitor);|
GetMonitorHeight|int|(int monitor);|
GetMonitorPhysicalWidth|int|(int monitor);|
GetMonitorPhysicalHeight|int|(int monitor);|
GetMonitorName|const char *|(int monitor);|
GetClipboardText|const char *|(void);|
SetClipboardText|void|(const char *text);|
ShowCursor|void|(void);|
HideCursor|void|(void);|
IsCursorHidden|bool|(void);|
EnableCursor|void|(void);|
DisableCursor|void|(void);|
ClearBackground|void|(Color color);|
BeginDrawing|void|(void);|
EndDrawing|void|(void);|
BeginMode2D|void|(Camera2D camera);|
EndMode2D|void|(void);|
BeginMode3D|void|(Camera3D camera);|
EndMode3D|void|(void);|
BeginTextureMode|void|(RenderTexture2D target);|
EndTextureMode|void|(void);|
GetMouseRay|Ray|(Vector2 mousePosition, Camera camera);|
GetWorldToScreen|Vector2|(Vector3 position, Camera camera);|
GetCameraMatrix|Matrix|(Camera camera);|
SetTargetFPS|void|(int fps);|
GetFPS|int|(void);|
GetFrameTime|float|(void);|
GetTime|double|(void);|
ColorToInt|int|(Color color);|
ColorNormalize|Vector4|(Color color);|
ColorToHSV|Vector3|(Color color);|
ColorFromHSV|Color|(Vector3 hsv);|
GetColor|Color|(int hexValue);|
Fade|Color|(Color color, float alpha);|
SetConfigFlags|void|(unsigned char flags);|
SetTraceLogLevel|void|(int logType);|
SetTraceLogExit|void|(int logType);|
SetTraceLogCallback|void|(TraceLogCallback callback);|
TraceLog|void|(int logType, const char *text, ...);|
TakeScreenshot|void|(const char *fileName);|
GetRandomValue|int|(int min, int max);|
FileExists|bool|(const char *fileName);|
IsFileExtension|bool|(const char *fileName, const char *ext);|
GetExtension|const char *|(const char *fileName);|
GetFileName|const char *|(const char *filePath);|
GetFileNameWithoutExt|const char *|(const char *filePath);|
GetDirectoryPath|const char *|(const char *fileName);|
GetWorkingDirectory|const char *|(void);|
GetDirectoryFiles|char **|(const char *dirPath, int *count);|
ClearDirectoryFiles|void|(void);|
ChangeDirectory|bool|(const char *dir);|
IsFileDropped|bool|(void);|
GetDroppedFiles|char **|(int *count);|
ClearDroppedFiles|void|(void);|
GetFileModTime|long|(const char *fileName);|
StorageSaveValue|void|(int position, int value);|
StorageLoadValue|int|(int position);|
OpenURL|void|(const char *url);|
IsKeyPressed|bool|(int key);|
IsKeyDown|bool|(int key);|
IsKeyReleased|bool|(int key);|
IsKeyUp|bool|(int key);|
GetKeyPressed|int|(void);|
SetExitKey|void|(int key);|
IsGamepadAvailable|bool|(int gamepad);|
IsGamepadName|bool|(int gamepad, const char *name);|
GetGamepadName|const char *|(int gamepad);|
IsGamepadButtonPressed|bool|(int gamepad, int button);|
IsGamepadButtonDown|bool|(int gamepad, int button);|
IsGamepadButtonReleased|bool|(int gamepad, int button);|
IsGamepadButtonUp|bool|(int gamepad, int button);|
GetGamepadButtonPressed|int|(void);|
GetGamepadAxisCount|int|(int gamepad);|
GetGamepadAxisMovement|float|(int gamepad, int axis);|
IsMouseButtonPressed|bool|(int button);|
IsMouseButtonDown|bool|(int button);|
IsMouseButtonReleased|bool|(int button);|
IsMouseButtonUp|bool|(int button);|
GetMouseX|int|(void);|
GetMouseY|int|(void);|
GetMousePosition|Vector2|(void);|
SetMousePosition|void|(int x, int y);|
SetMouseOffset|void|(int offsetX, int offsetY);|
SetMouseScale|void|(float scaleX, float scaleY);|
GetMouseWheelMove|float|(void);|
GetTouchX|int|(void);|
GetTouchY|int|(void);|
GetTouchPosition|Vector2|(int index);|
SetGesturesEnabled|void|(unsigned int gestureFlags);|
IsGestureDetected|bool|(int gesture);|
GetGestureDetected|int|(void);|
GetTouchPointsCount|int|(void);|
GetGestureHoldDuration|float|(void);|
GetGestureDragVector|Vector2|(void);|
GetGestureDragAngle|float|(void);|
GetGesturePinchVector|Vector2|(void);|
GetGesturePinchAngle|float|(void);|
SetCameraMode|void|(Camera camera, int mode);|
UpdateCamera|void|(Camera *camera);|            
SetCameraPanControl|void|(int panKey);|
SetCameraAltControl|void|(int altKey);|
SetCameraSmoothZoomControl|void|(int szKey);|
SetCameraMoveControls|void|(int frontKey, int backKey, int rightKey, int leftKey, int upKey, int downKey);|
DrawPixel|void|(int posX, int posY, Color color);|
DrawPixelV|void|(Vector2 position, Color color);|
DrawLine|void|(int startPosX, int startPosY, int endPosX, int endPosY, Color color);|
DrawLineV|void|(Vector2 startPos, Vector2 endPos, Color color);|
DrawLineEx|void|(Vector2 startPos, Vector2 endPos, float thick, Color color);|
DrawLineBezier|void|(Vector2 startPos, Vector2 endPos, float thick, Color color);|
DrawCircle|void|(int centerX, int centerY, float radius, Color color);|
DrawCircleSector|void|(Vector2 center, float radius, int startAngle, int endAngle, int segments, Color color);|
DrawCircleSectorLines|void|(Vector2 center, float radius, int startAngle, int endAngle, int segments, Color color); // Draw circle sector outline
DrawCircleGradient|void|(int centerX, int centerY, float radius, Color color1, Color color2);|
DrawCircleV|void|(Vector2 center, float radius, Color color);|
DrawCircleLines|void|(int centerX, int centerY, float radius, Color color);|
DrawRing|void|(Vector2 center, float innerRadius, float outerRadius, int startAngle, int endAngle, int segments, Color color);|
DrawRingLines|void|(Vector2 center, float innerRadius, float outerRadius, int startAngle, int endAngle, int segments, Color color);|
DrawRectangle|void|(int posX, int posY, int width, int height, Color color);|
DrawRectangleV|void|(Vector2 position, Vector2 size, Color color);|
DrawRectangleRec|void|(Rectangle rec, Color color);|
DrawRectanglePro|void|(Rectangle rec, Vector2 origin, float rotation, Color color);|
DrawRectangleGradientV|void|(int posX, int posY, int width, int height, Color color1, Color color2);|
DrawRectangleGradientH|void|(int posX, int posY, int width, int height, Color color1, Color color2);|
DrawRectangleGradientEx|void|(Rectangle rec, Color col1, Color col2, Color col3, Color col4);|
DrawRectangleLines|void|(int posX, int posY, int width, int height, Color color);|
DrawRectangleLinesEx|void|(Rectangle rec, int lineThick, Color color);|
DrawRectangleRounded|void|(Rectangle rec, float roundness, int segments, Color color);|
DrawRectangleRoundedLines|void|(Rectangle rec, float roundness, int segments, int lineThick, Color color);|
DrawTriangle|void|(Vector2 v1, Vector2 v2, Vector2 v3, Color color);|
DrawTriangleLines|void|(Vector2 v1, Vector2 v2, Vector2 v3, Color color);|
DrawPoly|void|(Vector2 center, int sides, float radius, float rotation, Color color);|
DrawPolyEx|void|(Vector2 *points, int numPoints, Color color);|
DrawPolyExLines|void|(Vector2 *points, int numPoints, Color color);|
SetShapesTexture|void|(Texture2D texture, Rectangle source);|
CheckCollisionRecs|bool|(Rectangle rec1, Rectangle rec2);|
CheckCollisionCircles|bool|(Vector2 center1, float radius1, Vector2 center2, float radius2);|
CheckCollisionCircleRec|bool|(Vector2 center, float radius, Rectangle rec);|
GetCollisionRec|Rectangle|(Rectangle rec1, Rectangle rec2);|
CheckCollisionPointRec|bool|(Vector2 point, Rectangle rec);|
CheckCollisionPointCircle|bool|(Vector2 point, Vector2 center, float radius);|
CheckCollisionPointTriangle|bool|(Vector2 point, Vector2 p1, Vector2 p2, Vector2 p3);|
LoadImage|Image|(const char *fileName);|
LoadImageEx|Image|(Color *pixels, int width, int height);|
LoadImagePro|Image|(void *data, int width, int height, int format);|
LoadImageRaw|Image|(const char *fileName, int width, int height, int format, int headerSize);|
ExportImage|void|(Image image, const char *fileName);|
ExportImageAsCode|void|(Image image, const char *fileName);|
LoadTexture|Texture2D|(const char *fileName);|
LoadTextureFromImage|Texture2D|(Image image);|
LoadTextureCubemap|TextureCubemap|(Image image, int layoutType);|
LoadRenderTexture|RenderTexture2D|(int width, int height);|
UnloadImage|void|(Image image);|
UnloadTexture|void|(Texture2D texture);|
UnloadRenderTexture|void|(RenderTexture2D target);|
GetImageData|Color *|(Image image);|
GetImageDataNormalized|Vector4 *|(Image image);|
GetPixelDataSize|int|(int width, int height, int format);|
GetTextureData|Image|(Texture2D texture);|
GetScreenData|Image|(void);|
UpdateTexture|void|(Texture2D texture, const void *pixels);|
ImageCopy|Image|(Image image);|
ImageToPOT|void|(Image *image, Color fillColor);|
ImageFormat|void|(Image *image, int newFormat);|
ImageAlphaMask|void|(Image *image, Image alphaMask);|
ImageAlphaClear|void|(Image *image, Color color, float threshold);
ImageAlphaCrop|void|(Image *image, float threshold);|
ImageAlphaPremultiply|void|(Image *image);|
ImageCrop|void|(Image *image, Rectangle crop);|
ImageResize|void|(Image *image, int newWidth, int newHeight);|
ImageResizeNN|void|(Image *image, int newWidth,int newHeight);|void|
ImageResizeCanvas|void|(Image *image, int newWidth, int newHeight, int offsetX, int offsetY, Color color);|
ImageMipmaps|void|(Image *image);|
ImageDither|void|(Image *image, int rBpp, int gBpp, int bBpp, int aBpp);|
ImageExtractPalette|Color *|(Image image, int maxPaletteSize, int *extractCount);|
ImageText|Image|(const char *text, int fontSize, Color color);|
ImageTextEx|Image|(Font font, const char *text, float fontSize, float spacing, Color tint);|
ImageDraw|void|(Image *dst, Image src, Rectangle srcRec, Rectangle dstRec);|
ImageDrawRectangle|void|(Image *dst, Rectangle rec, Color color);|
ImageDrawRectangleLines|void|(Image *dst, Rectangle rec, int thick, Color color);|
ImageDrawText|void|(Image *dst, Vector2 position, const char *text, int fontSize, Color color);|
ImageDrawTextEx|void|(Image *dst, Vector2 position, Font font, const char *text, float fontSize, float spacing, Color color);|
ImageFlipVertical|void|(Image *image);|
ImageFlipHorizontal|void|(Image *image);|
ImageRotateCW|void|(Image *image);|
ImageRotateCCW|void|(Image *image);|
ImageColorTint|void|(Image *image, Color color);|
ImageColorInvert|void|(Image *image);|
ImageColorGrayscale|void|(Image *image);|
ImageColorContrast|void|(Image *image, float contrast);|
ImageColorBrightness|void|(Image *image, int brightness);|
ImageColorReplace|void|(Image *image, Color color, Color replace);|
GenImageColor|Image|(int width, int height, Color color);|
GenImageGradientV|Image|(int width, int height, Color top, Color bottom);|
GenImageGradientH|Image|(int width, int height, Color left, Color right);|
GenImageGradientRadial|Image|(int width, int height, float density, Color inner, Color outer);|
GenImageChecked|Image|(int width, int height, int checksX, int checksY, Color col1, Color col2);|
GenImageWhiteNoise|Image|(int width, int height, float factor);|
GenImagePerlinNoise|Image|(int width, int height, int offsetX, int offsetY, float scale);|
GenImageCellular|Image|(int width, int height, int tileSize);|
GenTextureMipmaps|void|(Texture2D *texture);|
SetTextureFilter|void|(Texture2D texture, int filterMode);|
SetTextureWrap|void|(Texture2D texture, int wrapMode);|
DrawTexture|void|(Texture2D texture, int posX, int posY, Color tint);|
DrawTextureV|void|(Texture2D texture, Vector2 position, Color tint);|
DrawTextureEx|void|(Texture2D texture, Vector2 position, float rotation, float scale, Color tint);|
DrawTextureRec|void|(Texture2D texture, Rectangle sourceRec, Vector2 position, Color tint);|
DrawTextureQuad|void|(Texture2D texture, Vector2 tiling, Vector2 offset, Rectangle quad, Color tint);|
DrawTexturePro|void|(Texture2D texture, Rectangle sourceRec, Rectangle destRec, Vector2 origin, float rotation, Color tint);|
DrawTextureNPatch|void|(Texture2D texture, NPatchInfo nPatchInfo, Rectangle destRec, Vector2 origin, float rotation, Color tint);|
GetFontDefault|Font|(void);|
LoadFont|Font|(const char *fileName);|
LoadFontEx|Font|(const char *fileName, int fontSize, int *fontChars, int charsCount);|
LoadFontFromImage|Font|(Image image, Color key, int firstChar);|
LoadFontData|CharInfo *|(const char *fileName, int fontSize, int *fontChars, int charsCount, int type);|
GenImageFontAtlas|Image|(CharInfo *chars, int charsCount, int fontSize, int padding, int packMethod);|
UnloadFont|void|(Font font);|
DrawFPS|void|(int posX, int posY);|
DrawText|void|(const char *text, int posX, int posY, int fontSize, Color color);|
DrawTextEx|void|(Font font, const char *text, Vector2 position, float fontSize, float spacing, Color tint);|
DrawTextRec|void|(Font font, const char *text, Rectangle rec, float fontSize, float spacing, bool wordWrap, Color tint);|
DrawTextRecEx|void|(Font font, const char *text, Rectangle rec, float fontSize, float spacing, bool wordWrap, Color tint, int selectStart, int selectLength, Color selectText, Color selectBack);|
MeasureText|int|(const char *text, int fontSize);|
MeasureTextEx|Vector2|(Font font, const char *text, float fontSize, float spacing);|
GetGlyphIndex|int|(Font font, int character);|
TextIsEqual|bool|(const char *text1, const char *text2);|
TextLength|unsigned int|(const char *text);|
TextFormat|const char *|(const char *text, ...);|
TextSubtext|const char *|(const char *text, int position, int length);|
TextReplace|const char *|(char *text, const char *replace, const char *by);|
TextInsert|const char *|(const char *text, const char *insert, int position);|
TextJoin|const char *|(const char **textList, int count, const char *delimiter);|
TextSplit|const char **|(const char *text, char delimiter, int *count);|
TextAppend|void|(char *text, const char *append, int *position);|
TextFindIndex|int|(const char *text, const char *find);|
TextToUpper|const char *|(const char *text);|
TextToLower|const char *|(const char *text);|
TextToPascal|const char *|(const char *text);|
TextToInteger|int|(const char *text);|
DrawLine3D|void|(Vector3 startPos, Vector3 endPos, Color color);|
DrawCircle3D|void|(Vector3 center, float radius, Vector3 rotationAxis, float rotationAngle, Color color);|
DrawCube|void|(Vector3 position, float width, float height, float length, Color color);|
DrawCubeV|void|(Vector3 position, Vector3 size, Color color);|
DrawCubeWires|void|(Vector3 position, float width, float height, float length, Color color);|
DrawCubeWiresV|void|(Vector3 position, Vector3 size, Color color);|
DrawCubeTexture|void|(Texture2D texture, Vector3 position, float width, float height, float length, Color color);|
DrawSphere|void|(Vector3 centerPos, float radius, Color color);|
DrawSphereEx|void|(Vector3 centerPos, float radius, int rings, int slices, Color color);|
DrawSphereWires|void|(Vector3 centerPos, float radius, int rings, int slices, Color color);|
DrawCylinder|void|(Vector3 position, float radiusTop, float radiusBottom, float height, int slices, Color color);|
DrawCylinderWires|void|(Vector3 position, float radiusTop, float radiusBottom, float height, int slices, Color color);|
DrawPlane|void|(Vector3 centerPos, Vector2 size, Color color);|
DrawRay|void|(Ray ray, Color color);|
DrawGrid|void|(int slices, float spacing);|
DrawGizmo|void|(Vector3 position);|
LoadModel|Model|(const char *fileName);|
LoadModelFromMesh|Model|(Mesh mesh);|
UnloadModel|void|(Model model);|
LoadMeshes|Mesh *|(const char *fileName, int *meshCount);|
ExportMesh|void|(Mesh mesh, const char *fileName);|
UnloadMesh|void|(Mesh *mesh);|
LoadMaterials|Material *|(const char *fileName, int *materialCount);|
LoadMaterialDefault|Material|(void);|
UnloadMaterial|void|(Material material);|
SetMaterialTexture|void|(Material *material, int mapType, Texture2D texture);|
SetModelMeshMaterial|void|(Model *model, int meshId, int materialId);|
LoadModelAnimations|ModelAnimation *|(const char *fileName, int *animsCount);|
UpdateModelAnimation|void|(Model model, ModelAnimation anim, int frame);|
UnloadModelAnimation|void|(ModelAnimation anim);|
IsModelAnimationValid|bool|(Model model, ModelAnimation anim);|
GenMeshPoly|Mesh|(int sides, float radius);|
GenMeshPlane|Mesh|(float width, float length, int resX, int resZ);|
GenMeshCube|Mesh|(float width, float height, float length);|
GenMeshSphere|Mesh|(float radius, int rings, int slices);|
GenMeshHemiSphere|Mesh|(float radius, int rings, int slices);|
GenMeshCylinder|Mesh|(float radius, float height, int slices);|
GenMeshTorus|Mesh|(float radius, float size, int radSeg, int sides);|
GenMeshKnot|Mesh|(float radius, float size, int radSeg, int sides);|
GenMeshHeightmap|Mesh|(Image heightmap, Vector3 size);|
GenMeshCubicmap|Mesh|(Image cubicmap, Vector3 cubeSize);|
GetMeshBoundingBox|BoundingBox|(Mesh mesh);|
MeshTangents|void|(Mesh *mesh);|
MeshBinormals|void|(Mesh *mesh);|
DrawModel|void|(Model model, Vector3 position, float scale, Color tint);|
DrawModelEx|void|(Model model, Vector3 position, Vector3 rotationAxis, float rotationAngle, Vector3 scale, Color tint);|
DrawModelWires|void|(Model model, Vector3 position, float scale, Color tint);|
DrawModelWiresEx|void|(Model model, Vector3 position, Vector3 rotationAxis, float rotationAngle, Vector3 scale, Color tint);|
DrawBoundingBox|void|(BoundingBox box, Color color);|
DrawBillboard|void|(Camera camera, Texture2D texture, Vector3 center, float size, Color tint);|
DrawBillboardRec|void|(Camera camera, Texture2D texture, Rectangle sourceRec, Vector3 center, float size, Color tint);|
CheckCollisionSpheres|bool|(Vector3 centerA, float radiusA, Vector3 centerB, float radiusB);|
CheckCollisionBoxes|bool|(BoundingBox box1, BoundingBox box2);|
CheckCollisionBoxSphere|bool|(BoundingBox box, Vector3 centerSphere, float radiusSphere);|
CheckCollisionRaySphere|bool|(Ray ray, Vector3 spherePosition, float sphereRadius);|
CheckCollisionRaySphereEx|bool|(Ray ray, Vector3 spherePosition, float sphereRadius, Vector3 *collisionPoint);|
CheckCollisionRayBox|bool|(Ray ray, BoundingBox box);|
GetCollisionRayModel|RayHitInfo|(Ray ray, Model *model);|
GetCollisionRayTriangle|RayHitInfo|(Ray ray, Vector3 p1, Vector3 p2, Vector3 p3);|
GetCollisionRayGround|RayHitInfo|(Ray ray, float groundHeight);|
LoadText|char *|(const char *fileName);|
LoadShader|Shader|(const char *vsFileName, const char *fsFileName);|
LoadShaderCode|Shader|(char *vsCode, char *fsCode);|
UnloadShader|void|(Shader shader);|
GetShaderDefault|Shader|(void);|
GetTextureDefault|Texture2D|(void);|
GetShaderLocation|int|(Shader shader, const char *uniformName);|
SetShaderValue|void|(Shader shader, int uniformLoc, const void *value, int uniformType);|
SetShaderValueV|void|(Shader shader, int uniformLoc, const void *value, int uniformType, int count);|
SetShaderValueMatrix|void|(Shader shader, int uniformLoc, Matrix mat);|
SetShaderValueTexture|void|(Shader shader, int uniformLoc, Texture2D texture);|
SetMatrixProjection|void|(Matrix proj);|
SetMatrixModelview|void|(Matrix view);|
GetMatrixModelview|Matrix|();|
BeginShaderMode|void|(Shader shader);|
EndShaderMode|void|(void);|
BeginBlendMode|void|(int mode);|
EndBlendMode|void|(void);|
BeginScissorMode|void|(int x, int y, int width, int height);|
EndScissorMode|void|(void);|
InitVrSimulator|void|(void);|
CloseVrSimulator|void|(void);|
UpdateVrTracking|void|(Camera *camera);|
SetVrConfiguration|void|(VrDeviceInfo info, Shader distortion);|
IsVrSimulatorReady|bool|(void);|
ToggleVrMode|void|(void);|
BeginVrDrawing|void|(void);|
EndVrDrawing|void|(void);|
InitAudioDevice|void|(void);|
CloseAudioDevice|void|(void);|
IsAudioDeviceReady|bool|(void);|
SetMasterVolume|void|(float volume);|
LoadWave|Wave|(const char *fileName);|
LoadWaveEx|Wave|(void *data, int sampleCount, int sampleRate, int sampleSize, int channels);|
LoadSound|Sound|(const char *fileName);|
LoadSoundFromWave|Sound|(Wave wave);|
UpdateSound|void|(Sound sound, const void *data, int samplesCount);|
UnloadWave|void|(Wave wave);|
UnloadSound|void|(Sound sound);|
ExportWave|void|(Wave wave, const char *fileName);|
ExportWaveAsCode|void|(Wave wave, const char *fileName);|
PlaySound|void|(Sound sound);|
PauseSound|void|(Sound sound);|
ResumeSound|void|(Sound sound);|
StopSound|void|(Sound sound);|
IsSoundPlaying|bool|(Sound sound);|
SetSoundVolume|void|(Sound sound, float volume);|
SetSoundPitch|void|(Sound sound, float pitch);|
WaveFormat|void|(Wave *wave, int sampleRate, int sampleSize, int channels);|
WaveCopy|Wave|(Wave wave);|
WaveCrop|void|(Wave *wave, int initSample, int finalSample);|
GetWaveData|float *|(Wave wave);|
LoadMusicStream|Music|(const char *fileName);|
UnloadMusicStream|void|(Music music);|
PlayMusicStream|void|(Music music);|
UpdateMusicStream|void|(Music music);|
StopMusicStream|void|(Music music);|
PauseMusicStream|void|(Music music);|
ResumeMusicStream|void|(Music music);|
IsMusicPlaying|bool|(Music music);|
SetMusicVolume|void|(Music music, float volume);|
SetMusicPitch|void|(Music music, float pitch);|
SetMusicLoopCount|void|(Music music, int count);|
GetMusicTimeLength|float|(Music music);|
GetMusicTimePlayed|float|(Music music);|
InitAudioStream|AudioStream|(unsigned int sampleRate, unsigned int sampleSize, unsigned int channels);|
UpdateAudioStream|void|(AudioStream stream, const void *data, int samplesCount);|
CloseAudioStream|void|(AudioStream stream);|
IsAudioBufferProcessed|bool|(AudioStream stream);|
PlayAudioStream|void|(AudioStream stream);|
PauseAudioStream|void|(AudioStream stream);|
ResumeAudioStream|void|(AudioStream stream);|
IsAudioStreamPlaying|bool|(AudioStream stream);|
StopAudioStream|void|(AudioStream stream);|
SetAudioStreamVolume|void|(AudioStream stream, float volume);|
SetAudioStreamPitch|void|(AudioStream stream, float pitch);|
Vector2|struct||
Vector3|struct||
Vector4|struct||
Quaternion|struct||
Matrix|struct||
Color|struct||
Rectangle|struct||
Image|struct||
Texture|struct||
RenderTexture|struct||
NPatchInfo|struct||
CharInfo|struct||
Font|struct||
Camera|struct||
Camera2D|struct||
Mesh|struct||
Shader|struct||
MaterialMap|struct||
Material|struct||
Model|struct||
Transform|struct||
BoneInfo|struct||
ModelAnimation|struct||
Ray|struct||
RayHitInfo|struct||
BoundingBox|struct||
Wave|struct||
Sound|struct||
Music|struct||
AudioStream|struct||
VrDeviceInfo|struct||
LIGHTGRAY|#define||
GRAY|#define||
DARKGRAY|#define||
YELLOW|#define||
GOLD|#define||
ORANGE|#define||
PINK|#define||
RED|#define||
MAROON|#define||
GREEN|#define||
LIME|#define||
DARKGREEN|#define||
SKYBLUE|#define||
BLUE|#define||
DARKBLUE|#define||
PURPLE|#define||
VIOLET|#define||
DARKPURPLE|#define||
BEIGE|#define||
BROWN|#define||
DARKBROWN|#define||
WHITE|#define||
BLACK|#define||
BLANK|#define||
MAGENTA|#define||
RAYWHITE|#define||
