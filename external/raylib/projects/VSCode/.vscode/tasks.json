{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build debug",
            "type": "process",
            "command": "make",
            "args": [
                "PLATFORM=PLATFORM_DESKTOP",
                "BUILD_MODE=DEBUG",
                "PROJECT_NAME=${fileBasenameNoExtension}",
                "OBJS=${fileBasenameNoExtension}.c"
            ],
            "windows": {
                "command": "mingw32-make.exe",
                "args": [
                    "RAYLIB_PATH=C:/raylib/raylib",
                    "PROJECT_NAME=${fileBasenameNoExtension}",
                    "OBJS=${fileBasenameNoExtension}.c",
                    "BUILD_MODE=DEBUG"
                ],
            },
            "osx": {
                "args": [
                    "RAYLIB_PATH=<path_to_raylib>/raylib",
                    "PROJECT_NAME=${fileBasenameNoExtension}",
                    "OBJS=${fileBasenameNoExtension}.c",
                    "BUILD_MODE=DEBUG"
                ],
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": [
                "$gcc"
            ]
        },
        {
            "label": "build release",
            "type": "process",
            "command": "make",
            "args": [
                "PLATFORM=PLATFORM_DESKTOP",
                "PROJECT_NAME=${fileBasenameNoExtension}",
                "OBJS=${fileBasenameNoExtension}.c"
            ],
            "windows": {
                "command": "mingw32-make.exe",
                "args": [
                    "RAYLIB_PATH=C:/raylib/raylib",
                    "PROJECT_NAME=${fileBasenameNoExtension}",
                    "OBJS=${fileBasenameNoExtension}.c"
                ],
            },
            "osx": {
                "args": [
                    "RAYLIB_PATH=<path_to_raylib>/raylib",
                    "PROJECT_NAME=${fileBasenameNoExtension}",
                    "OBJS=${fileBasenameNoExtension}.c"
                ],
            },
            "group": "build",
            "problemMatcher": [
                "$gcc"
            ]
        }
    ]
}