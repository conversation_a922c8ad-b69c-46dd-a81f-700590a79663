{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
        "version": "0.2.0",
        "configurations": [
          {
            "name": "Debug",
            "type": "cppdbg",
            "request": "launch",
            "program": "${workspaceFolder}/${fileBasenameNoExtension}",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "externalConsole": false,
            "MIMode": "gdb",
            "setupCommands": [
              {
                "description": "Enable pretty-printing for gdb",
                "text": "-enable-pretty-printing",
                "ignoreFailures": false
              }
            ],
            "windows": {
              "miDebuggerPath": "C:/raylib/w64devkit/bin/gdb.exe",
            },
            "osx": {
              "MIMode": "lldb"
            },
            "linux": {
              "miDebuggerPath": "/usr/bin/gdb",
            },
            "preLaunchTask": "build debug"
          },
          {
            "name": "Run",
            "type": "cppdbg",
            "request": "launch",
            "args": [],
            "stopAtEntry": false,
            "cwd": "${workspaceFolder}",
            "environment": [],
            "externalConsole": false,
            "program": "${workspaceFolder}/${fileBasenameNoExtension}",
            "MIMode": "gdb",
            "windows": {
              "program": "${workspaceFolder}/${fileBasenameNoExtension}.exe",
              "miDebuggerPath": "C:/raylib/w64devkit/bin/gdb.exe"
            },
            "osx": {
              "MIMode": "lldb"
            },
            "linux": {
              "miDebuggerPath": "/usr/bin/gdb"
            },
            "preLaunchTask": "build release",
          }
        ]
      }
