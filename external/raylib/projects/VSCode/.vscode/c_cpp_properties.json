{"configurations": [{"name": "Win32", "includePath": ["C:/raylib/raylib/src/**", "${workspaceFolder}/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "GRAPHICS_API_OPENGL_33", "PLATFORM_DESKTOP"], "compilerPath": "C:/raylib/w64devkit/bin/gcc.exe", "cStandard": "c99", "cppStandard": "c++14", "intelliSenseMode": "gcc-x64"}, {"name": "<PERSON>", "includePath": ["<path_to_raylib>/src/**", "${workspaceFolder}/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "GRAPHICS_API_OPENGL_33", "PLATFORM_DESKTOP"], "macFrameworkPath": ["/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX10.13.sdk/System/Library/Frameworks"], "compilerPath": "/usr/bin/clang", "cStandard": "c11", "cppStandard": "c++14", "intelliSenseMode": "clang-x64"}, {"name": "Linux", "includePath": ["<path_to_raylib>/src/**", "${workspaceFolder}/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE", "GRAPHICS_API_OPENGL_33", "PLATFORM_DESKTOP"], "compilerPath": "/usr/bin/clang", "cStandard": "c11", "cppStandard": "c++14", "intelliSenseMode": "clang-x64"}], "version": 4}