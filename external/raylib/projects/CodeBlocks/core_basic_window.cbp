<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="raylib-game-template" />
		<Option execution_dir="." />
		<Option pch_mode="2" />
		<Option compiler="gcc" />
		<Build>
			<Target title="Debug (Mac)">
				<Option platforms="Mac;" />
				<Option output="bin/Debug/raylib-game-template" prefix_auto="1" extension_auto="1" />
				<Option working_dir="." />
				<Option object_output="obj/Debug/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-g" />
				</Compiler>
				<Linker>
					<Add library="raylib" />
	                <Add option="-framework OpenGL" />
	                <Add option="-framework Cocoa" />
	                <Add option="-framework IOKit" />
	                <Add option="-framework CoreAudio" />
	                <Add option="-framework CoreVideo" />
				</Linker>
			</Target>
			<Target title="Release (Mac)">
				<Option platforms="Mac;" />
				<Option output="bin/Release/raylib-game-template" prefix_auto="1" extension_auto="1" />
				<Option working_dir="." />
				<Option object_output="obj/Release/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-O2" />
				</Compiler>
				<Linker>
					<Add option="-s" />
					<Add library="raylib" />
	                <Add option="-framework OpenGL" />
	                <Add option="-framework Cocoa" />
	                <Add option="-framework IOKit" />
	                <Add option="-framework CoreAudio" />
	                <Add option="-framework CoreVideo" />
				</Linker>
			</Target>
			<Target title="Debug (Linux)">
				<Option platforms="Unix;" />
				<Option output="bin/Debug/raylib-game-template" prefix_auto="1" extension_auto="1" />
				<Option working_dir="." />
				<Option object_output="obj/Debug/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-g" />
				</Compiler>
				<Linker>
					<Add library="raylib" />
					<Add library="GL" />
					<Add library="m" />
					<Add library="pthread" />
					<Add library="dl" />
					<Add library="rt" />
					<Add library="X11" />
				</Linker>
			</Target>
			<Target title="Release (Linux)">
				<Option platforms="Unix;" />
				<Option output="bin/Release/raylib-game-template" prefix_auto="1" extension_auto="1" />
				<Option working_dir="." />
				<Option object_output="obj/Release/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-O2" />
				</Compiler>
				<Linker>
					<Add option="-s" />
					<Add library="raylib" />
					<Add library="GL" />
					<Add library="m" />
					<Add library="pthread" />
					<Add library="dl" />
					<Add library="rt" />
					<Add library="X11" />
				</Linker>
			</Target>
			<Target title="Debug (Windows)">
				<Option platforms="Windows;" />
				<Option output="bin/Debug/raylib-game-template" prefix_auto="1" extension_auto="1" />
				<Option working_dir="." />
				<Option object_output="obj/Debug/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-g" />
				</Compiler>
				<Linker>
					<Add library="raylib" />
					<Add library="opengl32" />
					<Add library="gdi32" />
					<Add library="winmm" />
					<Add option="-static" />
					<Add library="pthread" />
				</Linker>
			</Target>
			<Target title="Release (Windows)">
				<Option platforms="Windows;" />
				<Option output="bin/Release/raylib-game-template" prefix_auto="1" extension_auto="1" />
				<Option working_dir="." />
				<Option object_output="obj/Release/" />
				<Option type="1" />
				<Option compiler="gcc" />
				<Compiler>
					<Add option="-O2" />
				</Compiler>
				<Linker>
					<Add option="-s" />
					<Add library="raylib" />
					<Add library="opengl32" />
					<Add library="gdi32" />
					<Add library="winmm" />
					<Add option="-static" />
					<Add library="pthread" />
				</Linker>
			</Target>
		</Build>
		<Compiler>
			<Add option="-Wall" />
		</Compiler>
		<Unit filename="core_basic_window.c">
			<Option compilerVar="CC" />
		</Unit>
	</Project>
</CodeBlocks_project_file>
