# Sublime Text 3 project template

This is a project template to be used with [Sublime Text 3](https://www.sublimetext.com/).

Simply click on the `raylib.sublime-project` file to open it with Sublime Text.
Alternatively you can open Sublime Text first and click on the `Project -> Open Project`.

It comes with raylib.sublime-build. This file needs to be copied into the sublime packages folder for the user. On windows it can be found in `%AppData%\Sublime Text 3\Packages\User`.

Once done open the project and select `Tools -> Build System -> raylib`. 
To run press Ctrl + Shift + B and select which build you want to run.

For a full overview of build systems please check the [build systems guide](https://www.sublimetext.com/docs/3/build_systems.html).
