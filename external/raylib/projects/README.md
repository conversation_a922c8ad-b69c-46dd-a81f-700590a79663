## raylib PROJECT TEMPLATES

This folder contains raylib templates for some common IDEs.

IDE | Platform(s) | Source | Example(s)
----| ------------| :-------: | :-----:
[4coder](http://4coder.net/) | Windows | ❌ | ✔️
[Builder](https://wiki.gnome.org/Apps/Builder) | Linux | ❌ | ✔️
[CMake](https://cmake.org/) | Windows, Linux, macOS, Web | ✔️ | ✔️
[CodeBlocks](http://www.codeblocks.org/) | Windows, Linux, macOS | ❌ | ✔️
[Geany](https://www.geany.org/) | Windows, Linux | ✔️ | ✔️
[Notepad++](https://notepad-plus-plus.org/) | Windows | ✔️ | ✔️
[SublimeText](https://www.sublimetext.com/) | Windows, Linux, macOS | ✔️ | ✔️
[VS2019](https://www.visualstudio.com) | Windows | ✔️ | ✔️
[VSCode](https://code.visualstudio.com/) | Windows, macOS | ❌ | ✔️
scripts | Windows, Linux, macOS | ✔️ | ✔️

 *New IDEs config files are welcome!*
