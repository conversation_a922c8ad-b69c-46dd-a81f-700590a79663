# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/projects/cpp/external/raylib

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/projects/cpp/external/raylib/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /home/<USER>/projects/cpp/external/raylib/build && /usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /home/<USER>/projects/cpp/external/raylib/build && /usr/bin/cpack --config ./CPackSourceConfig.cmake /home/<USER>/projects/cpp/external/raylib/build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/projects/cpp/external/raylib/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/external/raylib/build/CMakeFiles /home/<USER>/projects/cpp/external/raylib/build/raylib//CMakeFiles/progress.marks
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 raylib/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/external/raylib/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 raylib/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 raylib/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 raylib/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
raylib/CMakeFiles/raylib.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 raylib/CMakeFiles/raylib.dir/rule
.PHONY : raylib/CMakeFiles/raylib.dir/rule

# Convenience name for target.
raylib: raylib/CMakeFiles/raylib.dir/rule
.PHONY : raylib

# fast build rule for target.
raylib/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/build
.PHONY : raylib/fast

raudio.o: raudio.c.o
.PHONY : raudio.o

# target to build an object file
raudio.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/raudio.c.o
.PHONY : raudio.c.o

raudio.i: raudio.c.i
.PHONY : raudio.i

# target to preprocess a source file
raudio.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/raudio.c.i
.PHONY : raudio.c.i

raudio.s: raudio.c.s
.PHONY : raudio.s

# target to generate assembly for a file
raudio.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/raudio.c.s
.PHONY : raudio.c.s

rcore.o: rcore.c.o
.PHONY : rcore.o

# target to build an object file
rcore.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rcore.c.o
.PHONY : rcore.c.o

rcore.i: rcore.c.i
.PHONY : rcore.i

# target to preprocess a source file
rcore.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rcore.c.i
.PHONY : rcore.c.i

rcore.s: rcore.c.s
.PHONY : rcore.s

# target to generate assembly for a file
rcore.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rcore.c.s
.PHONY : rcore.c.s

rmodels.o: rmodels.c.o
.PHONY : rmodels.o

# target to build an object file
rmodels.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rmodels.c.o
.PHONY : rmodels.c.o

rmodels.i: rmodels.c.i
.PHONY : rmodels.i

# target to preprocess a source file
rmodels.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rmodels.c.i
.PHONY : rmodels.c.i

rmodels.s: rmodels.c.s
.PHONY : rmodels.s

# target to generate assembly for a file
rmodels.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rmodels.c.s
.PHONY : rmodels.c.s

rshapes.o: rshapes.c.o
.PHONY : rshapes.o

# target to build an object file
rshapes.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rshapes.c.o
.PHONY : rshapes.c.o

rshapes.i: rshapes.c.i
.PHONY : rshapes.i

# target to preprocess a source file
rshapes.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rshapes.c.i
.PHONY : rshapes.c.i

rshapes.s: rshapes.c.s
.PHONY : rshapes.s

# target to generate assembly for a file
rshapes.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rshapes.c.s
.PHONY : rshapes.c.s

rtext.o: rtext.c.o
.PHONY : rtext.o

# target to build an object file
rtext.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rtext.c.o
.PHONY : rtext.c.o

rtext.i: rtext.c.i
.PHONY : rtext.i

# target to preprocess a source file
rtext.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rtext.c.i
.PHONY : rtext.c.i

rtext.s: rtext.c.s
.PHONY : rtext.s

# target to generate assembly for a file
rtext.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rtext.c.s
.PHONY : rtext.c.s

rtextures.o: rtextures.c.o
.PHONY : rtextures.o

# target to build an object file
rtextures.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rtextures.c.o
.PHONY : rtextures.c.o

rtextures.i: rtextures.c.i
.PHONY : rtextures.i

# target to preprocess a source file
rtextures.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rtextures.c.i
.PHONY : rtextures.c.i

rtextures.s: rtextures.c.s
.PHONY : rtextures.s

# target to generate assembly for a file
rtextures.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/rtextures.c.s
.PHONY : rtextures.c.s

utils.o: utils.c.o
.PHONY : utils.o

# target to build an object file
utils.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/utils.c.o
.PHONY : utils.c.o

utils.i: utils.c.i
.PHONY : utils.i

# target to preprocess a source file
utils.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/utils.c.i
.PHONY : utils.c.i

utils.s: utils.c.s
.PHONY : utils.s

# target to generate assembly for a file
utils.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/utils.c.s
.PHONY : utils.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... raylib"
	@echo "... raudio.o"
	@echo "... raudio.i"
	@echo "... raudio.s"
	@echo "... rcore.o"
	@echo "... rcore.i"
	@echo "... rcore.s"
	@echo "... rmodels.o"
	@echo "... rmodels.i"
	@echo "... rmodels.s"
	@echo "... rshapes.o"
	@echo "... rshapes.i"
	@echo "... rshapes.s"
	@echo "... rtext.o"
	@echo "... rtext.i"
	@echo "... rtext.s"
	@echo "... rtextures.o"
	@echo "... rtextures.i"
	@echo "... rtextures.s"
	@echo "... utils.o"
	@echo "... utils.i"
	@echo "... utils.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

