
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/projects/cpp/external/raylib/src/raudio.c" "raylib/CMakeFiles/raylib.dir/raudio.c.o" "gcc" "raylib/CMakeFiles/raylib.dir/raudio.c.o.d"
  "/home/<USER>/projects/cpp/external/raylib/src/rcore.c" "raylib/CMakeFiles/raylib.dir/rcore.c.o" "gcc" "raylib/CMakeFiles/raylib.dir/rcore.c.o.d"
  "/home/<USER>/projects/cpp/external/raylib/src/rmodels.c" "raylib/CMakeFiles/raylib.dir/rmodels.c.o" "gcc" "raylib/CMakeFiles/raylib.dir/rmodels.c.o.d"
  "/home/<USER>/projects/cpp/external/raylib/src/rshapes.c" "raylib/CMakeFiles/raylib.dir/rshapes.c.o" "gcc" "raylib/CMakeFiles/raylib.dir/rshapes.c.o.d"
  "/home/<USER>/projects/cpp/external/raylib/src/rtext.c" "raylib/CMakeFiles/raylib.dir/rtext.c.o" "gcc" "raylib/CMakeFiles/raylib.dir/rtext.c.o.d"
  "/home/<USER>/projects/cpp/external/raylib/src/rtextures.c" "raylib/CMakeFiles/raylib.dir/rtextures.c.o" "gcc" "raylib/CMakeFiles/raylib.dir/rtextures.c.o.d"
  "/home/<USER>/projects/cpp/external/raylib/src/utils.c" "raylib/CMakeFiles/raylib.dir/utils.c.o" "gcc" "raylib/CMakeFiles/raylib.dir/utils.c.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/projects/cpp/external/raylib/build/raylib/external/glfw/src/CMakeFiles/glfw.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
