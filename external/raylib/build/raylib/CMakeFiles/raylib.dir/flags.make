# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile C with /usr/bin/cc
C_DEFINES = -DGRAPHICS_API_OPENGL_33 -DPLATFORM_DESKTOP

C_INCLUDES = -I/home/<USER>/projects/cpp/external/raylib/src -isystem /home/<USER>/projects/cpp/external/raylib/src/external/glfw/include

C_FLAGS = -fno-strict-aliasing -Werror=implicit-function-declaration -Werror=pointer-arith  -g -std=gnu99

