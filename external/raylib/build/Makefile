# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/projects/cpp/external/raylib

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/projects/cpp/external/raylib/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	/usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	/usr/bin/cpack --config ./CPackSourceConfig.cmake /home/<USER>/projects/cpp/external/raylib/build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/external/raylib/build/CMakeFiles /home/<USER>/projects/cpp/external/raylib/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/external/raylib/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named raylib

# Build rule for target.
raylib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 raylib
.PHONY : raylib

# fast build rule for target.
raylib/fast:
	$(MAKE) $(MAKESILENT) -f raylib/CMakeFiles/raylib.dir/build.make raylib/CMakeFiles/raylib.dir/build
.PHONY : raylib/fast

#=============================================================================
# Target rules for targets named glfw

# Build rule for target.
glfw: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 glfw
.PHONY : glfw

# fast build rule for target.
glfw/fast:
	$(MAKE) $(MAKESILENT) -f raylib/external/glfw/src/CMakeFiles/glfw.dir/build.make raylib/external/glfw/src/CMakeFiles/glfw.dir/build
.PHONY : glfw/fast

#=============================================================================
# Target rules for targets named update_mappings

# Build rule for target.
update_mappings: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 update_mappings
.PHONY : update_mappings

# fast build rule for target.
update_mappings/fast:
	$(MAKE) $(MAKESILENT) -f raylib/external/glfw/src/CMakeFiles/update_mappings.dir/build.make raylib/external/glfw/src/CMakeFiles/update_mappings.dir/build
.PHONY : update_mappings/fast

#=============================================================================
# Target rules for targets named audio_mixed_processor

# Build rule for target.
audio_mixed_processor: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 audio_mixed_processor
.PHONY : audio_mixed_processor

# fast build rule for target.
audio_mixed_processor/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_mixed_processor.dir/build.make examples/CMakeFiles/audio_mixed_processor.dir/build
.PHONY : audio_mixed_processor/fast

#=============================================================================
# Target rules for targets named audio_module_playing

# Build rule for target.
audio_module_playing: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 audio_module_playing
.PHONY : audio_module_playing

# fast build rule for target.
audio_module_playing/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_module_playing.dir/build.make examples/CMakeFiles/audio_module_playing.dir/build
.PHONY : audio_module_playing/fast

#=============================================================================
# Target rules for targets named audio_music_stream

# Build rule for target.
audio_music_stream: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 audio_music_stream
.PHONY : audio_music_stream

# fast build rule for target.
audio_music_stream/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_music_stream.dir/build.make examples/CMakeFiles/audio_music_stream.dir/build
.PHONY : audio_music_stream/fast

#=============================================================================
# Target rules for targets named audio_raw_stream

# Build rule for target.
audio_raw_stream: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 audio_raw_stream
.PHONY : audio_raw_stream

# fast build rule for target.
audio_raw_stream/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_raw_stream.dir/build.make examples/CMakeFiles/audio_raw_stream.dir/build
.PHONY : audio_raw_stream/fast

#=============================================================================
# Target rules for targets named audio_sound_loading

# Build rule for target.
audio_sound_loading: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 audio_sound_loading
.PHONY : audio_sound_loading

# fast build rule for target.
audio_sound_loading/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_sound_loading.dir/build.make examples/CMakeFiles/audio_sound_loading.dir/build
.PHONY : audio_sound_loading/fast

#=============================================================================
# Target rules for targets named audio_stream_effects

# Build rule for target.
audio_stream_effects: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 audio_stream_effects
.PHONY : audio_stream_effects

# fast build rule for target.
audio_stream_effects/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_stream_effects.dir/build.make examples/CMakeFiles/audio_stream_effects.dir/build
.PHONY : audio_stream_effects/fast

#=============================================================================
# Target rules for targets named core_2d_camera

# Build rule for target.
core_2d_camera: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_2d_camera
.PHONY : core_2d_camera

# fast build rule for target.
core_2d_camera/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera.dir/build.make examples/CMakeFiles/core_2d_camera.dir/build
.PHONY : core_2d_camera/fast

#=============================================================================
# Target rules for targets named core_2d_camera_mouse_zoom

# Build rule for target.
core_2d_camera_mouse_zoom: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_2d_camera_mouse_zoom
.PHONY : core_2d_camera_mouse_zoom

# fast build rule for target.
core_2d_camera_mouse_zoom/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/build.make examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/build
.PHONY : core_2d_camera_mouse_zoom/fast

#=============================================================================
# Target rules for targets named core_2d_camera_platformer

# Build rule for target.
core_2d_camera_platformer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_2d_camera_platformer
.PHONY : core_2d_camera_platformer

# fast build rule for target.
core_2d_camera_platformer/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera_platformer.dir/build.make examples/CMakeFiles/core_2d_camera_platformer.dir/build
.PHONY : core_2d_camera_platformer/fast

#=============================================================================
# Target rules for targets named core_3d_camera_first_person

# Build rule for target.
core_3d_camera_first_person: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_3d_camera_first_person
.PHONY : core_3d_camera_first_person

# fast build rule for target.
core_3d_camera_first_person/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_first_person.dir/build.make examples/CMakeFiles/core_3d_camera_first_person.dir/build
.PHONY : core_3d_camera_first_person/fast

#=============================================================================
# Target rules for targets named core_3d_camera_free

# Build rule for target.
core_3d_camera_free: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_3d_camera_free
.PHONY : core_3d_camera_free

# fast build rule for target.
core_3d_camera_free/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_free.dir/build.make examples/CMakeFiles/core_3d_camera_free.dir/build
.PHONY : core_3d_camera_free/fast

#=============================================================================
# Target rules for targets named core_3d_camera_mode

# Build rule for target.
core_3d_camera_mode: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_3d_camera_mode
.PHONY : core_3d_camera_mode

# fast build rule for target.
core_3d_camera_mode/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_mode.dir/build.make examples/CMakeFiles/core_3d_camera_mode.dir/build
.PHONY : core_3d_camera_mode/fast

#=============================================================================
# Target rules for targets named core_3d_picking

# Build rule for target.
core_3d_picking: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_3d_picking
.PHONY : core_3d_picking

# fast build rule for target.
core_3d_picking/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_picking.dir/build.make examples/CMakeFiles/core_3d_picking.dir/build
.PHONY : core_3d_picking/fast

#=============================================================================
# Target rules for targets named core_basic_screen_manager

# Build rule for target.
core_basic_screen_manager: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_basic_screen_manager
.PHONY : core_basic_screen_manager

# fast build rule for target.
core_basic_screen_manager/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_screen_manager.dir/build.make examples/CMakeFiles/core_basic_screen_manager.dir/build
.PHONY : core_basic_screen_manager/fast

#=============================================================================
# Target rules for targets named core_basic_window

# Build rule for target.
core_basic_window: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_basic_window
.PHONY : core_basic_window

# fast build rule for target.
core_basic_window/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_window.dir/build.make examples/CMakeFiles/core_basic_window.dir/build
.PHONY : core_basic_window/fast

#=============================================================================
# Target rules for targets named core_basic_window_web

# Build rule for target.
core_basic_window_web: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_basic_window_web
.PHONY : core_basic_window_web

# fast build rule for target.
core_basic_window_web/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_window_web.dir/build.make examples/CMakeFiles/core_basic_window_web.dir/build
.PHONY : core_basic_window_web/fast

#=============================================================================
# Target rules for targets named core_custom_frame_control

# Build rule for target.
core_custom_frame_control: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_custom_frame_control
.PHONY : core_custom_frame_control

# fast build rule for target.
core_custom_frame_control/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_custom_frame_control.dir/build.make examples/CMakeFiles/core_custom_frame_control.dir/build
.PHONY : core_custom_frame_control/fast

#=============================================================================
# Target rules for targets named core_custom_logging

# Build rule for target.
core_custom_logging: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_custom_logging
.PHONY : core_custom_logging

# fast build rule for target.
core_custom_logging/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_custom_logging.dir/build.make examples/CMakeFiles/core_custom_logging.dir/build
.PHONY : core_custom_logging/fast

#=============================================================================
# Target rules for targets named core_drop_files

# Build rule for target.
core_drop_files: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_drop_files
.PHONY : core_drop_files

# fast build rule for target.
core_drop_files/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_drop_files.dir/build.make examples/CMakeFiles/core_drop_files.dir/build
.PHONY : core_drop_files/fast

#=============================================================================
# Target rules for targets named core_input_gamepad

# Build rule for target.
core_input_gamepad: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_input_gamepad
.PHONY : core_input_gamepad

# fast build rule for target.
core_input_gamepad/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_gamepad.dir/build.make examples/CMakeFiles/core_input_gamepad.dir/build
.PHONY : core_input_gamepad/fast

#=============================================================================
# Target rules for targets named core_input_gestures

# Build rule for target.
core_input_gestures: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_input_gestures
.PHONY : core_input_gestures

# fast build rule for target.
core_input_gestures/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_gestures.dir/build.make examples/CMakeFiles/core_input_gestures.dir/build
.PHONY : core_input_gestures/fast

#=============================================================================
# Target rules for targets named core_input_keys

# Build rule for target.
core_input_keys: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_input_keys
.PHONY : core_input_keys

# fast build rule for target.
core_input_keys/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_keys.dir/build.make examples/CMakeFiles/core_input_keys.dir/build
.PHONY : core_input_keys/fast

#=============================================================================
# Target rules for targets named core_input_mouse

# Build rule for target.
core_input_mouse: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_input_mouse
.PHONY : core_input_mouse

# fast build rule for target.
core_input_mouse/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_mouse.dir/build.make examples/CMakeFiles/core_input_mouse.dir/build
.PHONY : core_input_mouse/fast

#=============================================================================
# Target rules for targets named core_input_mouse_wheel

# Build rule for target.
core_input_mouse_wheel: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_input_mouse_wheel
.PHONY : core_input_mouse_wheel

# fast build rule for target.
core_input_mouse_wheel/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_mouse_wheel.dir/build.make examples/CMakeFiles/core_input_mouse_wheel.dir/build
.PHONY : core_input_mouse_wheel/fast

#=============================================================================
# Target rules for targets named core_input_multitouch

# Build rule for target.
core_input_multitouch: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_input_multitouch
.PHONY : core_input_multitouch

# fast build rule for target.
core_input_multitouch/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_multitouch.dir/build.make examples/CMakeFiles/core_input_multitouch.dir/build
.PHONY : core_input_multitouch/fast

#=============================================================================
# Target rules for targets named core_loading_thread

# Build rule for target.
core_loading_thread: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_loading_thread
.PHONY : core_loading_thread

# fast build rule for target.
core_loading_thread/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_loading_thread.dir/build.make examples/CMakeFiles/core_loading_thread.dir/build
.PHONY : core_loading_thread/fast

#=============================================================================
# Target rules for targets named core_random_values

# Build rule for target.
core_random_values: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_random_values
.PHONY : core_random_values

# fast build rule for target.
core_random_values/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_random_values.dir/build.make examples/CMakeFiles/core_random_values.dir/build
.PHONY : core_random_values/fast

#=============================================================================
# Target rules for targets named core_scissor_test

# Build rule for target.
core_scissor_test: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_scissor_test
.PHONY : core_scissor_test

# fast build rule for target.
core_scissor_test/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_scissor_test.dir/build.make examples/CMakeFiles/core_scissor_test.dir/build
.PHONY : core_scissor_test/fast

#=============================================================================
# Target rules for targets named core_smooth_pixelperfect

# Build rule for target.
core_smooth_pixelperfect: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_smooth_pixelperfect
.PHONY : core_smooth_pixelperfect

# fast build rule for target.
core_smooth_pixelperfect/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_smooth_pixelperfect.dir/build.make examples/CMakeFiles/core_smooth_pixelperfect.dir/build
.PHONY : core_smooth_pixelperfect/fast

#=============================================================================
# Target rules for targets named core_split_screen

# Build rule for target.
core_split_screen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_split_screen
.PHONY : core_split_screen

# fast build rule for target.
core_split_screen/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_split_screen.dir/build.make examples/CMakeFiles/core_split_screen.dir/build
.PHONY : core_split_screen/fast

#=============================================================================
# Target rules for targets named core_storage_values

# Build rule for target.
core_storage_values: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_storage_values
.PHONY : core_storage_values

# fast build rule for target.
core_storage_values/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_storage_values.dir/build.make examples/CMakeFiles/core_storage_values.dir/build
.PHONY : core_storage_values/fast

#=============================================================================
# Target rules for targets named core_vr_simulator

# Build rule for target.
core_vr_simulator: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_vr_simulator
.PHONY : core_vr_simulator

# fast build rule for target.
core_vr_simulator/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_vr_simulator.dir/build.make examples/CMakeFiles/core_vr_simulator.dir/build
.PHONY : core_vr_simulator/fast

#=============================================================================
# Target rules for targets named core_window_flags

# Build rule for target.
core_window_flags: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_window_flags
.PHONY : core_window_flags

# fast build rule for target.
core_window_flags/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_flags.dir/build.make examples/CMakeFiles/core_window_flags.dir/build
.PHONY : core_window_flags/fast

#=============================================================================
# Target rules for targets named core_window_letterbox

# Build rule for target.
core_window_letterbox: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_window_letterbox
.PHONY : core_window_letterbox

# fast build rule for target.
core_window_letterbox/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_letterbox.dir/build.make examples/CMakeFiles/core_window_letterbox.dir/build
.PHONY : core_window_letterbox/fast

#=============================================================================
# Target rules for targets named core_window_should_close

# Build rule for target.
core_window_should_close: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_window_should_close
.PHONY : core_window_should_close

# fast build rule for target.
core_window_should_close/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_should_close.dir/build.make examples/CMakeFiles/core_window_should_close.dir/build
.PHONY : core_window_should_close/fast

#=============================================================================
# Target rules for targets named core_world_screen

# Build rule for target.
core_world_screen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 core_world_screen
.PHONY : core_world_screen

# fast build rule for target.
core_world_screen/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_world_screen.dir/build.make examples/CMakeFiles/core_world_screen.dir/build
.PHONY : core_world_screen/fast

#=============================================================================
# Target rules for targets named models_animation

# Build rule for target.
models_animation: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_animation
.PHONY : models_animation

# fast build rule for target.
models_animation/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_animation.dir/build.make examples/CMakeFiles/models_animation.dir/build
.PHONY : models_animation/fast

#=============================================================================
# Target rules for targets named models_billboard

# Build rule for target.
models_billboard: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_billboard
.PHONY : models_billboard

# fast build rule for target.
models_billboard/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_billboard.dir/build.make examples/CMakeFiles/models_billboard.dir/build
.PHONY : models_billboard/fast

#=============================================================================
# Target rules for targets named models_box_collisions

# Build rule for target.
models_box_collisions: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_box_collisions
.PHONY : models_box_collisions

# fast build rule for target.
models_box_collisions/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_box_collisions.dir/build.make examples/CMakeFiles/models_box_collisions.dir/build
.PHONY : models_box_collisions/fast

#=============================================================================
# Target rules for targets named models_cubicmap

# Build rule for target.
models_cubicmap: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_cubicmap
.PHONY : models_cubicmap

# fast build rule for target.
models_cubicmap/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_cubicmap.dir/build.make examples/CMakeFiles/models_cubicmap.dir/build
.PHONY : models_cubicmap/fast

#=============================================================================
# Target rules for targets named models_draw_cube_texture

# Build rule for target.
models_draw_cube_texture: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_draw_cube_texture
.PHONY : models_draw_cube_texture

# fast build rule for target.
models_draw_cube_texture/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_draw_cube_texture.dir/build.make examples/CMakeFiles/models_draw_cube_texture.dir/build
.PHONY : models_draw_cube_texture/fast

#=============================================================================
# Target rules for targets named models_first_person_maze

# Build rule for target.
models_first_person_maze: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_first_person_maze
.PHONY : models_first_person_maze

# fast build rule for target.
models_first_person_maze/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_first_person_maze.dir/build.make examples/CMakeFiles/models_first_person_maze.dir/build
.PHONY : models_first_person_maze/fast

#=============================================================================
# Target rules for targets named models_geometric_shapes

# Build rule for target.
models_geometric_shapes: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_geometric_shapes
.PHONY : models_geometric_shapes

# fast build rule for target.
models_geometric_shapes/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_geometric_shapes.dir/build.make examples/CMakeFiles/models_geometric_shapes.dir/build
.PHONY : models_geometric_shapes/fast

#=============================================================================
# Target rules for targets named models_heightmap

# Build rule for target.
models_heightmap: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_heightmap
.PHONY : models_heightmap

# fast build rule for target.
models_heightmap/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_heightmap.dir/build.make examples/CMakeFiles/models_heightmap.dir/build
.PHONY : models_heightmap/fast

#=============================================================================
# Target rules for targets named models_loading

# Build rule for target.
models_loading: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_loading
.PHONY : models_loading

# fast build rule for target.
models_loading/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading.dir/build.make examples/CMakeFiles/models_loading.dir/build
.PHONY : models_loading/fast

#=============================================================================
# Target rules for targets named models_loading_gltf

# Build rule for target.
models_loading_gltf: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_loading_gltf
.PHONY : models_loading_gltf

# fast build rule for target.
models_loading_gltf/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_gltf.dir/build.make examples/CMakeFiles/models_loading_gltf.dir/build
.PHONY : models_loading_gltf/fast

#=============================================================================
# Target rules for targets named models_loading_m3d

# Build rule for target.
models_loading_m3d: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_loading_m3d
.PHONY : models_loading_m3d

# fast build rule for target.
models_loading_m3d/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_m3d.dir/build.make examples/CMakeFiles/models_loading_m3d.dir/build
.PHONY : models_loading_m3d/fast

#=============================================================================
# Target rules for targets named models_loading_vox

# Build rule for target.
models_loading_vox: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_loading_vox
.PHONY : models_loading_vox

# fast build rule for target.
models_loading_vox/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_vox.dir/build.make examples/CMakeFiles/models_loading_vox.dir/build
.PHONY : models_loading_vox/fast

#=============================================================================
# Target rules for targets named models_mesh_generation

# Build rule for target.
models_mesh_generation: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_mesh_generation
.PHONY : models_mesh_generation

# fast build rule for target.
models_mesh_generation/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_mesh_generation.dir/build.make examples/CMakeFiles/models_mesh_generation.dir/build
.PHONY : models_mesh_generation/fast

#=============================================================================
# Target rules for targets named models_mesh_picking

# Build rule for target.
models_mesh_picking: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_mesh_picking
.PHONY : models_mesh_picking

# fast build rule for target.
models_mesh_picking/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_mesh_picking.dir/build.make examples/CMakeFiles/models_mesh_picking.dir/build
.PHONY : models_mesh_picking/fast

#=============================================================================
# Target rules for targets named models_orthographic_projection

# Build rule for target.
models_orthographic_projection: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_orthographic_projection
.PHONY : models_orthographic_projection

# fast build rule for target.
models_orthographic_projection/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_orthographic_projection.dir/build.make examples/CMakeFiles/models_orthographic_projection.dir/build
.PHONY : models_orthographic_projection/fast

#=============================================================================
# Target rules for targets named models_rlgl_solar_system

# Build rule for target.
models_rlgl_solar_system: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_rlgl_solar_system
.PHONY : models_rlgl_solar_system

# fast build rule for target.
models_rlgl_solar_system/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_rlgl_solar_system.dir/build.make examples/CMakeFiles/models_rlgl_solar_system.dir/build
.PHONY : models_rlgl_solar_system/fast

#=============================================================================
# Target rules for targets named models_skybox

# Build rule for target.
models_skybox: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_skybox
.PHONY : models_skybox

# fast build rule for target.
models_skybox/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_skybox.dir/build.make examples/CMakeFiles/models_skybox.dir/build
.PHONY : models_skybox/fast

#=============================================================================
# Target rules for targets named models_waving_cubes

# Build rule for target.
models_waving_cubes: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_waving_cubes
.PHONY : models_waving_cubes

# fast build rule for target.
models_waving_cubes/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_waving_cubes.dir/build.make examples/CMakeFiles/models_waving_cubes.dir/build
.PHONY : models_waving_cubes/fast

#=============================================================================
# Target rules for targets named models_yaw_pitch_roll

# Build rule for target.
models_yaw_pitch_roll: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 models_yaw_pitch_roll
.PHONY : models_yaw_pitch_roll

# fast build rule for target.
models_yaw_pitch_roll/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_yaw_pitch_roll.dir/build.make examples/CMakeFiles/models_yaw_pitch_roll.dir/build
.PHONY : models_yaw_pitch_roll/fast

#=============================================================================
# Target rules for targets named easings_testbed

# Build rule for target.
easings_testbed: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 easings_testbed
.PHONY : easings_testbed

# fast build rule for target.
easings_testbed/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/easings_testbed.dir/build.make examples/CMakeFiles/easings_testbed.dir/build
.PHONY : easings_testbed/fast

#=============================================================================
# Target rules for targets named embedded_files_loading

# Build rule for target.
embedded_files_loading: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 embedded_files_loading
.PHONY : embedded_files_loading

# fast build rule for target.
embedded_files_loading/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/embedded_files_loading.dir/build.make examples/CMakeFiles/embedded_files_loading.dir/build
.PHONY : embedded_files_loading/fast

#=============================================================================
# Target rules for targets named raylib_opengl_interop

# Build rule for target.
raylib_opengl_interop: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 raylib_opengl_interop
.PHONY : raylib_opengl_interop

# fast build rule for target.
raylib_opengl_interop/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/raylib_opengl_interop.dir/build.make examples/CMakeFiles/raylib_opengl_interop.dir/build
.PHONY : raylib_opengl_interop/fast

#=============================================================================
# Target rules for targets named rlgl_compute_shader

# Build rule for target.
rlgl_compute_shader: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 rlgl_compute_shader
.PHONY : rlgl_compute_shader

# fast build rule for target.
rlgl_compute_shader/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/rlgl_compute_shader.dir/build.make examples/CMakeFiles/rlgl_compute_shader.dir/build
.PHONY : rlgl_compute_shader/fast

#=============================================================================
# Target rules for targets named rlgl_standalone

# Build rule for target.
rlgl_standalone: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 rlgl_standalone
.PHONY : rlgl_standalone

# fast build rule for target.
rlgl_standalone/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/rlgl_standalone.dir/build.make examples/CMakeFiles/rlgl_standalone.dir/build
.PHONY : rlgl_standalone/fast

#=============================================================================
# Target rules for targets named shaders_basic_lighting

# Build rule for target.
shaders_basic_lighting: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_basic_lighting
.PHONY : shaders_basic_lighting

# fast build rule for target.
shaders_basic_lighting/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_basic_lighting.dir/build.make examples/CMakeFiles/shaders_basic_lighting.dir/build
.PHONY : shaders_basic_lighting/fast

#=============================================================================
# Target rules for targets named shaders_custom_uniform

# Build rule for target.
shaders_custom_uniform: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_custom_uniform
.PHONY : shaders_custom_uniform

# fast build rule for target.
shaders_custom_uniform/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_custom_uniform.dir/build.make examples/CMakeFiles/shaders_custom_uniform.dir/build
.PHONY : shaders_custom_uniform/fast

#=============================================================================
# Target rules for targets named shaders_eratosthenes

# Build rule for target.
shaders_eratosthenes: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_eratosthenes
.PHONY : shaders_eratosthenes

# fast build rule for target.
shaders_eratosthenes/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_eratosthenes.dir/build.make examples/CMakeFiles/shaders_eratosthenes.dir/build
.PHONY : shaders_eratosthenes/fast

#=============================================================================
# Target rules for targets named shaders_fog

# Build rule for target.
shaders_fog: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_fog
.PHONY : shaders_fog

# fast build rule for target.
shaders_fog/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_fog.dir/build.make examples/CMakeFiles/shaders_fog.dir/build
.PHONY : shaders_fog/fast

#=============================================================================
# Target rules for targets named shaders_hot_reloading

# Build rule for target.
shaders_hot_reloading: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_hot_reloading
.PHONY : shaders_hot_reloading

# fast build rule for target.
shaders_hot_reloading/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_hot_reloading.dir/build.make examples/CMakeFiles/shaders_hot_reloading.dir/build
.PHONY : shaders_hot_reloading/fast

#=============================================================================
# Target rules for targets named shaders_hybrid_render

# Build rule for target.
shaders_hybrid_render: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_hybrid_render
.PHONY : shaders_hybrid_render

# fast build rule for target.
shaders_hybrid_render/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_hybrid_render.dir/build.make examples/CMakeFiles/shaders_hybrid_render.dir/build
.PHONY : shaders_hybrid_render/fast

#=============================================================================
# Target rules for targets named shaders_julia_set

# Build rule for target.
shaders_julia_set: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_julia_set
.PHONY : shaders_julia_set

# fast build rule for target.
shaders_julia_set/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_julia_set.dir/build.make examples/CMakeFiles/shaders_julia_set.dir/build
.PHONY : shaders_julia_set/fast

#=============================================================================
# Target rules for targets named shaders_mesh_instancing

# Build rule for target.
shaders_mesh_instancing: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_mesh_instancing
.PHONY : shaders_mesh_instancing

# fast build rule for target.
shaders_mesh_instancing/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_mesh_instancing.dir/build.make examples/CMakeFiles/shaders_mesh_instancing.dir/build
.PHONY : shaders_mesh_instancing/fast

#=============================================================================
# Target rules for targets named shaders_model_shader

# Build rule for target.
shaders_model_shader: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_model_shader
.PHONY : shaders_model_shader

# fast build rule for target.
shaders_model_shader/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_model_shader.dir/build.make examples/CMakeFiles/shaders_model_shader.dir/build
.PHONY : shaders_model_shader/fast

#=============================================================================
# Target rules for targets named shaders_multi_sample2d

# Build rule for target.
shaders_multi_sample2d: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_multi_sample2d
.PHONY : shaders_multi_sample2d

# fast build rule for target.
shaders_multi_sample2d/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_multi_sample2d.dir/build.make examples/CMakeFiles/shaders_multi_sample2d.dir/build
.PHONY : shaders_multi_sample2d/fast

#=============================================================================
# Target rules for targets named shaders_palette_switch

# Build rule for target.
shaders_palette_switch: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_palette_switch
.PHONY : shaders_palette_switch

# fast build rule for target.
shaders_palette_switch/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_palette_switch.dir/build.make examples/CMakeFiles/shaders_palette_switch.dir/build
.PHONY : shaders_palette_switch/fast

#=============================================================================
# Target rules for targets named shaders_postprocessing

# Build rule for target.
shaders_postprocessing: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_postprocessing
.PHONY : shaders_postprocessing

# fast build rule for target.
shaders_postprocessing/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_postprocessing.dir/build.make examples/CMakeFiles/shaders_postprocessing.dir/build
.PHONY : shaders_postprocessing/fast

#=============================================================================
# Target rules for targets named shaders_raymarching

# Build rule for target.
shaders_raymarching: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_raymarching
.PHONY : shaders_raymarching

# fast build rule for target.
shaders_raymarching/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_raymarching.dir/build.make examples/CMakeFiles/shaders_raymarching.dir/build
.PHONY : shaders_raymarching/fast

#=============================================================================
# Target rules for targets named shaders_shapes_textures

# Build rule for target.
shaders_shapes_textures: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_shapes_textures
.PHONY : shaders_shapes_textures

# fast build rule for target.
shaders_shapes_textures/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_shapes_textures.dir/build.make examples/CMakeFiles/shaders_shapes_textures.dir/build
.PHONY : shaders_shapes_textures/fast

#=============================================================================
# Target rules for targets named shaders_simple_mask

# Build rule for target.
shaders_simple_mask: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_simple_mask
.PHONY : shaders_simple_mask

# fast build rule for target.
shaders_simple_mask/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_simple_mask.dir/build.make examples/CMakeFiles/shaders_simple_mask.dir/build
.PHONY : shaders_simple_mask/fast

#=============================================================================
# Target rules for targets named shaders_spotlight

# Build rule for target.
shaders_spotlight: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_spotlight
.PHONY : shaders_spotlight

# fast build rule for target.
shaders_spotlight/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_spotlight.dir/build.make examples/CMakeFiles/shaders_spotlight.dir/build
.PHONY : shaders_spotlight/fast

#=============================================================================
# Target rules for targets named shaders_texture_drawing

# Build rule for target.
shaders_texture_drawing: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_texture_drawing
.PHONY : shaders_texture_drawing

# fast build rule for target.
shaders_texture_drawing/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_drawing.dir/build.make examples/CMakeFiles/shaders_texture_drawing.dir/build
.PHONY : shaders_texture_drawing/fast

#=============================================================================
# Target rules for targets named shaders_texture_outline

# Build rule for target.
shaders_texture_outline: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_texture_outline
.PHONY : shaders_texture_outline

# fast build rule for target.
shaders_texture_outline/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_outline.dir/build.make examples/CMakeFiles/shaders_texture_outline.dir/build
.PHONY : shaders_texture_outline/fast

#=============================================================================
# Target rules for targets named shaders_texture_waves

# Build rule for target.
shaders_texture_waves: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_texture_waves
.PHONY : shaders_texture_waves

# fast build rule for target.
shaders_texture_waves/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_waves.dir/build.make examples/CMakeFiles/shaders_texture_waves.dir/build
.PHONY : shaders_texture_waves/fast

#=============================================================================
# Target rules for targets named shaders_write_depth

# Build rule for target.
shaders_write_depth: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shaders_write_depth
.PHONY : shaders_write_depth

# fast build rule for target.
shaders_write_depth/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_write_depth.dir/build.make examples/CMakeFiles/shaders_write_depth.dir/build
.PHONY : shaders_write_depth/fast

#=============================================================================
# Target rules for targets named shapes_basic_shapes

# Build rule for target.
shapes_basic_shapes: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_basic_shapes
.PHONY : shapes_basic_shapes

# fast build rule for target.
shapes_basic_shapes/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_basic_shapes.dir/build.make examples/CMakeFiles/shapes_basic_shapes.dir/build
.PHONY : shapes_basic_shapes/fast

#=============================================================================
# Target rules for targets named shapes_bouncing_ball

# Build rule for target.
shapes_bouncing_ball: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_bouncing_ball
.PHONY : shapes_bouncing_ball

# fast build rule for target.
shapes_bouncing_ball/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_bouncing_ball.dir/build.make examples/CMakeFiles/shapes_bouncing_ball.dir/build
.PHONY : shapes_bouncing_ball/fast

#=============================================================================
# Target rules for targets named shapes_collision_area

# Build rule for target.
shapes_collision_area: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_collision_area
.PHONY : shapes_collision_area

# fast build rule for target.
shapes_collision_area/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_collision_area.dir/build.make examples/CMakeFiles/shapes_collision_area.dir/build
.PHONY : shapes_collision_area/fast

#=============================================================================
# Target rules for targets named shapes_colors_palette

# Build rule for target.
shapes_colors_palette: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_colors_palette
.PHONY : shapes_colors_palette

# fast build rule for target.
shapes_colors_palette/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_colors_palette.dir/build.make examples/CMakeFiles/shapes_colors_palette.dir/build
.PHONY : shapes_colors_palette/fast

#=============================================================================
# Target rules for targets named shapes_draw_circle_sector

# Build rule for target.
shapes_draw_circle_sector: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_draw_circle_sector
.PHONY : shapes_draw_circle_sector

# fast build rule for target.
shapes_draw_circle_sector/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_circle_sector.dir/build.make examples/CMakeFiles/shapes_draw_circle_sector.dir/build
.PHONY : shapes_draw_circle_sector/fast

#=============================================================================
# Target rules for targets named shapes_draw_rectangle_rounded

# Build rule for target.
shapes_draw_rectangle_rounded: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_draw_rectangle_rounded
.PHONY : shapes_draw_rectangle_rounded

# fast build rule for target.
shapes_draw_rectangle_rounded/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/build.make examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/build
.PHONY : shapes_draw_rectangle_rounded/fast

#=============================================================================
# Target rules for targets named shapes_draw_ring

# Build rule for target.
shapes_draw_ring: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_draw_ring
.PHONY : shapes_draw_ring

# fast build rule for target.
shapes_draw_ring/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_ring.dir/build.make examples/CMakeFiles/shapes_draw_ring.dir/build
.PHONY : shapes_draw_ring/fast

#=============================================================================
# Target rules for targets named shapes_easings_ball_anim

# Build rule for target.
shapes_easings_ball_anim: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_easings_ball_anim
.PHONY : shapes_easings_ball_anim

# fast build rule for target.
shapes_easings_ball_anim/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_ball_anim.dir/build.make examples/CMakeFiles/shapes_easings_ball_anim.dir/build
.PHONY : shapes_easings_ball_anim/fast

#=============================================================================
# Target rules for targets named shapes_easings_box_anim

# Build rule for target.
shapes_easings_box_anim: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_easings_box_anim
.PHONY : shapes_easings_box_anim

# fast build rule for target.
shapes_easings_box_anim/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_box_anim.dir/build.make examples/CMakeFiles/shapes_easings_box_anim.dir/build
.PHONY : shapes_easings_box_anim/fast

#=============================================================================
# Target rules for targets named shapes_easings_rectangle_array

# Build rule for target.
shapes_easings_rectangle_array: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_easings_rectangle_array
.PHONY : shapes_easings_rectangle_array

# fast build rule for target.
shapes_easings_rectangle_array/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_rectangle_array.dir/build.make examples/CMakeFiles/shapes_easings_rectangle_array.dir/build
.PHONY : shapes_easings_rectangle_array/fast

#=============================================================================
# Target rules for targets named shapes_following_eyes

# Build rule for target.
shapes_following_eyes: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_following_eyes
.PHONY : shapes_following_eyes

# fast build rule for target.
shapes_following_eyes/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_following_eyes.dir/build.make examples/CMakeFiles/shapes_following_eyes.dir/build
.PHONY : shapes_following_eyes/fast

#=============================================================================
# Target rules for targets named shapes_lines_bezier

# Build rule for target.
shapes_lines_bezier: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_lines_bezier
.PHONY : shapes_lines_bezier

# fast build rule for target.
shapes_lines_bezier/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_lines_bezier.dir/build.make examples/CMakeFiles/shapes_lines_bezier.dir/build
.PHONY : shapes_lines_bezier/fast

#=============================================================================
# Target rules for targets named shapes_logo_raylib

# Build rule for target.
shapes_logo_raylib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_logo_raylib
.PHONY : shapes_logo_raylib

# fast build rule for target.
shapes_logo_raylib/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_logo_raylib.dir/build.make examples/CMakeFiles/shapes_logo_raylib.dir/build
.PHONY : shapes_logo_raylib/fast

#=============================================================================
# Target rules for targets named shapes_logo_raylib_anim

# Build rule for target.
shapes_logo_raylib_anim: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_logo_raylib_anim
.PHONY : shapes_logo_raylib_anim

# fast build rule for target.
shapes_logo_raylib_anim/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_logo_raylib_anim.dir/build.make examples/CMakeFiles/shapes_logo_raylib_anim.dir/build
.PHONY : shapes_logo_raylib_anim/fast

#=============================================================================
# Target rules for targets named shapes_rectangle_scaling

# Build rule for target.
shapes_rectangle_scaling: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_rectangle_scaling
.PHONY : shapes_rectangle_scaling

# fast build rule for target.
shapes_rectangle_scaling/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_rectangle_scaling.dir/build.make examples/CMakeFiles/shapes_rectangle_scaling.dir/build
.PHONY : shapes_rectangle_scaling/fast

#=============================================================================
# Target rules for targets named shapes_top_down_lights

# Build rule for target.
shapes_top_down_lights: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 shapes_top_down_lights
.PHONY : shapes_top_down_lights

# fast build rule for target.
shapes_top_down_lights/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_top_down_lights.dir/build.make examples/CMakeFiles/shapes_top_down_lights.dir/build
.PHONY : shapes_top_down_lights/fast

#=============================================================================
# Target rules for targets named text_codepoints_loading

# Build rule for target.
text_codepoints_loading: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_codepoints_loading
.PHONY : text_codepoints_loading

# fast build rule for target.
text_codepoints_loading/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_codepoints_loading.dir/build.make examples/CMakeFiles/text_codepoints_loading.dir/build
.PHONY : text_codepoints_loading/fast

#=============================================================================
# Target rules for targets named text_draw_3d

# Build rule for target.
text_draw_3d: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_draw_3d
.PHONY : text_draw_3d

# fast build rule for target.
text_draw_3d/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_draw_3d.dir/build.make examples/CMakeFiles/text_draw_3d.dir/build
.PHONY : text_draw_3d/fast

#=============================================================================
# Target rules for targets named text_font_filters

# Build rule for target.
text_font_filters: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_font_filters
.PHONY : text_font_filters

# fast build rule for target.
text_font_filters/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_filters.dir/build.make examples/CMakeFiles/text_font_filters.dir/build
.PHONY : text_font_filters/fast

#=============================================================================
# Target rules for targets named text_font_loading

# Build rule for target.
text_font_loading: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_font_loading
.PHONY : text_font_loading

# fast build rule for target.
text_font_loading/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_loading.dir/build.make examples/CMakeFiles/text_font_loading.dir/build
.PHONY : text_font_loading/fast

#=============================================================================
# Target rules for targets named text_font_sdf

# Build rule for target.
text_font_sdf: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_font_sdf
.PHONY : text_font_sdf

# fast build rule for target.
text_font_sdf/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_sdf.dir/build.make examples/CMakeFiles/text_font_sdf.dir/build
.PHONY : text_font_sdf/fast

#=============================================================================
# Target rules for targets named text_font_spritefont

# Build rule for target.
text_font_spritefont: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_font_spritefont
.PHONY : text_font_spritefont

# fast build rule for target.
text_font_spritefont/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_spritefont.dir/build.make examples/CMakeFiles/text_font_spritefont.dir/build
.PHONY : text_font_spritefont/fast

#=============================================================================
# Target rules for targets named text_format_text

# Build rule for target.
text_format_text: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_format_text
.PHONY : text_format_text

# fast build rule for target.
text_format_text/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_format_text.dir/build.make examples/CMakeFiles/text_format_text.dir/build
.PHONY : text_format_text/fast

#=============================================================================
# Target rules for targets named text_input_box

# Build rule for target.
text_input_box: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_input_box
.PHONY : text_input_box

# fast build rule for target.
text_input_box/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_input_box.dir/build.make examples/CMakeFiles/text_input_box.dir/build
.PHONY : text_input_box/fast

#=============================================================================
# Target rules for targets named text_raylib_fonts

# Build rule for target.
text_raylib_fonts: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_raylib_fonts
.PHONY : text_raylib_fonts

# fast build rule for target.
text_raylib_fonts/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_raylib_fonts.dir/build.make examples/CMakeFiles/text_raylib_fonts.dir/build
.PHONY : text_raylib_fonts/fast

#=============================================================================
# Target rules for targets named text_rectangle_bounds

# Build rule for target.
text_rectangle_bounds: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_rectangle_bounds
.PHONY : text_rectangle_bounds

# fast build rule for target.
text_rectangle_bounds/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_rectangle_bounds.dir/build.make examples/CMakeFiles/text_rectangle_bounds.dir/build
.PHONY : text_rectangle_bounds/fast

#=============================================================================
# Target rules for targets named text_unicode

# Build rule for target.
text_unicode: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_unicode
.PHONY : text_unicode

# fast build rule for target.
text_unicode/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_unicode.dir/build.make examples/CMakeFiles/text_unicode.dir/build
.PHONY : text_unicode/fast

#=============================================================================
# Target rules for targets named text_writing_anim

# Build rule for target.
text_writing_anim: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 text_writing_anim
.PHONY : text_writing_anim

# fast build rule for target.
text_writing_anim/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_writing_anim.dir/build.make examples/CMakeFiles/text_writing_anim.dir/build
.PHONY : text_writing_anim/fast

#=============================================================================
# Target rules for targets named textures_background_scrolling

# Build rule for target.
textures_background_scrolling: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_background_scrolling
.PHONY : textures_background_scrolling

# fast build rule for target.
textures_background_scrolling/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_background_scrolling.dir/build.make examples/CMakeFiles/textures_background_scrolling.dir/build
.PHONY : textures_background_scrolling/fast

#=============================================================================
# Target rules for targets named textures_blend_modes

# Build rule for target.
textures_blend_modes: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_blend_modes
.PHONY : textures_blend_modes

# fast build rule for target.
textures_blend_modes/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_blend_modes.dir/build.make examples/CMakeFiles/textures_blend_modes.dir/build
.PHONY : textures_blend_modes/fast

#=============================================================================
# Target rules for targets named textures_bunnymark

# Build rule for target.
textures_bunnymark: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_bunnymark
.PHONY : textures_bunnymark

# fast build rule for target.
textures_bunnymark/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_bunnymark.dir/build.make examples/CMakeFiles/textures_bunnymark.dir/build
.PHONY : textures_bunnymark/fast

#=============================================================================
# Target rules for targets named textures_draw_tiled

# Build rule for target.
textures_draw_tiled: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_draw_tiled
.PHONY : textures_draw_tiled

# fast build rule for target.
textures_draw_tiled/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_draw_tiled.dir/build.make examples/CMakeFiles/textures_draw_tiled.dir/build
.PHONY : textures_draw_tiled/fast

#=============================================================================
# Target rules for targets named textures_fog_of_war

# Build rule for target.
textures_fog_of_war: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_fog_of_war
.PHONY : textures_fog_of_war

# fast build rule for target.
textures_fog_of_war/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_fog_of_war.dir/build.make examples/CMakeFiles/textures_fog_of_war.dir/build
.PHONY : textures_fog_of_war/fast

#=============================================================================
# Target rules for targets named textures_gif_player

# Build rule for target.
textures_gif_player: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_gif_player
.PHONY : textures_gif_player

# fast build rule for target.
textures_gif_player/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_gif_player.dir/build.make examples/CMakeFiles/textures_gif_player.dir/build
.PHONY : textures_gif_player/fast

#=============================================================================
# Target rules for targets named textures_image_drawing

# Build rule for target.
textures_image_drawing: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_image_drawing
.PHONY : textures_image_drawing

# fast build rule for target.
textures_image_drawing/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_drawing.dir/build.make examples/CMakeFiles/textures_image_drawing.dir/build
.PHONY : textures_image_drawing/fast

#=============================================================================
# Target rules for targets named textures_image_generation

# Build rule for target.
textures_image_generation: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_image_generation
.PHONY : textures_image_generation

# fast build rule for target.
textures_image_generation/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_generation.dir/build.make examples/CMakeFiles/textures_image_generation.dir/build
.PHONY : textures_image_generation/fast

#=============================================================================
# Target rules for targets named textures_image_loading

# Build rule for target.
textures_image_loading: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_image_loading
.PHONY : textures_image_loading

# fast build rule for target.
textures_image_loading/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_loading.dir/build.make examples/CMakeFiles/textures_image_loading.dir/build
.PHONY : textures_image_loading/fast

#=============================================================================
# Target rules for targets named textures_image_processing

# Build rule for target.
textures_image_processing: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_image_processing
.PHONY : textures_image_processing

# fast build rule for target.
textures_image_processing/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_processing.dir/build.make examples/CMakeFiles/textures_image_processing.dir/build
.PHONY : textures_image_processing/fast

#=============================================================================
# Target rules for targets named textures_image_text

# Build rule for target.
textures_image_text: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_image_text
.PHONY : textures_image_text

# fast build rule for target.
textures_image_text/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_text.dir/build.make examples/CMakeFiles/textures_image_text.dir/build
.PHONY : textures_image_text/fast

#=============================================================================
# Target rules for targets named textures_logo_raylib

# Build rule for target.
textures_logo_raylib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_logo_raylib
.PHONY : textures_logo_raylib

# fast build rule for target.
textures_logo_raylib/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_logo_raylib.dir/build.make examples/CMakeFiles/textures_logo_raylib.dir/build
.PHONY : textures_logo_raylib/fast

#=============================================================================
# Target rules for targets named textures_mouse_painting

# Build rule for target.
textures_mouse_painting: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_mouse_painting
.PHONY : textures_mouse_painting

# fast build rule for target.
textures_mouse_painting/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_mouse_painting.dir/build.make examples/CMakeFiles/textures_mouse_painting.dir/build
.PHONY : textures_mouse_painting/fast

#=============================================================================
# Target rules for targets named textures_npatch_drawing

# Build rule for target.
textures_npatch_drawing: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_npatch_drawing
.PHONY : textures_npatch_drawing

# fast build rule for target.
textures_npatch_drawing/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_npatch_drawing.dir/build.make examples/CMakeFiles/textures_npatch_drawing.dir/build
.PHONY : textures_npatch_drawing/fast

#=============================================================================
# Target rules for targets named textures_particles_blending

# Build rule for target.
textures_particles_blending: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_particles_blending
.PHONY : textures_particles_blending

# fast build rule for target.
textures_particles_blending/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_particles_blending.dir/build.make examples/CMakeFiles/textures_particles_blending.dir/build
.PHONY : textures_particles_blending/fast

#=============================================================================
# Target rules for targets named textures_polygon

# Build rule for target.
textures_polygon: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_polygon
.PHONY : textures_polygon

# fast build rule for target.
textures_polygon/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_polygon.dir/build.make examples/CMakeFiles/textures_polygon.dir/build
.PHONY : textures_polygon/fast

#=============================================================================
# Target rules for targets named textures_raw_data

# Build rule for target.
textures_raw_data: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_raw_data
.PHONY : textures_raw_data

# fast build rule for target.
textures_raw_data/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_raw_data.dir/build.make examples/CMakeFiles/textures_raw_data.dir/build
.PHONY : textures_raw_data/fast

#=============================================================================
# Target rules for targets named textures_sprite_anim

# Build rule for target.
textures_sprite_anim: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_sprite_anim
.PHONY : textures_sprite_anim

# fast build rule for target.
textures_sprite_anim/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_anim.dir/build.make examples/CMakeFiles/textures_sprite_anim.dir/build
.PHONY : textures_sprite_anim/fast

#=============================================================================
# Target rules for targets named textures_sprite_button

# Build rule for target.
textures_sprite_button: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_sprite_button
.PHONY : textures_sprite_button

# fast build rule for target.
textures_sprite_button/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_button.dir/build.make examples/CMakeFiles/textures_sprite_button.dir/build
.PHONY : textures_sprite_button/fast

#=============================================================================
# Target rules for targets named textures_sprite_explosion

# Build rule for target.
textures_sprite_explosion: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_sprite_explosion
.PHONY : textures_sprite_explosion

# fast build rule for target.
textures_sprite_explosion/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_explosion.dir/build.make examples/CMakeFiles/textures_sprite_explosion.dir/build
.PHONY : textures_sprite_explosion/fast

#=============================================================================
# Target rules for targets named textures_srcrec_dstrec

# Build rule for target.
textures_srcrec_dstrec: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_srcrec_dstrec
.PHONY : textures_srcrec_dstrec

# fast build rule for target.
textures_srcrec_dstrec/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_srcrec_dstrec.dir/build.make examples/CMakeFiles/textures_srcrec_dstrec.dir/build
.PHONY : textures_srcrec_dstrec/fast

#=============================================================================
# Target rules for targets named textures_textured_curve

# Build rule for target.
textures_textured_curve: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_textured_curve
.PHONY : textures_textured_curve

# fast build rule for target.
textures_textured_curve/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_textured_curve.dir/build.make examples/CMakeFiles/textures_textured_curve.dir/build
.PHONY : textures_textured_curve/fast

#=============================================================================
# Target rules for targets named textures_to_image

# Build rule for target.
textures_to_image: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 textures_to_image
.PHONY : textures_to_image

# fast build rule for target.
textures_to_image/fast:
	$(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_to_image.dir/build.make examples/CMakeFiles/textures_to_image.dir/build
.PHONY : textures_to_image/fast

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... update_mappings"
	@echo "... audio_mixed_processor"
	@echo "... audio_module_playing"
	@echo "... audio_music_stream"
	@echo "... audio_raw_stream"
	@echo "... audio_sound_loading"
	@echo "... audio_stream_effects"
	@echo "... core_2d_camera"
	@echo "... core_2d_camera_mouse_zoom"
	@echo "... core_2d_camera_platformer"
	@echo "... core_3d_camera_first_person"
	@echo "... core_3d_camera_free"
	@echo "... core_3d_camera_mode"
	@echo "... core_3d_picking"
	@echo "... core_basic_screen_manager"
	@echo "... core_basic_window"
	@echo "... core_basic_window_web"
	@echo "... core_custom_frame_control"
	@echo "... core_custom_logging"
	@echo "... core_drop_files"
	@echo "... core_input_gamepad"
	@echo "... core_input_gestures"
	@echo "... core_input_keys"
	@echo "... core_input_mouse"
	@echo "... core_input_mouse_wheel"
	@echo "... core_input_multitouch"
	@echo "... core_loading_thread"
	@echo "... core_random_values"
	@echo "... core_scissor_test"
	@echo "... core_smooth_pixelperfect"
	@echo "... core_split_screen"
	@echo "... core_storage_values"
	@echo "... core_vr_simulator"
	@echo "... core_window_flags"
	@echo "... core_window_letterbox"
	@echo "... core_window_should_close"
	@echo "... core_world_screen"
	@echo "... easings_testbed"
	@echo "... embedded_files_loading"
	@echo "... glfw"
	@echo "... models_animation"
	@echo "... models_billboard"
	@echo "... models_box_collisions"
	@echo "... models_cubicmap"
	@echo "... models_draw_cube_texture"
	@echo "... models_first_person_maze"
	@echo "... models_geometric_shapes"
	@echo "... models_heightmap"
	@echo "... models_loading"
	@echo "... models_loading_gltf"
	@echo "... models_loading_m3d"
	@echo "... models_loading_vox"
	@echo "... models_mesh_generation"
	@echo "... models_mesh_picking"
	@echo "... models_orthographic_projection"
	@echo "... models_rlgl_solar_system"
	@echo "... models_skybox"
	@echo "... models_waving_cubes"
	@echo "... models_yaw_pitch_roll"
	@echo "... raylib"
	@echo "... raylib_opengl_interop"
	@echo "... rlgl_compute_shader"
	@echo "... rlgl_standalone"
	@echo "... shaders_basic_lighting"
	@echo "... shaders_custom_uniform"
	@echo "... shaders_eratosthenes"
	@echo "... shaders_fog"
	@echo "... shaders_hot_reloading"
	@echo "... shaders_hybrid_render"
	@echo "... shaders_julia_set"
	@echo "... shaders_mesh_instancing"
	@echo "... shaders_model_shader"
	@echo "... shaders_multi_sample2d"
	@echo "... shaders_palette_switch"
	@echo "... shaders_postprocessing"
	@echo "... shaders_raymarching"
	@echo "... shaders_shapes_textures"
	@echo "... shaders_simple_mask"
	@echo "... shaders_spotlight"
	@echo "... shaders_texture_drawing"
	@echo "... shaders_texture_outline"
	@echo "... shaders_texture_waves"
	@echo "... shaders_write_depth"
	@echo "... shapes_basic_shapes"
	@echo "... shapes_bouncing_ball"
	@echo "... shapes_collision_area"
	@echo "... shapes_colors_palette"
	@echo "... shapes_draw_circle_sector"
	@echo "... shapes_draw_rectangle_rounded"
	@echo "... shapes_draw_ring"
	@echo "... shapes_easings_ball_anim"
	@echo "... shapes_easings_box_anim"
	@echo "... shapes_easings_rectangle_array"
	@echo "... shapes_following_eyes"
	@echo "... shapes_lines_bezier"
	@echo "... shapes_logo_raylib"
	@echo "... shapes_logo_raylib_anim"
	@echo "... shapes_rectangle_scaling"
	@echo "... shapes_top_down_lights"
	@echo "... text_codepoints_loading"
	@echo "... text_draw_3d"
	@echo "... text_font_filters"
	@echo "... text_font_loading"
	@echo "... text_font_sdf"
	@echo "... text_font_spritefont"
	@echo "... text_format_text"
	@echo "... text_input_box"
	@echo "... text_raylib_fonts"
	@echo "... text_rectangle_bounds"
	@echo "... text_unicode"
	@echo "... text_writing_anim"
	@echo "... textures_background_scrolling"
	@echo "... textures_blend_modes"
	@echo "... textures_bunnymark"
	@echo "... textures_draw_tiled"
	@echo "... textures_fog_of_war"
	@echo "... textures_gif_player"
	@echo "... textures_image_drawing"
	@echo "... textures_image_generation"
	@echo "... textures_image_loading"
	@echo "... textures_image_processing"
	@echo "... textures_image_text"
	@echo "... textures_logo_raylib"
	@echo "... textures_mouse_painting"
	@echo "... textures_npatch_drawing"
	@echo "... textures_particles_blending"
	@echo "... textures_polygon"
	@echo "... textures_raw_data"
	@echo "... textures_sprite_anim"
	@echo "... textures_sprite_button"
	@echo "... textures_sprite_explosion"
	@echo "... textures_srcrec_dstrec"
	@echo "... textures_textured_curve"
	@echo "... textures_to_image"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

