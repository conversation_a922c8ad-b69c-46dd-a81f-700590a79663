# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/projects/cpp/external/raylib

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/projects/cpp/external/raylib/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool..."
	cd /home/<USER>/projects/cpp/external/raylib/build && /usr/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Run CPack packaging tool for source..."
	cd /home/<USER>/projects/cpp/external/raylib/build && /usr/bin/cpack --config ./CPackSourceConfig.cmake /home/<USER>/projects/cpp/external/raylib/build/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running tests..."
	/usr/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/projects/cpp/external/raylib/build && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/external/raylib/build/CMakeFiles /home/<USER>/projects/cpp/external/raylib/build/examples//CMakeFiles/progress.marks
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/external/raylib/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
examples/CMakeFiles/audio_mixed_processor.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/audio_mixed_processor.dir/rule
.PHONY : examples/CMakeFiles/audio_mixed_processor.dir/rule

# Convenience name for target.
audio_mixed_processor: examples/CMakeFiles/audio_mixed_processor.dir/rule
.PHONY : audio_mixed_processor

# fast build rule for target.
audio_mixed_processor/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_mixed_processor.dir/build.make examples/CMakeFiles/audio_mixed_processor.dir/build
.PHONY : audio_mixed_processor/fast

# Convenience name for target.
examples/CMakeFiles/audio_module_playing.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/audio_module_playing.dir/rule
.PHONY : examples/CMakeFiles/audio_module_playing.dir/rule

# Convenience name for target.
audio_module_playing: examples/CMakeFiles/audio_module_playing.dir/rule
.PHONY : audio_module_playing

# fast build rule for target.
audio_module_playing/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_module_playing.dir/build.make examples/CMakeFiles/audio_module_playing.dir/build
.PHONY : audio_module_playing/fast

# Convenience name for target.
examples/CMakeFiles/audio_music_stream.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/audio_music_stream.dir/rule
.PHONY : examples/CMakeFiles/audio_music_stream.dir/rule

# Convenience name for target.
audio_music_stream: examples/CMakeFiles/audio_music_stream.dir/rule
.PHONY : audio_music_stream

# fast build rule for target.
audio_music_stream/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_music_stream.dir/build.make examples/CMakeFiles/audio_music_stream.dir/build
.PHONY : audio_music_stream/fast

# Convenience name for target.
examples/CMakeFiles/audio_raw_stream.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/audio_raw_stream.dir/rule
.PHONY : examples/CMakeFiles/audio_raw_stream.dir/rule

# Convenience name for target.
audio_raw_stream: examples/CMakeFiles/audio_raw_stream.dir/rule
.PHONY : audio_raw_stream

# fast build rule for target.
audio_raw_stream/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_raw_stream.dir/build.make examples/CMakeFiles/audio_raw_stream.dir/build
.PHONY : audio_raw_stream/fast

# Convenience name for target.
examples/CMakeFiles/audio_sound_loading.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/audio_sound_loading.dir/rule
.PHONY : examples/CMakeFiles/audio_sound_loading.dir/rule

# Convenience name for target.
audio_sound_loading: examples/CMakeFiles/audio_sound_loading.dir/rule
.PHONY : audio_sound_loading

# fast build rule for target.
audio_sound_loading/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_sound_loading.dir/build.make examples/CMakeFiles/audio_sound_loading.dir/build
.PHONY : audio_sound_loading/fast

# Convenience name for target.
examples/CMakeFiles/audio_stream_effects.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/audio_stream_effects.dir/rule
.PHONY : examples/CMakeFiles/audio_stream_effects.dir/rule

# Convenience name for target.
audio_stream_effects: examples/CMakeFiles/audio_stream_effects.dir/rule
.PHONY : audio_stream_effects

# fast build rule for target.
audio_stream_effects/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_stream_effects.dir/build.make examples/CMakeFiles/audio_stream_effects.dir/build
.PHONY : audio_stream_effects/fast

# Convenience name for target.
examples/CMakeFiles/core_2d_camera.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_2d_camera.dir/rule
.PHONY : examples/CMakeFiles/core_2d_camera.dir/rule

# Convenience name for target.
core_2d_camera: examples/CMakeFiles/core_2d_camera.dir/rule
.PHONY : core_2d_camera

# fast build rule for target.
core_2d_camera/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera.dir/build.make examples/CMakeFiles/core_2d_camera.dir/build
.PHONY : core_2d_camera/fast

# Convenience name for target.
examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/rule
.PHONY : examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/rule

# Convenience name for target.
core_2d_camera_mouse_zoom: examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/rule
.PHONY : core_2d_camera_mouse_zoom

# fast build rule for target.
core_2d_camera_mouse_zoom/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/build.make examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/build
.PHONY : core_2d_camera_mouse_zoom/fast

# Convenience name for target.
examples/CMakeFiles/core_2d_camera_platformer.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_2d_camera_platformer.dir/rule
.PHONY : examples/CMakeFiles/core_2d_camera_platformer.dir/rule

# Convenience name for target.
core_2d_camera_platformer: examples/CMakeFiles/core_2d_camera_platformer.dir/rule
.PHONY : core_2d_camera_platformer

# fast build rule for target.
core_2d_camera_platformer/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera_platformer.dir/build.make examples/CMakeFiles/core_2d_camera_platformer.dir/build
.PHONY : core_2d_camera_platformer/fast

# Convenience name for target.
examples/CMakeFiles/core_3d_camera_first_person.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_3d_camera_first_person.dir/rule
.PHONY : examples/CMakeFiles/core_3d_camera_first_person.dir/rule

# Convenience name for target.
core_3d_camera_first_person: examples/CMakeFiles/core_3d_camera_first_person.dir/rule
.PHONY : core_3d_camera_first_person

# fast build rule for target.
core_3d_camera_first_person/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_first_person.dir/build.make examples/CMakeFiles/core_3d_camera_first_person.dir/build
.PHONY : core_3d_camera_first_person/fast

# Convenience name for target.
examples/CMakeFiles/core_3d_camera_free.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_3d_camera_free.dir/rule
.PHONY : examples/CMakeFiles/core_3d_camera_free.dir/rule

# Convenience name for target.
core_3d_camera_free: examples/CMakeFiles/core_3d_camera_free.dir/rule
.PHONY : core_3d_camera_free

# fast build rule for target.
core_3d_camera_free/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_free.dir/build.make examples/CMakeFiles/core_3d_camera_free.dir/build
.PHONY : core_3d_camera_free/fast

# Convenience name for target.
examples/CMakeFiles/core_3d_camera_mode.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_3d_camera_mode.dir/rule
.PHONY : examples/CMakeFiles/core_3d_camera_mode.dir/rule

# Convenience name for target.
core_3d_camera_mode: examples/CMakeFiles/core_3d_camera_mode.dir/rule
.PHONY : core_3d_camera_mode

# fast build rule for target.
core_3d_camera_mode/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_mode.dir/build.make examples/CMakeFiles/core_3d_camera_mode.dir/build
.PHONY : core_3d_camera_mode/fast

# Convenience name for target.
examples/CMakeFiles/core_3d_picking.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_3d_picking.dir/rule
.PHONY : examples/CMakeFiles/core_3d_picking.dir/rule

# Convenience name for target.
core_3d_picking: examples/CMakeFiles/core_3d_picking.dir/rule
.PHONY : core_3d_picking

# fast build rule for target.
core_3d_picking/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_picking.dir/build.make examples/CMakeFiles/core_3d_picking.dir/build
.PHONY : core_3d_picking/fast

# Convenience name for target.
examples/CMakeFiles/core_basic_screen_manager.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_basic_screen_manager.dir/rule
.PHONY : examples/CMakeFiles/core_basic_screen_manager.dir/rule

# Convenience name for target.
core_basic_screen_manager: examples/CMakeFiles/core_basic_screen_manager.dir/rule
.PHONY : core_basic_screen_manager

# fast build rule for target.
core_basic_screen_manager/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_screen_manager.dir/build.make examples/CMakeFiles/core_basic_screen_manager.dir/build
.PHONY : core_basic_screen_manager/fast

# Convenience name for target.
examples/CMakeFiles/core_basic_window.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_basic_window.dir/rule
.PHONY : examples/CMakeFiles/core_basic_window.dir/rule

# Convenience name for target.
core_basic_window: examples/CMakeFiles/core_basic_window.dir/rule
.PHONY : core_basic_window

# fast build rule for target.
core_basic_window/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_window.dir/build.make examples/CMakeFiles/core_basic_window.dir/build
.PHONY : core_basic_window/fast

# Convenience name for target.
examples/CMakeFiles/core_basic_window_web.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_basic_window_web.dir/rule
.PHONY : examples/CMakeFiles/core_basic_window_web.dir/rule

# Convenience name for target.
core_basic_window_web: examples/CMakeFiles/core_basic_window_web.dir/rule
.PHONY : core_basic_window_web

# fast build rule for target.
core_basic_window_web/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_window_web.dir/build.make examples/CMakeFiles/core_basic_window_web.dir/build
.PHONY : core_basic_window_web/fast

# Convenience name for target.
examples/CMakeFiles/core_custom_frame_control.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_custom_frame_control.dir/rule
.PHONY : examples/CMakeFiles/core_custom_frame_control.dir/rule

# Convenience name for target.
core_custom_frame_control: examples/CMakeFiles/core_custom_frame_control.dir/rule
.PHONY : core_custom_frame_control

# fast build rule for target.
core_custom_frame_control/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_custom_frame_control.dir/build.make examples/CMakeFiles/core_custom_frame_control.dir/build
.PHONY : core_custom_frame_control/fast

# Convenience name for target.
examples/CMakeFiles/core_custom_logging.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_custom_logging.dir/rule
.PHONY : examples/CMakeFiles/core_custom_logging.dir/rule

# Convenience name for target.
core_custom_logging: examples/CMakeFiles/core_custom_logging.dir/rule
.PHONY : core_custom_logging

# fast build rule for target.
core_custom_logging/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_custom_logging.dir/build.make examples/CMakeFiles/core_custom_logging.dir/build
.PHONY : core_custom_logging/fast

# Convenience name for target.
examples/CMakeFiles/core_drop_files.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_drop_files.dir/rule
.PHONY : examples/CMakeFiles/core_drop_files.dir/rule

# Convenience name for target.
core_drop_files: examples/CMakeFiles/core_drop_files.dir/rule
.PHONY : core_drop_files

# fast build rule for target.
core_drop_files/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_drop_files.dir/build.make examples/CMakeFiles/core_drop_files.dir/build
.PHONY : core_drop_files/fast

# Convenience name for target.
examples/CMakeFiles/core_input_gamepad.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_input_gamepad.dir/rule
.PHONY : examples/CMakeFiles/core_input_gamepad.dir/rule

# Convenience name for target.
core_input_gamepad: examples/CMakeFiles/core_input_gamepad.dir/rule
.PHONY : core_input_gamepad

# fast build rule for target.
core_input_gamepad/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_gamepad.dir/build.make examples/CMakeFiles/core_input_gamepad.dir/build
.PHONY : core_input_gamepad/fast

# Convenience name for target.
examples/CMakeFiles/core_input_gestures.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_input_gestures.dir/rule
.PHONY : examples/CMakeFiles/core_input_gestures.dir/rule

# Convenience name for target.
core_input_gestures: examples/CMakeFiles/core_input_gestures.dir/rule
.PHONY : core_input_gestures

# fast build rule for target.
core_input_gestures/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_gestures.dir/build.make examples/CMakeFiles/core_input_gestures.dir/build
.PHONY : core_input_gestures/fast

# Convenience name for target.
examples/CMakeFiles/core_input_keys.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_input_keys.dir/rule
.PHONY : examples/CMakeFiles/core_input_keys.dir/rule

# Convenience name for target.
core_input_keys: examples/CMakeFiles/core_input_keys.dir/rule
.PHONY : core_input_keys

# fast build rule for target.
core_input_keys/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_keys.dir/build.make examples/CMakeFiles/core_input_keys.dir/build
.PHONY : core_input_keys/fast

# Convenience name for target.
examples/CMakeFiles/core_input_mouse.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_input_mouse.dir/rule
.PHONY : examples/CMakeFiles/core_input_mouse.dir/rule

# Convenience name for target.
core_input_mouse: examples/CMakeFiles/core_input_mouse.dir/rule
.PHONY : core_input_mouse

# fast build rule for target.
core_input_mouse/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_mouse.dir/build.make examples/CMakeFiles/core_input_mouse.dir/build
.PHONY : core_input_mouse/fast

# Convenience name for target.
examples/CMakeFiles/core_input_mouse_wheel.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_input_mouse_wheel.dir/rule
.PHONY : examples/CMakeFiles/core_input_mouse_wheel.dir/rule

# Convenience name for target.
core_input_mouse_wheel: examples/CMakeFiles/core_input_mouse_wheel.dir/rule
.PHONY : core_input_mouse_wheel

# fast build rule for target.
core_input_mouse_wheel/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_mouse_wheel.dir/build.make examples/CMakeFiles/core_input_mouse_wheel.dir/build
.PHONY : core_input_mouse_wheel/fast

# Convenience name for target.
examples/CMakeFiles/core_input_multitouch.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_input_multitouch.dir/rule
.PHONY : examples/CMakeFiles/core_input_multitouch.dir/rule

# Convenience name for target.
core_input_multitouch: examples/CMakeFiles/core_input_multitouch.dir/rule
.PHONY : core_input_multitouch

# fast build rule for target.
core_input_multitouch/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_multitouch.dir/build.make examples/CMakeFiles/core_input_multitouch.dir/build
.PHONY : core_input_multitouch/fast

# Convenience name for target.
examples/CMakeFiles/core_loading_thread.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_loading_thread.dir/rule
.PHONY : examples/CMakeFiles/core_loading_thread.dir/rule

# Convenience name for target.
core_loading_thread: examples/CMakeFiles/core_loading_thread.dir/rule
.PHONY : core_loading_thread

# fast build rule for target.
core_loading_thread/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_loading_thread.dir/build.make examples/CMakeFiles/core_loading_thread.dir/build
.PHONY : core_loading_thread/fast

# Convenience name for target.
examples/CMakeFiles/core_random_values.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_random_values.dir/rule
.PHONY : examples/CMakeFiles/core_random_values.dir/rule

# Convenience name for target.
core_random_values: examples/CMakeFiles/core_random_values.dir/rule
.PHONY : core_random_values

# fast build rule for target.
core_random_values/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_random_values.dir/build.make examples/CMakeFiles/core_random_values.dir/build
.PHONY : core_random_values/fast

# Convenience name for target.
examples/CMakeFiles/core_scissor_test.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_scissor_test.dir/rule
.PHONY : examples/CMakeFiles/core_scissor_test.dir/rule

# Convenience name for target.
core_scissor_test: examples/CMakeFiles/core_scissor_test.dir/rule
.PHONY : core_scissor_test

# fast build rule for target.
core_scissor_test/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_scissor_test.dir/build.make examples/CMakeFiles/core_scissor_test.dir/build
.PHONY : core_scissor_test/fast

# Convenience name for target.
examples/CMakeFiles/core_smooth_pixelperfect.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_smooth_pixelperfect.dir/rule
.PHONY : examples/CMakeFiles/core_smooth_pixelperfect.dir/rule

# Convenience name for target.
core_smooth_pixelperfect: examples/CMakeFiles/core_smooth_pixelperfect.dir/rule
.PHONY : core_smooth_pixelperfect

# fast build rule for target.
core_smooth_pixelperfect/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_smooth_pixelperfect.dir/build.make examples/CMakeFiles/core_smooth_pixelperfect.dir/build
.PHONY : core_smooth_pixelperfect/fast

# Convenience name for target.
examples/CMakeFiles/core_split_screen.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_split_screen.dir/rule
.PHONY : examples/CMakeFiles/core_split_screen.dir/rule

# Convenience name for target.
core_split_screen: examples/CMakeFiles/core_split_screen.dir/rule
.PHONY : core_split_screen

# fast build rule for target.
core_split_screen/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_split_screen.dir/build.make examples/CMakeFiles/core_split_screen.dir/build
.PHONY : core_split_screen/fast

# Convenience name for target.
examples/CMakeFiles/core_storage_values.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_storage_values.dir/rule
.PHONY : examples/CMakeFiles/core_storage_values.dir/rule

# Convenience name for target.
core_storage_values: examples/CMakeFiles/core_storage_values.dir/rule
.PHONY : core_storage_values

# fast build rule for target.
core_storage_values/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_storage_values.dir/build.make examples/CMakeFiles/core_storage_values.dir/build
.PHONY : core_storage_values/fast

# Convenience name for target.
examples/CMakeFiles/core_vr_simulator.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_vr_simulator.dir/rule
.PHONY : examples/CMakeFiles/core_vr_simulator.dir/rule

# Convenience name for target.
core_vr_simulator: examples/CMakeFiles/core_vr_simulator.dir/rule
.PHONY : core_vr_simulator

# fast build rule for target.
core_vr_simulator/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_vr_simulator.dir/build.make examples/CMakeFiles/core_vr_simulator.dir/build
.PHONY : core_vr_simulator/fast

# Convenience name for target.
examples/CMakeFiles/core_window_flags.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_window_flags.dir/rule
.PHONY : examples/CMakeFiles/core_window_flags.dir/rule

# Convenience name for target.
core_window_flags: examples/CMakeFiles/core_window_flags.dir/rule
.PHONY : core_window_flags

# fast build rule for target.
core_window_flags/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_flags.dir/build.make examples/CMakeFiles/core_window_flags.dir/build
.PHONY : core_window_flags/fast

# Convenience name for target.
examples/CMakeFiles/core_window_letterbox.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_window_letterbox.dir/rule
.PHONY : examples/CMakeFiles/core_window_letterbox.dir/rule

# Convenience name for target.
core_window_letterbox: examples/CMakeFiles/core_window_letterbox.dir/rule
.PHONY : core_window_letterbox

# fast build rule for target.
core_window_letterbox/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_letterbox.dir/build.make examples/CMakeFiles/core_window_letterbox.dir/build
.PHONY : core_window_letterbox/fast

# Convenience name for target.
examples/CMakeFiles/core_window_should_close.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_window_should_close.dir/rule
.PHONY : examples/CMakeFiles/core_window_should_close.dir/rule

# Convenience name for target.
core_window_should_close: examples/CMakeFiles/core_window_should_close.dir/rule
.PHONY : core_window_should_close

# fast build rule for target.
core_window_should_close/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_should_close.dir/build.make examples/CMakeFiles/core_window_should_close.dir/build
.PHONY : core_window_should_close/fast

# Convenience name for target.
examples/CMakeFiles/core_world_screen.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/core_world_screen.dir/rule
.PHONY : examples/CMakeFiles/core_world_screen.dir/rule

# Convenience name for target.
core_world_screen: examples/CMakeFiles/core_world_screen.dir/rule
.PHONY : core_world_screen

# fast build rule for target.
core_world_screen/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_world_screen.dir/build.make examples/CMakeFiles/core_world_screen.dir/build
.PHONY : core_world_screen/fast

# Convenience name for target.
examples/CMakeFiles/models_animation.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_animation.dir/rule
.PHONY : examples/CMakeFiles/models_animation.dir/rule

# Convenience name for target.
models_animation: examples/CMakeFiles/models_animation.dir/rule
.PHONY : models_animation

# fast build rule for target.
models_animation/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_animation.dir/build.make examples/CMakeFiles/models_animation.dir/build
.PHONY : models_animation/fast

# Convenience name for target.
examples/CMakeFiles/models_billboard.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_billboard.dir/rule
.PHONY : examples/CMakeFiles/models_billboard.dir/rule

# Convenience name for target.
models_billboard: examples/CMakeFiles/models_billboard.dir/rule
.PHONY : models_billboard

# fast build rule for target.
models_billboard/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_billboard.dir/build.make examples/CMakeFiles/models_billboard.dir/build
.PHONY : models_billboard/fast

# Convenience name for target.
examples/CMakeFiles/models_box_collisions.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_box_collisions.dir/rule
.PHONY : examples/CMakeFiles/models_box_collisions.dir/rule

# Convenience name for target.
models_box_collisions: examples/CMakeFiles/models_box_collisions.dir/rule
.PHONY : models_box_collisions

# fast build rule for target.
models_box_collisions/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_box_collisions.dir/build.make examples/CMakeFiles/models_box_collisions.dir/build
.PHONY : models_box_collisions/fast

# Convenience name for target.
examples/CMakeFiles/models_cubicmap.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_cubicmap.dir/rule
.PHONY : examples/CMakeFiles/models_cubicmap.dir/rule

# Convenience name for target.
models_cubicmap: examples/CMakeFiles/models_cubicmap.dir/rule
.PHONY : models_cubicmap

# fast build rule for target.
models_cubicmap/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_cubicmap.dir/build.make examples/CMakeFiles/models_cubicmap.dir/build
.PHONY : models_cubicmap/fast

# Convenience name for target.
examples/CMakeFiles/models_draw_cube_texture.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_draw_cube_texture.dir/rule
.PHONY : examples/CMakeFiles/models_draw_cube_texture.dir/rule

# Convenience name for target.
models_draw_cube_texture: examples/CMakeFiles/models_draw_cube_texture.dir/rule
.PHONY : models_draw_cube_texture

# fast build rule for target.
models_draw_cube_texture/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_draw_cube_texture.dir/build.make examples/CMakeFiles/models_draw_cube_texture.dir/build
.PHONY : models_draw_cube_texture/fast

# Convenience name for target.
examples/CMakeFiles/models_first_person_maze.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_first_person_maze.dir/rule
.PHONY : examples/CMakeFiles/models_first_person_maze.dir/rule

# Convenience name for target.
models_first_person_maze: examples/CMakeFiles/models_first_person_maze.dir/rule
.PHONY : models_first_person_maze

# fast build rule for target.
models_first_person_maze/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_first_person_maze.dir/build.make examples/CMakeFiles/models_first_person_maze.dir/build
.PHONY : models_first_person_maze/fast

# Convenience name for target.
examples/CMakeFiles/models_geometric_shapes.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_geometric_shapes.dir/rule
.PHONY : examples/CMakeFiles/models_geometric_shapes.dir/rule

# Convenience name for target.
models_geometric_shapes: examples/CMakeFiles/models_geometric_shapes.dir/rule
.PHONY : models_geometric_shapes

# fast build rule for target.
models_geometric_shapes/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_geometric_shapes.dir/build.make examples/CMakeFiles/models_geometric_shapes.dir/build
.PHONY : models_geometric_shapes/fast

# Convenience name for target.
examples/CMakeFiles/models_heightmap.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_heightmap.dir/rule
.PHONY : examples/CMakeFiles/models_heightmap.dir/rule

# Convenience name for target.
models_heightmap: examples/CMakeFiles/models_heightmap.dir/rule
.PHONY : models_heightmap

# fast build rule for target.
models_heightmap/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_heightmap.dir/build.make examples/CMakeFiles/models_heightmap.dir/build
.PHONY : models_heightmap/fast

# Convenience name for target.
examples/CMakeFiles/models_loading.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_loading.dir/rule
.PHONY : examples/CMakeFiles/models_loading.dir/rule

# Convenience name for target.
models_loading: examples/CMakeFiles/models_loading.dir/rule
.PHONY : models_loading

# fast build rule for target.
models_loading/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading.dir/build.make examples/CMakeFiles/models_loading.dir/build
.PHONY : models_loading/fast

# Convenience name for target.
examples/CMakeFiles/models_loading_gltf.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_loading_gltf.dir/rule
.PHONY : examples/CMakeFiles/models_loading_gltf.dir/rule

# Convenience name for target.
models_loading_gltf: examples/CMakeFiles/models_loading_gltf.dir/rule
.PHONY : models_loading_gltf

# fast build rule for target.
models_loading_gltf/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_gltf.dir/build.make examples/CMakeFiles/models_loading_gltf.dir/build
.PHONY : models_loading_gltf/fast

# Convenience name for target.
examples/CMakeFiles/models_loading_m3d.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_loading_m3d.dir/rule
.PHONY : examples/CMakeFiles/models_loading_m3d.dir/rule

# Convenience name for target.
models_loading_m3d: examples/CMakeFiles/models_loading_m3d.dir/rule
.PHONY : models_loading_m3d

# fast build rule for target.
models_loading_m3d/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_m3d.dir/build.make examples/CMakeFiles/models_loading_m3d.dir/build
.PHONY : models_loading_m3d/fast

# Convenience name for target.
examples/CMakeFiles/models_loading_vox.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_loading_vox.dir/rule
.PHONY : examples/CMakeFiles/models_loading_vox.dir/rule

# Convenience name for target.
models_loading_vox: examples/CMakeFiles/models_loading_vox.dir/rule
.PHONY : models_loading_vox

# fast build rule for target.
models_loading_vox/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_vox.dir/build.make examples/CMakeFiles/models_loading_vox.dir/build
.PHONY : models_loading_vox/fast

# Convenience name for target.
examples/CMakeFiles/models_mesh_generation.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_mesh_generation.dir/rule
.PHONY : examples/CMakeFiles/models_mesh_generation.dir/rule

# Convenience name for target.
models_mesh_generation: examples/CMakeFiles/models_mesh_generation.dir/rule
.PHONY : models_mesh_generation

# fast build rule for target.
models_mesh_generation/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_mesh_generation.dir/build.make examples/CMakeFiles/models_mesh_generation.dir/build
.PHONY : models_mesh_generation/fast

# Convenience name for target.
examples/CMakeFiles/models_mesh_picking.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_mesh_picking.dir/rule
.PHONY : examples/CMakeFiles/models_mesh_picking.dir/rule

# Convenience name for target.
models_mesh_picking: examples/CMakeFiles/models_mesh_picking.dir/rule
.PHONY : models_mesh_picking

# fast build rule for target.
models_mesh_picking/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_mesh_picking.dir/build.make examples/CMakeFiles/models_mesh_picking.dir/build
.PHONY : models_mesh_picking/fast

# Convenience name for target.
examples/CMakeFiles/models_orthographic_projection.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_orthographic_projection.dir/rule
.PHONY : examples/CMakeFiles/models_orthographic_projection.dir/rule

# Convenience name for target.
models_orthographic_projection: examples/CMakeFiles/models_orthographic_projection.dir/rule
.PHONY : models_orthographic_projection

# fast build rule for target.
models_orthographic_projection/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_orthographic_projection.dir/build.make examples/CMakeFiles/models_orthographic_projection.dir/build
.PHONY : models_orthographic_projection/fast

# Convenience name for target.
examples/CMakeFiles/models_rlgl_solar_system.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_rlgl_solar_system.dir/rule
.PHONY : examples/CMakeFiles/models_rlgl_solar_system.dir/rule

# Convenience name for target.
models_rlgl_solar_system: examples/CMakeFiles/models_rlgl_solar_system.dir/rule
.PHONY : models_rlgl_solar_system

# fast build rule for target.
models_rlgl_solar_system/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_rlgl_solar_system.dir/build.make examples/CMakeFiles/models_rlgl_solar_system.dir/build
.PHONY : models_rlgl_solar_system/fast

# Convenience name for target.
examples/CMakeFiles/models_skybox.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_skybox.dir/rule
.PHONY : examples/CMakeFiles/models_skybox.dir/rule

# Convenience name for target.
models_skybox: examples/CMakeFiles/models_skybox.dir/rule
.PHONY : models_skybox

# fast build rule for target.
models_skybox/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_skybox.dir/build.make examples/CMakeFiles/models_skybox.dir/build
.PHONY : models_skybox/fast

# Convenience name for target.
examples/CMakeFiles/models_waving_cubes.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_waving_cubes.dir/rule
.PHONY : examples/CMakeFiles/models_waving_cubes.dir/rule

# Convenience name for target.
models_waving_cubes: examples/CMakeFiles/models_waving_cubes.dir/rule
.PHONY : models_waving_cubes

# fast build rule for target.
models_waving_cubes/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_waving_cubes.dir/build.make examples/CMakeFiles/models_waving_cubes.dir/build
.PHONY : models_waving_cubes/fast

# Convenience name for target.
examples/CMakeFiles/models_yaw_pitch_roll.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/models_yaw_pitch_roll.dir/rule
.PHONY : examples/CMakeFiles/models_yaw_pitch_roll.dir/rule

# Convenience name for target.
models_yaw_pitch_roll: examples/CMakeFiles/models_yaw_pitch_roll.dir/rule
.PHONY : models_yaw_pitch_roll

# fast build rule for target.
models_yaw_pitch_roll/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_yaw_pitch_roll.dir/build.make examples/CMakeFiles/models_yaw_pitch_roll.dir/build
.PHONY : models_yaw_pitch_roll/fast

# Convenience name for target.
examples/CMakeFiles/easings_testbed.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/easings_testbed.dir/rule
.PHONY : examples/CMakeFiles/easings_testbed.dir/rule

# Convenience name for target.
easings_testbed: examples/CMakeFiles/easings_testbed.dir/rule
.PHONY : easings_testbed

# fast build rule for target.
easings_testbed/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/easings_testbed.dir/build.make examples/CMakeFiles/easings_testbed.dir/build
.PHONY : easings_testbed/fast

# Convenience name for target.
examples/CMakeFiles/embedded_files_loading.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/embedded_files_loading.dir/rule
.PHONY : examples/CMakeFiles/embedded_files_loading.dir/rule

# Convenience name for target.
embedded_files_loading: examples/CMakeFiles/embedded_files_loading.dir/rule
.PHONY : embedded_files_loading

# fast build rule for target.
embedded_files_loading/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/embedded_files_loading.dir/build.make examples/CMakeFiles/embedded_files_loading.dir/build
.PHONY : embedded_files_loading/fast

# Convenience name for target.
examples/CMakeFiles/raylib_opengl_interop.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/raylib_opengl_interop.dir/rule
.PHONY : examples/CMakeFiles/raylib_opengl_interop.dir/rule

# Convenience name for target.
raylib_opengl_interop: examples/CMakeFiles/raylib_opengl_interop.dir/rule
.PHONY : raylib_opengl_interop

# fast build rule for target.
raylib_opengl_interop/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/raylib_opengl_interop.dir/build.make examples/CMakeFiles/raylib_opengl_interop.dir/build
.PHONY : raylib_opengl_interop/fast

# Convenience name for target.
examples/CMakeFiles/rlgl_compute_shader.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/rlgl_compute_shader.dir/rule
.PHONY : examples/CMakeFiles/rlgl_compute_shader.dir/rule

# Convenience name for target.
rlgl_compute_shader: examples/CMakeFiles/rlgl_compute_shader.dir/rule
.PHONY : rlgl_compute_shader

# fast build rule for target.
rlgl_compute_shader/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/rlgl_compute_shader.dir/build.make examples/CMakeFiles/rlgl_compute_shader.dir/build
.PHONY : rlgl_compute_shader/fast

# Convenience name for target.
examples/CMakeFiles/rlgl_standalone.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/rlgl_standalone.dir/rule
.PHONY : examples/CMakeFiles/rlgl_standalone.dir/rule

# Convenience name for target.
rlgl_standalone: examples/CMakeFiles/rlgl_standalone.dir/rule
.PHONY : rlgl_standalone

# fast build rule for target.
rlgl_standalone/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/rlgl_standalone.dir/build.make examples/CMakeFiles/rlgl_standalone.dir/build
.PHONY : rlgl_standalone/fast

# Convenience name for target.
examples/CMakeFiles/shaders_basic_lighting.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_basic_lighting.dir/rule
.PHONY : examples/CMakeFiles/shaders_basic_lighting.dir/rule

# Convenience name for target.
shaders_basic_lighting: examples/CMakeFiles/shaders_basic_lighting.dir/rule
.PHONY : shaders_basic_lighting

# fast build rule for target.
shaders_basic_lighting/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_basic_lighting.dir/build.make examples/CMakeFiles/shaders_basic_lighting.dir/build
.PHONY : shaders_basic_lighting/fast

# Convenience name for target.
examples/CMakeFiles/shaders_custom_uniform.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_custom_uniform.dir/rule
.PHONY : examples/CMakeFiles/shaders_custom_uniform.dir/rule

# Convenience name for target.
shaders_custom_uniform: examples/CMakeFiles/shaders_custom_uniform.dir/rule
.PHONY : shaders_custom_uniform

# fast build rule for target.
shaders_custom_uniform/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_custom_uniform.dir/build.make examples/CMakeFiles/shaders_custom_uniform.dir/build
.PHONY : shaders_custom_uniform/fast

# Convenience name for target.
examples/CMakeFiles/shaders_eratosthenes.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_eratosthenes.dir/rule
.PHONY : examples/CMakeFiles/shaders_eratosthenes.dir/rule

# Convenience name for target.
shaders_eratosthenes: examples/CMakeFiles/shaders_eratosthenes.dir/rule
.PHONY : shaders_eratosthenes

# fast build rule for target.
shaders_eratosthenes/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_eratosthenes.dir/build.make examples/CMakeFiles/shaders_eratosthenes.dir/build
.PHONY : shaders_eratosthenes/fast

# Convenience name for target.
examples/CMakeFiles/shaders_fog.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_fog.dir/rule
.PHONY : examples/CMakeFiles/shaders_fog.dir/rule

# Convenience name for target.
shaders_fog: examples/CMakeFiles/shaders_fog.dir/rule
.PHONY : shaders_fog

# fast build rule for target.
shaders_fog/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_fog.dir/build.make examples/CMakeFiles/shaders_fog.dir/build
.PHONY : shaders_fog/fast

# Convenience name for target.
examples/CMakeFiles/shaders_hot_reloading.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_hot_reloading.dir/rule
.PHONY : examples/CMakeFiles/shaders_hot_reloading.dir/rule

# Convenience name for target.
shaders_hot_reloading: examples/CMakeFiles/shaders_hot_reloading.dir/rule
.PHONY : shaders_hot_reloading

# fast build rule for target.
shaders_hot_reloading/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_hot_reloading.dir/build.make examples/CMakeFiles/shaders_hot_reloading.dir/build
.PHONY : shaders_hot_reloading/fast

# Convenience name for target.
examples/CMakeFiles/shaders_hybrid_render.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_hybrid_render.dir/rule
.PHONY : examples/CMakeFiles/shaders_hybrid_render.dir/rule

# Convenience name for target.
shaders_hybrid_render: examples/CMakeFiles/shaders_hybrid_render.dir/rule
.PHONY : shaders_hybrid_render

# fast build rule for target.
shaders_hybrid_render/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_hybrid_render.dir/build.make examples/CMakeFiles/shaders_hybrid_render.dir/build
.PHONY : shaders_hybrid_render/fast

# Convenience name for target.
examples/CMakeFiles/shaders_julia_set.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_julia_set.dir/rule
.PHONY : examples/CMakeFiles/shaders_julia_set.dir/rule

# Convenience name for target.
shaders_julia_set: examples/CMakeFiles/shaders_julia_set.dir/rule
.PHONY : shaders_julia_set

# fast build rule for target.
shaders_julia_set/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_julia_set.dir/build.make examples/CMakeFiles/shaders_julia_set.dir/build
.PHONY : shaders_julia_set/fast

# Convenience name for target.
examples/CMakeFiles/shaders_mesh_instancing.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_mesh_instancing.dir/rule
.PHONY : examples/CMakeFiles/shaders_mesh_instancing.dir/rule

# Convenience name for target.
shaders_mesh_instancing: examples/CMakeFiles/shaders_mesh_instancing.dir/rule
.PHONY : shaders_mesh_instancing

# fast build rule for target.
shaders_mesh_instancing/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_mesh_instancing.dir/build.make examples/CMakeFiles/shaders_mesh_instancing.dir/build
.PHONY : shaders_mesh_instancing/fast

# Convenience name for target.
examples/CMakeFiles/shaders_model_shader.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_model_shader.dir/rule
.PHONY : examples/CMakeFiles/shaders_model_shader.dir/rule

# Convenience name for target.
shaders_model_shader: examples/CMakeFiles/shaders_model_shader.dir/rule
.PHONY : shaders_model_shader

# fast build rule for target.
shaders_model_shader/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_model_shader.dir/build.make examples/CMakeFiles/shaders_model_shader.dir/build
.PHONY : shaders_model_shader/fast

# Convenience name for target.
examples/CMakeFiles/shaders_multi_sample2d.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_multi_sample2d.dir/rule
.PHONY : examples/CMakeFiles/shaders_multi_sample2d.dir/rule

# Convenience name for target.
shaders_multi_sample2d: examples/CMakeFiles/shaders_multi_sample2d.dir/rule
.PHONY : shaders_multi_sample2d

# fast build rule for target.
shaders_multi_sample2d/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_multi_sample2d.dir/build.make examples/CMakeFiles/shaders_multi_sample2d.dir/build
.PHONY : shaders_multi_sample2d/fast

# Convenience name for target.
examples/CMakeFiles/shaders_palette_switch.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_palette_switch.dir/rule
.PHONY : examples/CMakeFiles/shaders_palette_switch.dir/rule

# Convenience name for target.
shaders_palette_switch: examples/CMakeFiles/shaders_palette_switch.dir/rule
.PHONY : shaders_palette_switch

# fast build rule for target.
shaders_palette_switch/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_palette_switch.dir/build.make examples/CMakeFiles/shaders_palette_switch.dir/build
.PHONY : shaders_palette_switch/fast

# Convenience name for target.
examples/CMakeFiles/shaders_postprocessing.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_postprocessing.dir/rule
.PHONY : examples/CMakeFiles/shaders_postprocessing.dir/rule

# Convenience name for target.
shaders_postprocessing: examples/CMakeFiles/shaders_postprocessing.dir/rule
.PHONY : shaders_postprocessing

# fast build rule for target.
shaders_postprocessing/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_postprocessing.dir/build.make examples/CMakeFiles/shaders_postprocessing.dir/build
.PHONY : shaders_postprocessing/fast

# Convenience name for target.
examples/CMakeFiles/shaders_raymarching.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_raymarching.dir/rule
.PHONY : examples/CMakeFiles/shaders_raymarching.dir/rule

# Convenience name for target.
shaders_raymarching: examples/CMakeFiles/shaders_raymarching.dir/rule
.PHONY : shaders_raymarching

# fast build rule for target.
shaders_raymarching/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_raymarching.dir/build.make examples/CMakeFiles/shaders_raymarching.dir/build
.PHONY : shaders_raymarching/fast

# Convenience name for target.
examples/CMakeFiles/shaders_shapes_textures.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_shapes_textures.dir/rule
.PHONY : examples/CMakeFiles/shaders_shapes_textures.dir/rule

# Convenience name for target.
shaders_shapes_textures: examples/CMakeFiles/shaders_shapes_textures.dir/rule
.PHONY : shaders_shapes_textures

# fast build rule for target.
shaders_shapes_textures/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_shapes_textures.dir/build.make examples/CMakeFiles/shaders_shapes_textures.dir/build
.PHONY : shaders_shapes_textures/fast

# Convenience name for target.
examples/CMakeFiles/shaders_simple_mask.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_simple_mask.dir/rule
.PHONY : examples/CMakeFiles/shaders_simple_mask.dir/rule

# Convenience name for target.
shaders_simple_mask: examples/CMakeFiles/shaders_simple_mask.dir/rule
.PHONY : shaders_simple_mask

# fast build rule for target.
shaders_simple_mask/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_simple_mask.dir/build.make examples/CMakeFiles/shaders_simple_mask.dir/build
.PHONY : shaders_simple_mask/fast

# Convenience name for target.
examples/CMakeFiles/shaders_spotlight.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_spotlight.dir/rule
.PHONY : examples/CMakeFiles/shaders_spotlight.dir/rule

# Convenience name for target.
shaders_spotlight: examples/CMakeFiles/shaders_spotlight.dir/rule
.PHONY : shaders_spotlight

# fast build rule for target.
shaders_spotlight/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_spotlight.dir/build.make examples/CMakeFiles/shaders_spotlight.dir/build
.PHONY : shaders_spotlight/fast

# Convenience name for target.
examples/CMakeFiles/shaders_texture_drawing.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_texture_drawing.dir/rule
.PHONY : examples/CMakeFiles/shaders_texture_drawing.dir/rule

# Convenience name for target.
shaders_texture_drawing: examples/CMakeFiles/shaders_texture_drawing.dir/rule
.PHONY : shaders_texture_drawing

# fast build rule for target.
shaders_texture_drawing/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_drawing.dir/build.make examples/CMakeFiles/shaders_texture_drawing.dir/build
.PHONY : shaders_texture_drawing/fast

# Convenience name for target.
examples/CMakeFiles/shaders_texture_outline.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_texture_outline.dir/rule
.PHONY : examples/CMakeFiles/shaders_texture_outline.dir/rule

# Convenience name for target.
shaders_texture_outline: examples/CMakeFiles/shaders_texture_outline.dir/rule
.PHONY : shaders_texture_outline

# fast build rule for target.
shaders_texture_outline/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_outline.dir/build.make examples/CMakeFiles/shaders_texture_outline.dir/build
.PHONY : shaders_texture_outline/fast

# Convenience name for target.
examples/CMakeFiles/shaders_texture_waves.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_texture_waves.dir/rule
.PHONY : examples/CMakeFiles/shaders_texture_waves.dir/rule

# Convenience name for target.
shaders_texture_waves: examples/CMakeFiles/shaders_texture_waves.dir/rule
.PHONY : shaders_texture_waves

# fast build rule for target.
shaders_texture_waves/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_waves.dir/build.make examples/CMakeFiles/shaders_texture_waves.dir/build
.PHONY : shaders_texture_waves/fast

# Convenience name for target.
examples/CMakeFiles/shaders_write_depth.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shaders_write_depth.dir/rule
.PHONY : examples/CMakeFiles/shaders_write_depth.dir/rule

# Convenience name for target.
shaders_write_depth: examples/CMakeFiles/shaders_write_depth.dir/rule
.PHONY : shaders_write_depth

# fast build rule for target.
shaders_write_depth/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_write_depth.dir/build.make examples/CMakeFiles/shaders_write_depth.dir/build
.PHONY : shaders_write_depth/fast

# Convenience name for target.
examples/CMakeFiles/shapes_basic_shapes.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_basic_shapes.dir/rule
.PHONY : examples/CMakeFiles/shapes_basic_shapes.dir/rule

# Convenience name for target.
shapes_basic_shapes: examples/CMakeFiles/shapes_basic_shapes.dir/rule
.PHONY : shapes_basic_shapes

# fast build rule for target.
shapes_basic_shapes/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_basic_shapes.dir/build.make examples/CMakeFiles/shapes_basic_shapes.dir/build
.PHONY : shapes_basic_shapes/fast

# Convenience name for target.
examples/CMakeFiles/shapes_bouncing_ball.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_bouncing_ball.dir/rule
.PHONY : examples/CMakeFiles/shapes_bouncing_ball.dir/rule

# Convenience name for target.
shapes_bouncing_ball: examples/CMakeFiles/shapes_bouncing_ball.dir/rule
.PHONY : shapes_bouncing_ball

# fast build rule for target.
shapes_bouncing_ball/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_bouncing_ball.dir/build.make examples/CMakeFiles/shapes_bouncing_ball.dir/build
.PHONY : shapes_bouncing_ball/fast

# Convenience name for target.
examples/CMakeFiles/shapes_collision_area.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_collision_area.dir/rule
.PHONY : examples/CMakeFiles/shapes_collision_area.dir/rule

# Convenience name for target.
shapes_collision_area: examples/CMakeFiles/shapes_collision_area.dir/rule
.PHONY : shapes_collision_area

# fast build rule for target.
shapes_collision_area/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_collision_area.dir/build.make examples/CMakeFiles/shapes_collision_area.dir/build
.PHONY : shapes_collision_area/fast

# Convenience name for target.
examples/CMakeFiles/shapes_colors_palette.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_colors_palette.dir/rule
.PHONY : examples/CMakeFiles/shapes_colors_palette.dir/rule

# Convenience name for target.
shapes_colors_palette: examples/CMakeFiles/shapes_colors_palette.dir/rule
.PHONY : shapes_colors_palette

# fast build rule for target.
shapes_colors_palette/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_colors_palette.dir/build.make examples/CMakeFiles/shapes_colors_palette.dir/build
.PHONY : shapes_colors_palette/fast

# Convenience name for target.
examples/CMakeFiles/shapes_draw_circle_sector.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_draw_circle_sector.dir/rule
.PHONY : examples/CMakeFiles/shapes_draw_circle_sector.dir/rule

# Convenience name for target.
shapes_draw_circle_sector: examples/CMakeFiles/shapes_draw_circle_sector.dir/rule
.PHONY : shapes_draw_circle_sector

# fast build rule for target.
shapes_draw_circle_sector/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_circle_sector.dir/build.make examples/CMakeFiles/shapes_draw_circle_sector.dir/build
.PHONY : shapes_draw_circle_sector/fast

# Convenience name for target.
examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/rule
.PHONY : examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/rule

# Convenience name for target.
shapes_draw_rectangle_rounded: examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/rule
.PHONY : shapes_draw_rectangle_rounded

# fast build rule for target.
shapes_draw_rectangle_rounded/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/build.make examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/build
.PHONY : shapes_draw_rectangle_rounded/fast

# Convenience name for target.
examples/CMakeFiles/shapes_draw_ring.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_draw_ring.dir/rule
.PHONY : examples/CMakeFiles/shapes_draw_ring.dir/rule

# Convenience name for target.
shapes_draw_ring: examples/CMakeFiles/shapes_draw_ring.dir/rule
.PHONY : shapes_draw_ring

# fast build rule for target.
shapes_draw_ring/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_ring.dir/build.make examples/CMakeFiles/shapes_draw_ring.dir/build
.PHONY : shapes_draw_ring/fast

# Convenience name for target.
examples/CMakeFiles/shapes_easings_ball_anim.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_easings_ball_anim.dir/rule
.PHONY : examples/CMakeFiles/shapes_easings_ball_anim.dir/rule

# Convenience name for target.
shapes_easings_ball_anim: examples/CMakeFiles/shapes_easings_ball_anim.dir/rule
.PHONY : shapes_easings_ball_anim

# fast build rule for target.
shapes_easings_ball_anim/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_ball_anim.dir/build.make examples/CMakeFiles/shapes_easings_ball_anim.dir/build
.PHONY : shapes_easings_ball_anim/fast

# Convenience name for target.
examples/CMakeFiles/shapes_easings_box_anim.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_easings_box_anim.dir/rule
.PHONY : examples/CMakeFiles/shapes_easings_box_anim.dir/rule

# Convenience name for target.
shapes_easings_box_anim: examples/CMakeFiles/shapes_easings_box_anim.dir/rule
.PHONY : shapes_easings_box_anim

# fast build rule for target.
shapes_easings_box_anim/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_box_anim.dir/build.make examples/CMakeFiles/shapes_easings_box_anim.dir/build
.PHONY : shapes_easings_box_anim/fast

# Convenience name for target.
examples/CMakeFiles/shapes_easings_rectangle_array.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_easings_rectangle_array.dir/rule
.PHONY : examples/CMakeFiles/shapes_easings_rectangle_array.dir/rule

# Convenience name for target.
shapes_easings_rectangle_array: examples/CMakeFiles/shapes_easings_rectangle_array.dir/rule
.PHONY : shapes_easings_rectangle_array

# fast build rule for target.
shapes_easings_rectangle_array/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_rectangle_array.dir/build.make examples/CMakeFiles/shapes_easings_rectangle_array.dir/build
.PHONY : shapes_easings_rectangle_array/fast

# Convenience name for target.
examples/CMakeFiles/shapes_following_eyes.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_following_eyes.dir/rule
.PHONY : examples/CMakeFiles/shapes_following_eyes.dir/rule

# Convenience name for target.
shapes_following_eyes: examples/CMakeFiles/shapes_following_eyes.dir/rule
.PHONY : shapes_following_eyes

# fast build rule for target.
shapes_following_eyes/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_following_eyes.dir/build.make examples/CMakeFiles/shapes_following_eyes.dir/build
.PHONY : shapes_following_eyes/fast

# Convenience name for target.
examples/CMakeFiles/shapes_lines_bezier.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_lines_bezier.dir/rule
.PHONY : examples/CMakeFiles/shapes_lines_bezier.dir/rule

# Convenience name for target.
shapes_lines_bezier: examples/CMakeFiles/shapes_lines_bezier.dir/rule
.PHONY : shapes_lines_bezier

# fast build rule for target.
shapes_lines_bezier/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_lines_bezier.dir/build.make examples/CMakeFiles/shapes_lines_bezier.dir/build
.PHONY : shapes_lines_bezier/fast

# Convenience name for target.
examples/CMakeFiles/shapes_logo_raylib.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_logo_raylib.dir/rule
.PHONY : examples/CMakeFiles/shapes_logo_raylib.dir/rule

# Convenience name for target.
shapes_logo_raylib: examples/CMakeFiles/shapes_logo_raylib.dir/rule
.PHONY : shapes_logo_raylib

# fast build rule for target.
shapes_logo_raylib/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_logo_raylib.dir/build.make examples/CMakeFiles/shapes_logo_raylib.dir/build
.PHONY : shapes_logo_raylib/fast

# Convenience name for target.
examples/CMakeFiles/shapes_logo_raylib_anim.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_logo_raylib_anim.dir/rule
.PHONY : examples/CMakeFiles/shapes_logo_raylib_anim.dir/rule

# Convenience name for target.
shapes_logo_raylib_anim: examples/CMakeFiles/shapes_logo_raylib_anim.dir/rule
.PHONY : shapes_logo_raylib_anim

# fast build rule for target.
shapes_logo_raylib_anim/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_logo_raylib_anim.dir/build.make examples/CMakeFiles/shapes_logo_raylib_anim.dir/build
.PHONY : shapes_logo_raylib_anim/fast

# Convenience name for target.
examples/CMakeFiles/shapes_rectangle_scaling.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_rectangle_scaling.dir/rule
.PHONY : examples/CMakeFiles/shapes_rectangle_scaling.dir/rule

# Convenience name for target.
shapes_rectangle_scaling: examples/CMakeFiles/shapes_rectangle_scaling.dir/rule
.PHONY : shapes_rectangle_scaling

# fast build rule for target.
shapes_rectangle_scaling/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_rectangle_scaling.dir/build.make examples/CMakeFiles/shapes_rectangle_scaling.dir/build
.PHONY : shapes_rectangle_scaling/fast

# Convenience name for target.
examples/CMakeFiles/shapes_top_down_lights.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/shapes_top_down_lights.dir/rule
.PHONY : examples/CMakeFiles/shapes_top_down_lights.dir/rule

# Convenience name for target.
shapes_top_down_lights: examples/CMakeFiles/shapes_top_down_lights.dir/rule
.PHONY : shapes_top_down_lights

# fast build rule for target.
shapes_top_down_lights/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_top_down_lights.dir/build.make examples/CMakeFiles/shapes_top_down_lights.dir/build
.PHONY : shapes_top_down_lights/fast

# Convenience name for target.
examples/CMakeFiles/text_codepoints_loading.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_codepoints_loading.dir/rule
.PHONY : examples/CMakeFiles/text_codepoints_loading.dir/rule

# Convenience name for target.
text_codepoints_loading: examples/CMakeFiles/text_codepoints_loading.dir/rule
.PHONY : text_codepoints_loading

# fast build rule for target.
text_codepoints_loading/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_codepoints_loading.dir/build.make examples/CMakeFiles/text_codepoints_loading.dir/build
.PHONY : text_codepoints_loading/fast

# Convenience name for target.
examples/CMakeFiles/text_draw_3d.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_draw_3d.dir/rule
.PHONY : examples/CMakeFiles/text_draw_3d.dir/rule

# Convenience name for target.
text_draw_3d: examples/CMakeFiles/text_draw_3d.dir/rule
.PHONY : text_draw_3d

# fast build rule for target.
text_draw_3d/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_draw_3d.dir/build.make examples/CMakeFiles/text_draw_3d.dir/build
.PHONY : text_draw_3d/fast

# Convenience name for target.
examples/CMakeFiles/text_font_filters.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_font_filters.dir/rule
.PHONY : examples/CMakeFiles/text_font_filters.dir/rule

# Convenience name for target.
text_font_filters: examples/CMakeFiles/text_font_filters.dir/rule
.PHONY : text_font_filters

# fast build rule for target.
text_font_filters/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_filters.dir/build.make examples/CMakeFiles/text_font_filters.dir/build
.PHONY : text_font_filters/fast

# Convenience name for target.
examples/CMakeFiles/text_font_loading.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_font_loading.dir/rule
.PHONY : examples/CMakeFiles/text_font_loading.dir/rule

# Convenience name for target.
text_font_loading: examples/CMakeFiles/text_font_loading.dir/rule
.PHONY : text_font_loading

# fast build rule for target.
text_font_loading/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_loading.dir/build.make examples/CMakeFiles/text_font_loading.dir/build
.PHONY : text_font_loading/fast

# Convenience name for target.
examples/CMakeFiles/text_font_sdf.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_font_sdf.dir/rule
.PHONY : examples/CMakeFiles/text_font_sdf.dir/rule

# Convenience name for target.
text_font_sdf: examples/CMakeFiles/text_font_sdf.dir/rule
.PHONY : text_font_sdf

# fast build rule for target.
text_font_sdf/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_sdf.dir/build.make examples/CMakeFiles/text_font_sdf.dir/build
.PHONY : text_font_sdf/fast

# Convenience name for target.
examples/CMakeFiles/text_font_spritefont.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_font_spritefont.dir/rule
.PHONY : examples/CMakeFiles/text_font_spritefont.dir/rule

# Convenience name for target.
text_font_spritefont: examples/CMakeFiles/text_font_spritefont.dir/rule
.PHONY : text_font_spritefont

# fast build rule for target.
text_font_spritefont/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_spritefont.dir/build.make examples/CMakeFiles/text_font_spritefont.dir/build
.PHONY : text_font_spritefont/fast

# Convenience name for target.
examples/CMakeFiles/text_format_text.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_format_text.dir/rule
.PHONY : examples/CMakeFiles/text_format_text.dir/rule

# Convenience name for target.
text_format_text: examples/CMakeFiles/text_format_text.dir/rule
.PHONY : text_format_text

# fast build rule for target.
text_format_text/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_format_text.dir/build.make examples/CMakeFiles/text_format_text.dir/build
.PHONY : text_format_text/fast

# Convenience name for target.
examples/CMakeFiles/text_input_box.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_input_box.dir/rule
.PHONY : examples/CMakeFiles/text_input_box.dir/rule

# Convenience name for target.
text_input_box: examples/CMakeFiles/text_input_box.dir/rule
.PHONY : text_input_box

# fast build rule for target.
text_input_box/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_input_box.dir/build.make examples/CMakeFiles/text_input_box.dir/build
.PHONY : text_input_box/fast

# Convenience name for target.
examples/CMakeFiles/text_raylib_fonts.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_raylib_fonts.dir/rule
.PHONY : examples/CMakeFiles/text_raylib_fonts.dir/rule

# Convenience name for target.
text_raylib_fonts: examples/CMakeFiles/text_raylib_fonts.dir/rule
.PHONY : text_raylib_fonts

# fast build rule for target.
text_raylib_fonts/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_raylib_fonts.dir/build.make examples/CMakeFiles/text_raylib_fonts.dir/build
.PHONY : text_raylib_fonts/fast

# Convenience name for target.
examples/CMakeFiles/text_rectangle_bounds.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_rectangle_bounds.dir/rule
.PHONY : examples/CMakeFiles/text_rectangle_bounds.dir/rule

# Convenience name for target.
text_rectangle_bounds: examples/CMakeFiles/text_rectangle_bounds.dir/rule
.PHONY : text_rectangle_bounds

# fast build rule for target.
text_rectangle_bounds/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_rectangle_bounds.dir/build.make examples/CMakeFiles/text_rectangle_bounds.dir/build
.PHONY : text_rectangle_bounds/fast

# Convenience name for target.
examples/CMakeFiles/text_unicode.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_unicode.dir/rule
.PHONY : examples/CMakeFiles/text_unicode.dir/rule

# Convenience name for target.
text_unicode: examples/CMakeFiles/text_unicode.dir/rule
.PHONY : text_unicode

# fast build rule for target.
text_unicode/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_unicode.dir/build.make examples/CMakeFiles/text_unicode.dir/build
.PHONY : text_unicode/fast

# Convenience name for target.
examples/CMakeFiles/text_writing_anim.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/text_writing_anim.dir/rule
.PHONY : examples/CMakeFiles/text_writing_anim.dir/rule

# Convenience name for target.
text_writing_anim: examples/CMakeFiles/text_writing_anim.dir/rule
.PHONY : text_writing_anim

# fast build rule for target.
text_writing_anim/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_writing_anim.dir/build.make examples/CMakeFiles/text_writing_anim.dir/build
.PHONY : text_writing_anim/fast

# Convenience name for target.
examples/CMakeFiles/textures_background_scrolling.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_background_scrolling.dir/rule
.PHONY : examples/CMakeFiles/textures_background_scrolling.dir/rule

# Convenience name for target.
textures_background_scrolling: examples/CMakeFiles/textures_background_scrolling.dir/rule
.PHONY : textures_background_scrolling

# fast build rule for target.
textures_background_scrolling/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_background_scrolling.dir/build.make examples/CMakeFiles/textures_background_scrolling.dir/build
.PHONY : textures_background_scrolling/fast

# Convenience name for target.
examples/CMakeFiles/textures_blend_modes.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_blend_modes.dir/rule
.PHONY : examples/CMakeFiles/textures_blend_modes.dir/rule

# Convenience name for target.
textures_blend_modes: examples/CMakeFiles/textures_blend_modes.dir/rule
.PHONY : textures_blend_modes

# fast build rule for target.
textures_blend_modes/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_blend_modes.dir/build.make examples/CMakeFiles/textures_blend_modes.dir/build
.PHONY : textures_blend_modes/fast

# Convenience name for target.
examples/CMakeFiles/textures_bunnymark.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_bunnymark.dir/rule
.PHONY : examples/CMakeFiles/textures_bunnymark.dir/rule

# Convenience name for target.
textures_bunnymark: examples/CMakeFiles/textures_bunnymark.dir/rule
.PHONY : textures_bunnymark

# fast build rule for target.
textures_bunnymark/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_bunnymark.dir/build.make examples/CMakeFiles/textures_bunnymark.dir/build
.PHONY : textures_bunnymark/fast

# Convenience name for target.
examples/CMakeFiles/textures_draw_tiled.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_draw_tiled.dir/rule
.PHONY : examples/CMakeFiles/textures_draw_tiled.dir/rule

# Convenience name for target.
textures_draw_tiled: examples/CMakeFiles/textures_draw_tiled.dir/rule
.PHONY : textures_draw_tiled

# fast build rule for target.
textures_draw_tiled/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_draw_tiled.dir/build.make examples/CMakeFiles/textures_draw_tiled.dir/build
.PHONY : textures_draw_tiled/fast

# Convenience name for target.
examples/CMakeFiles/textures_fog_of_war.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_fog_of_war.dir/rule
.PHONY : examples/CMakeFiles/textures_fog_of_war.dir/rule

# Convenience name for target.
textures_fog_of_war: examples/CMakeFiles/textures_fog_of_war.dir/rule
.PHONY : textures_fog_of_war

# fast build rule for target.
textures_fog_of_war/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_fog_of_war.dir/build.make examples/CMakeFiles/textures_fog_of_war.dir/build
.PHONY : textures_fog_of_war/fast

# Convenience name for target.
examples/CMakeFiles/textures_gif_player.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_gif_player.dir/rule
.PHONY : examples/CMakeFiles/textures_gif_player.dir/rule

# Convenience name for target.
textures_gif_player: examples/CMakeFiles/textures_gif_player.dir/rule
.PHONY : textures_gif_player

# fast build rule for target.
textures_gif_player/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_gif_player.dir/build.make examples/CMakeFiles/textures_gif_player.dir/build
.PHONY : textures_gif_player/fast

# Convenience name for target.
examples/CMakeFiles/textures_image_drawing.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_image_drawing.dir/rule
.PHONY : examples/CMakeFiles/textures_image_drawing.dir/rule

# Convenience name for target.
textures_image_drawing: examples/CMakeFiles/textures_image_drawing.dir/rule
.PHONY : textures_image_drawing

# fast build rule for target.
textures_image_drawing/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_drawing.dir/build.make examples/CMakeFiles/textures_image_drawing.dir/build
.PHONY : textures_image_drawing/fast

# Convenience name for target.
examples/CMakeFiles/textures_image_generation.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_image_generation.dir/rule
.PHONY : examples/CMakeFiles/textures_image_generation.dir/rule

# Convenience name for target.
textures_image_generation: examples/CMakeFiles/textures_image_generation.dir/rule
.PHONY : textures_image_generation

# fast build rule for target.
textures_image_generation/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_generation.dir/build.make examples/CMakeFiles/textures_image_generation.dir/build
.PHONY : textures_image_generation/fast

# Convenience name for target.
examples/CMakeFiles/textures_image_loading.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_image_loading.dir/rule
.PHONY : examples/CMakeFiles/textures_image_loading.dir/rule

# Convenience name for target.
textures_image_loading: examples/CMakeFiles/textures_image_loading.dir/rule
.PHONY : textures_image_loading

# fast build rule for target.
textures_image_loading/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_loading.dir/build.make examples/CMakeFiles/textures_image_loading.dir/build
.PHONY : textures_image_loading/fast

# Convenience name for target.
examples/CMakeFiles/textures_image_processing.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_image_processing.dir/rule
.PHONY : examples/CMakeFiles/textures_image_processing.dir/rule

# Convenience name for target.
textures_image_processing: examples/CMakeFiles/textures_image_processing.dir/rule
.PHONY : textures_image_processing

# fast build rule for target.
textures_image_processing/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_processing.dir/build.make examples/CMakeFiles/textures_image_processing.dir/build
.PHONY : textures_image_processing/fast

# Convenience name for target.
examples/CMakeFiles/textures_image_text.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_image_text.dir/rule
.PHONY : examples/CMakeFiles/textures_image_text.dir/rule

# Convenience name for target.
textures_image_text: examples/CMakeFiles/textures_image_text.dir/rule
.PHONY : textures_image_text

# fast build rule for target.
textures_image_text/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_text.dir/build.make examples/CMakeFiles/textures_image_text.dir/build
.PHONY : textures_image_text/fast

# Convenience name for target.
examples/CMakeFiles/textures_logo_raylib.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_logo_raylib.dir/rule
.PHONY : examples/CMakeFiles/textures_logo_raylib.dir/rule

# Convenience name for target.
textures_logo_raylib: examples/CMakeFiles/textures_logo_raylib.dir/rule
.PHONY : textures_logo_raylib

# fast build rule for target.
textures_logo_raylib/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_logo_raylib.dir/build.make examples/CMakeFiles/textures_logo_raylib.dir/build
.PHONY : textures_logo_raylib/fast

# Convenience name for target.
examples/CMakeFiles/textures_mouse_painting.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_mouse_painting.dir/rule
.PHONY : examples/CMakeFiles/textures_mouse_painting.dir/rule

# Convenience name for target.
textures_mouse_painting: examples/CMakeFiles/textures_mouse_painting.dir/rule
.PHONY : textures_mouse_painting

# fast build rule for target.
textures_mouse_painting/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_mouse_painting.dir/build.make examples/CMakeFiles/textures_mouse_painting.dir/build
.PHONY : textures_mouse_painting/fast

# Convenience name for target.
examples/CMakeFiles/textures_npatch_drawing.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_npatch_drawing.dir/rule
.PHONY : examples/CMakeFiles/textures_npatch_drawing.dir/rule

# Convenience name for target.
textures_npatch_drawing: examples/CMakeFiles/textures_npatch_drawing.dir/rule
.PHONY : textures_npatch_drawing

# fast build rule for target.
textures_npatch_drawing/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_npatch_drawing.dir/build.make examples/CMakeFiles/textures_npatch_drawing.dir/build
.PHONY : textures_npatch_drawing/fast

# Convenience name for target.
examples/CMakeFiles/textures_particles_blending.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_particles_blending.dir/rule
.PHONY : examples/CMakeFiles/textures_particles_blending.dir/rule

# Convenience name for target.
textures_particles_blending: examples/CMakeFiles/textures_particles_blending.dir/rule
.PHONY : textures_particles_blending

# fast build rule for target.
textures_particles_blending/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_particles_blending.dir/build.make examples/CMakeFiles/textures_particles_blending.dir/build
.PHONY : textures_particles_blending/fast

# Convenience name for target.
examples/CMakeFiles/textures_polygon.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_polygon.dir/rule
.PHONY : examples/CMakeFiles/textures_polygon.dir/rule

# Convenience name for target.
textures_polygon: examples/CMakeFiles/textures_polygon.dir/rule
.PHONY : textures_polygon

# fast build rule for target.
textures_polygon/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_polygon.dir/build.make examples/CMakeFiles/textures_polygon.dir/build
.PHONY : textures_polygon/fast

# Convenience name for target.
examples/CMakeFiles/textures_raw_data.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_raw_data.dir/rule
.PHONY : examples/CMakeFiles/textures_raw_data.dir/rule

# Convenience name for target.
textures_raw_data: examples/CMakeFiles/textures_raw_data.dir/rule
.PHONY : textures_raw_data

# fast build rule for target.
textures_raw_data/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_raw_data.dir/build.make examples/CMakeFiles/textures_raw_data.dir/build
.PHONY : textures_raw_data/fast

# Convenience name for target.
examples/CMakeFiles/textures_sprite_anim.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_sprite_anim.dir/rule
.PHONY : examples/CMakeFiles/textures_sprite_anim.dir/rule

# Convenience name for target.
textures_sprite_anim: examples/CMakeFiles/textures_sprite_anim.dir/rule
.PHONY : textures_sprite_anim

# fast build rule for target.
textures_sprite_anim/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_anim.dir/build.make examples/CMakeFiles/textures_sprite_anim.dir/build
.PHONY : textures_sprite_anim/fast

# Convenience name for target.
examples/CMakeFiles/textures_sprite_button.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_sprite_button.dir/rule
.PHONY : examples/CMakeFiles/textures_sprite_button.dir/rule

# Convenience name for target.
textures_sprite_button: examples/CMakeFiles/textures_sprite_button.dir/rule
.PHONY : textures_sprite_button

# fast build rule for target.
textures_sprite_button/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_button.dir/build.make examples/CMakeFiles/textures_sprite_button.dir/build
.PHONY : textures_sprite_button/fast

# Convenience name for target.
examples/CMakeFiles/textures_sprite_explosion.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_sprite_explosion.dir/rule
.PHONY : examples/CMakeFiles/textures_sprite_explosion.dir/rule

# Convenience name for target.
textures_sprite_explosion: examples/CMakeFiles/textures_sprite_explosion.dir/rule
.PHONY : textures_sprite_explosion

# fast build rule for target.
textures_sprite_explosion/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_explosion.dir/build.make examples/CMakeFiles/textures_sprite_explosion.dir/build
.PHONY : textures_sprite_explosion/fast

# Convenience name for target.
examples/CMakeFiles/textures_srcrec_dstrec.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_srcrec_dstrec.dir/rule
.PHONY : examples/CMakeFiles/textures_srcrec_dstrec.dir/rule

# Convenience name for target.
textures_srcrec_dstrec: examples/CMakeFiles/textures_srcrec_dstrec.dir/rule
.PHONY : textures_srcrec_dstrec

# fast build rule for target.
textures_srcrec_dstrec/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_srcrec_dstrec.dir/build.make examples/CMakeFiles/textures_srcrec_dstrec.dir/build
.PHONY : textures_srcrec_dstrec/fast

# Convenience name for target.
examples/CMakeFiles/textures_textured_curve.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_textured_curve.dir/rule
.PHONY : examples/CMakeFiles/textures_textured_curve.dir/rule

# Convenience name for target.
textures_textured_curve: examples/CMakeFiles/textures_textured_curve.dir/rule
.PHONY : textures_textured_curve

# fast build rule for target.
textures_textured_curve/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_textured_curve.dir/build.make examples/CMakeFiles/textures_textured_curve.dir/build
.PHONY : textures_textured_curve/fast

# Convenience name for target.
examples/CMakeFiles/textures_to_image.dir/rule:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 examples/CMakeFiles/textures_to_image.dir/rule
.PHONY : examples/CMakeFiles/textures_to_image.dir/rule

# Convenience name for target.
textures_to_image: examples/CMakeFiles/textures_to_image.dir/rule
.PHONY : textures_to_image

# fast build rule for target.
textures_to_image/fast:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_to_image.dir/build.make examples/CMakeFiles/textures_to_image.dir/build
.PHONY : textures_to_image/fast

audio/audio_mixed_processor.o: audio/audio_mixed_processor.c.o
.PHONY : audio/audio_mixed_processor.o

# target to build an object file
audio/audio_mixed_processor.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_mixed_processor.dir/build.make examples/CMakeFiles/audio_mixed_processor.dir/audio/audio_mixed_processor.c.o
.PHONY : audio/audio_mixed_processor.c.o

audio/audio_mixed_processor.i: audio/audio_mixed_processor.c.i
.PHONY : audio/audio_mixed_processor.i

# target to preprocess a source file
audio/audio_mixed_processor.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_mixed_processor.dir/build.make examples/CMakeFiles/audio_mixed_processor.dir/audio/audio_mixed_processor.c.i
.PHONY : audio/audio_mixed_processor.c.i

audio/audio_mixed_processor.s: audio/audio_mixed_processor.c.s
.PHONY : audio/audio_mixed_processor.s

# target to generate assembly for a file
audio/audio_mixed_processor.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_mixed_processor.dir/build.make examples/CMakeFiles/audio_mixed_processor.dir/audio/audio_mixed_processor.c.s
.PHONY : audio/audio_mixed_processor.c.s

audio/audio_module_playing.o: audio/audio_module_playing.c.o
.PHONY : audio/audio_module_playing.o

# target to build an object file
audio/audio_module_playing.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_module_playing.dir/build.make examples/CMakeFiles/audio_module_playing.dir/audio/audio_module_playing.c.o
.PHONY : audio/audio_module_playing.c.o

audio/audio_module_playing.i: audio/audio_module_playing.c.i
.PHONY : audio/audio_module_playing.i

# target to preprocess a source file
audio/audio_module_playing.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_module_playing.dir/build.make examples/CMakeFiles/audio_module_playing.dir/audio/audio_module_playing.c.i
.PHONY : audio/audio_module_playing.c.i

audio/audio_module_playing.s: audio/audio_module_playing.c.s
.PHONY : audio/audio_module_playing.s

# target to generate assembly for a file
audio/audio_module_playing.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_module_playing.dir/build.make examples/CMakeFiles/audio_module_playing.dir/audio/audio_module_playing.c.s
.PHONY : audio/audio_module_playing.c.s

audio/audio_music_stream.o: audio/audio_music_stream.c.o
.PHONY : audio/audio_music_stream.o

# target to build an object file
audio/audio_music_stream.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_music_stream.dir/build.make examples/CMakeFiles/audio_music_stream.dir/audio/audio_music_stream.c.o
.PHONY : audio/audio_music_stream.c.o

audio/audio_music_stream.i: audio/audio_music_stream.c.i
.PHONY : audio/audio_music_stream.i

# target to preprocess a source file
audio/audio_music_stream.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_music_stream.dir/build.make examples/CMakeFiles/audio_music_stream.dir/audio/audio_music_stream.c.i
.PHONY : audio/audio_music_stream.c.i

audio/audio_music_stream.s: audio/audio_music_stream.c.s
.PHONY : audio/audio_music_stream.s

# target to generate assembly for a file
audio/audio_music_stream.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_music_stream.dir/build.make examples/CMakeFiles/audio_music_stream.dir/audio/audio_music_stream.c.s
.PHONY : audio/audio_music_stream.c.s

audio/audio_raw_stream.o: audio/audio_raw_stream.c.o
.PHONY : audio/audio_raw_stream.o

# target to build an object file
audio/audio_raw_stream.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_raw_stream.dir/build.make examples/CMakeFiles/audio_raw_stream.dir/audio/audio_raw_stream.c.o
.PHONY : audio/audio_raw_stream.c.o

audio/audio_raw_stream.i: audio/audio_raw_stream.c.i
.PHONY : audio/audio_raw_stream.i

# target to preprocess a source file
audio/audio_raw_stream.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_raw_stream.dir/build.make examples/CMakeFiles/audio_raw_stream.dir/audio/audio_raw_stream.c.i
.PHONY : audio/audio_raw_stream.c.i

audio/audio_raw_stream.s: audio/audio_raw_stream.c.s
.PHONY : audio/audio_raw_stream.s

# target to generate assembly for a file
audio/audio_raw_stream.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_raw_stream.dir/build.make examples/CMakeFiles/audio_raw_stream.dir/audio/audio_raw_stream.c.s
.PHONY : audio/audio_raw_stream.c.s

audio/audio_sound_loading.o: audio/audio_sound_loading.c.o
.PHONY : audio/audio_sound_loading.o

# target to build an object file
audio/audio_sound_loading.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_sound_loading.dir/build.make examples/CMakeFiles/audio_sound_loading.dir/audio/audio_sound_loading.c.o
.PHONY : audio/audio_sound_loading.c.o

audio/audio_sound_loading.i: audio/audio_sound_loading.c.i
.PHONY : audio/audio_sound_loading.i

# target to preprocess a source file
audio/audio_sound_loading.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_sound_loading.dir/build.make examples/CMakeFiles/audio_sound_loading.dir/audio/audio_sound_loading.c.i
.PHONY : audio/audio_sound_loading.c.i

audio/audio_sound_loading.s: audio/audio_sound_loading.c.s
.PHONY : audio/audio_sound_loading.s

# target to generate assembly for a file
audio/audio_sound_loading.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_sound_loading.dir/build.make examples/CMakeFiles/audio_sound_loading.dir/audio/audio_sound_loading.c.s
.PHONY : audio/audio_sound_loading.c.s

audio/audio_stream_effects.o: audio/audio_stream_effects.c.o
.PHONY : audio/audio_stream_effects.o

# target to build an object file
audio/audio_stream_effects.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_stream_effects.dir/build.make examples/CMakeFiles/audio_stream_effects.dir/audio/audio_stream_effects.c.o
.PHONY : audio/audio_stream_effects.c.o

audio/audio_stream_effects.i: audio/audio_stream_effects.c.i
.PHONY : audio/audio_stream_effects.i

# target to preprocess a source file
audio/audio_stream_effects.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_stream_effects.dir/build.make examples/CMakeFiles/audio_stream_effects.dir/audio/audio_stream_effects.c.i
.PHONY : audio/audio_stream_effects.c.i

audio/audio_stream_effects.s: audio/audio_stream_effects.c.s
.PHONY : audio/audio_stream_effects.s

# target to generate assembly for a file
audio/audio_stream_effects.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/audio_stream_effects.dir/build.make examples/CMakeFiles/audio_stream_effects.dir/audio/audio_stream_effects.c.s
.PHONY : audio/audio_stream_effects.c.s

core/core_2d_camera.o: core/core_2d_camera.c.o
.PHONY : core/core_2d_camera.o

# target to build an object file
core/core_2d_camera.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera.dir/build.make examples/CMakeFiles/core_2d_camera.dir/core/core_2d_camera.c.o
.PHONY : core/core_2d_camera.c.o

core/core_2d_camera.i: core/core_2d_camera.c.i
.PHONY : core/core_2d_camera.i

# target to preprocess a source file
core/core_2d_camera.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera.dir/build.make examples/CMakeFiles/core_2d_camera.dir/core/core_2d_camera.c.i
.PHONY : core/core_2d_camera.c.i

core/core_2d_camera.s: core/core_2d_camera.c.s
.PHONY : core/core_2d_camera.s

# target to generate assembly for a file
core/core_2d_camera.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera.dir/build.make examples/CMakeFiles/core_2d_camera.dir/core/core_2d_camera.c.s
.PHONY : core/core_2d_camera.c.s

core/core_2d_camera_mouse_zoom.o: core/core_2d_camera_mouse_zoom.c.o
.PHONY : core/core_2d_camera_mouse_zoom.o

# target to build an object file
core/core_2d_camera_mouse_zoom.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/build.make examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/core/core_2d_camera_mouse_zoom.c.o
.PHONY : core/core_2d_camera_mouse_zoom.c.o

core/core_2d_camera_mouse_zoom.i: core/core_2d_camera_mouse_zoom.c.i
.PHONY : core/core_2d_camera_mouse_zoom.i

# target to preprocess a source file
core/core_2d_camera_mouse_zoom.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/build.make examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/core/core_2d_camera_mouse_zoom.c.i
.PHONY : core/core_2d_camera_mouse_zoom.c.i

core/core_2d_camera_mouse_zoom.s: core/core_2d_camera_mouse_zoom.c.s
.PHONY : core/core_2d_camera_mouse_zoom.s

# target to generate assembly for a file
core/core_2d_camera_mouse_zoom.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/build.make examples/CMakeFiles/core_2d_camera_mouse_zoom.dir/core/core_2d_camera_mouse_zoom.c.s
.PHONY : core/core_2d_camera_mouse_zoom.c.s

core/core_2d_camera_platformer.o: core/core_2d_camera_platformer.c.o
.PHONY : core/core_2d_camera_platformer.o

# target to build an object file
core/core_2d_camera_platformer.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera_platformer.dir/build.make examples/CMakeFiles/core_2d_camera_platformer.dir/core/core_2d_camera_platformer.c.o
.PHONY : core/core_2d_camera_platformer.c.o

core/core_2d_camera_platformer.i: core/core_2d_camera_platformer.c.i
.PHONY : core/core_2d_camera_platformer.i

# target to preprocess a source file
core/core_2d_camera_platformer.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera_platformer.dir/build.make examples/CMakeFiles/core_2d_camera_platformer.dir/core/core_2d_camera_platformer.c.i
.PHONY : core/core_2d_camera_platformer.c.i

core/core_2d_camera_platformer.s: core/core_2d_camera_platformer.c.s
.PHONY : core/core_2d_camera_platformer.s

# target to generate assembly for a file
core/core_2d_camera_platformer.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_2d_camera_platformer.dir/build.make examples/CMakeFiles/core_2d_camera_platformer.dir/core/core_2d_camera_platformer.c.s
.PHONY : core/core_2d_camera_platformer.c.s

core/core_3d_camera_first_person.o: core/core_3d_camera_first_person.c.o
.PHONY : core/core_3d_camera_first_person.o

# target to build an object file
core/core_3d_camera_first_person.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_first_person.dir/build.make examples/CMakeFiles/core_3d_camera_first_person.dir/core/core_3d_camera_first_person.c.o
.PHONY : core/core_3d_camera_first_person.c.o

core/core_3d_camera_first_person.i: core/core_3d_camera_first_person.c.i
.PHONY : core/core_3d_camera_first_person.i

# target to preprocess a source file
core/core_3d_camera_first_person.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_first_person.dir/build.make examples/CMakeFiles/core_3d_camera_first_person.dir/core/core_3d_camera_first_person.c.i
.PHONY : core/core_3d_camera_first_person.c.i

core/core_3d_camera_first_person.s: core/core_3d_camera_first_person.c.s
.PHONY : core/core_3d_camera_first_person.s

# target to generate assembly for a file
core/core_3d_camera_first_person.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_first_person.dir/build.make examples/CMakeFiles/core_3d_camera_first_person.dir/core/core_3d_camera_first_person.c.s
.PHONY : core/core_3d_camera_first_person.c.s

core/core_3d_camera_free.o: core/core_3d_camera_free.c.o
.PHONY : core/core_3d_camera_free.o

# target to build an object file
core/core_3d_camera_free.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_free.dir/build.make examples/CMakeFiles/core_3d_camera_free.dir/core/core_3d_camera_free.c.o
.PHONY : core/core_3d_camera_free.c.o

core/core_3d_camera_free.i: core/core_3d_camera_free.c.i
.PHONY : core/core_3d_camera_free.i

# target to preprocess a source file
core/core_3d_camera_free.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_free.dir/build.make examples/CMakeFiles/core_3d_camera_free.dir/core/core_3d_camera_free.c.i
.PHONY : core/core_3d_camera_free.c.i

core/core_3d_camera_free.s: core/core_3d_camera_free.c.s
.PHONY : core/core_3d_camera_free.s

# target to generate assembly for a file
core/core_3d_camera_free.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_free.dir/build.make examples/CMakeFiles/core_3d_camera_free.dir/core/core_3d_camera_free.c.s
.PHONY : core/core_3d_camera_free.c.s

core/core_3d_camera_mode.o: core/core_3d_camera_mode.c.o
.PHONY : core/core_3d_camera_mode.o

# target to build an object file
core/core_3d_camera_mode.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_mode.dir/build.make examples/CMakeFiles/core_3d_camera_mode.dir/core/core_3d_camera_mode.c.o
.PHONY : core/core_3d_camera_mode.c.o

core/core_3d_camera_mode.i: core/core_3d_camera_mode.c.i
.PHONY : core/core_3d_camera_mode.i

# target to preprocess a source file
core/core_3d_camera_mode.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_mode.dir/build.make examples/CMakeFiles/core_3d_camera_mode.dir/core/core_3d_camera_mode.c.i
.PHONY : core/core_3d_camera_mode.c.i

core/core_3d_camera_mode.s: core/core_3d_camera_mode.c.s
.PHONY : core/core_3d_camera_mode.s

# target to generate assembly for a file
core/core_3d_camera_mode.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_camera_mode.dir/build.make examples/CMakeFiles/core_3d_camera_mode.dir/core/core_3d_camera_mode.c.s
.PHONY : core/core_3d_camera_mode.c.s

core/core_3d_picking.o: core/core_3d_picking.c.o
.PHONY : core/core_3d_picking.o

# target to build an object file
core/core_3d_picking.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_picking.dir/build.make examples/CMakeFiles/core_3d_picking.dir/core/core_3d_picking.c.o
.PHONY : core/core_3d_picking.c.o

core/core_3d_picking.i: core/core_3d_picking.c.i
.PHONY : core/core_3d_picking.i

# target to preprocess a source file
core/core_3d_picking.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_picking.dir/build.make examples/CMakeFiles/core_3d_picking.dir/core/core_3d_picking.c.i
.PHONY : core/core_3d_picking.c.i

core/core_3d_picking.s: core/core_3d_picking.c.s
.PHONY : core/core_3d_picking.s

# target to generate assembly for a file
core/core_3d_picking.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_3d_picking.dir/build.make examples/CMakeFiles/core_3d_picking.dir/core/core_3d_picking.c.s
.PHONY : core/core_3d_picking.c.s

core/core_basic_screen_manager.o: core/core_basic_screen_manager.c.o
.PHONY : core/core_basic_screen_manager.o

# target to build an object file
core/core_basic_screen_manager.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_screen_manager.dir/build.make examples/CMakeFiles/core_basic_screen_manager.dir/core/core_basic_screen_manager.c.o
.PHONY : core/core_basic_screen_manager.c.o

core/core_basic_screen_manager.i: core/core_basic_screen_manager.c.i
.PHONY : core/core_basic_screen_manager.i

# target to preprocess a source file
core/core_basic_screen_manager.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_screen_manager.dir/build.make examples/CMakeFiles/core_basic_screen_manager.dir/core/core_basic_screen_manager.c.i
.PHONY : core/core_basic_screen_manager.c.i

core/core_basic_screen_manager.s: core/core_basic_screen_manager.c.s
.PHONY : core/core_basic_screen_manager.s

# target to generate assembly for a file
core/core_basic_screen_manager.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_screen_manager.dir/build.make examples/CMakeFiles/core_basic_screen_manager.dir/core/core_basic_screen_manager.c.s
.PHONY : core/core_basic_screen_manager.c.s

core/core_basic_window.o: core/core_basic_window.c.o
.PHONY : core/core_basic_window.o

# target to build an object file
core/core_basic_window.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_window.dir/build.make examples/CMakeFiles/core_basic_window.dir/core/core_basic_window.c.o
.PHONY : core/core_basic_window.c.o

core/core_basic_window.i: core/core_basic_window.c.i
.PHONY : core/core_basic_window.i

# target to preprocess a source file
core/core_basic_window.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_window.dir/build.make examples/CMakeFiles/core_basic_window.dir/core/core_basic_window.c.i
.PHONY : core/core_basic_window.c.i

core/core_basic_window.s: core/core_basic_window.c.s
.PHONY : core/core_basic_window.s

# target to generate assembly for a file
core/core_basic_window.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_window.dir/build.make examples/CMakeFiles/core_basic_window.dir/core/core_basic_window.c.s
.PHONY : core/core_basic_window.c.s

core/core_basic_window_web.o: core/core_basic_window_web.c.o
.PHONY : core/core_basic_window_web.o

# target to build an object file
core/core_basic_window_web.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_window_web.dir/build.make examples/CMakeFiles/core_basic_window_web.dir/core/core_basic_window_web.c.o
.PHONY : core/core_basic_window_web.c.o

core/core_basic_window_web.i: core/core_basic_window_web.c.i
.PHONY : core/core_basic_window_web.i

# target to preprocess a source file
core/core_basic_window_web.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_window_web.dir/build.make examples/CMakeFiles/core_basic_window_web.dir/core/core_basic_window_web.c.i
.PHONY : core/core_basic_window_web.c.i

core/core_basic_window_web.s: core/core_basic_window_web.c.s
.PHONY : core/core_basic_window_web.s

# target to generate assembly for a file
core/core_basic_window_web.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_basic_window_web.dir/build.make examples/CMakeFiles/core_basic_window_web.dir/core/core_basic_window_web.c.s
.PHONY : core/core_basic_window_web.c.s

core/core_custom_frame_control.o: core/core_custom_frame_control.c.o
.PHONY : core/core_custom_frame_control.o

# target to build an object file
core/core_custom_frame_control.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_custom_frame_control.dir/build.make examples/CMakeFiles/core_custom_frame_control.dir/core/core_custom_frame_control.c.o
.PHONY : core/core_custom_frame_control.c.o

core/core_custom_frame_control.i: core/core_custom_frame_control.c.i
.PHONY : core/core_custom_frame_control.i

# target to preprocess a source file
core/core_custom_frame_control.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_custom_frame_control.dir/build.make examples/CMakeFiles/core_custom_frame_control.dir/core/core_custom_frame_control.c.i
.PHONY : core/core_custom_frame_control.c.i

core/core_custom_frame_control.s: core/core_custom_frame_control.c.s
.PHONY : core/core_custom_frame_control.s

# target to generate assembly for a file
core/core_custom_frame_control.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_custom_frame_control.dir/build.make examples/CMakeFiles/core_custom_frame_control.dir/core/core_custom_frame_control.c.s
.PHONY : core/core_custom_frame_control.c.s

core/core_custom_logging.o: core/core_custom_logging.c.o
.PHONY : core/core_custom_logging.o

# target to build an object file
core/core_custom_logging.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_custom_logging.dir/build.make examples/CMakeFiles/core_custom_logging.dir/core/core_custom_logging.c.o
.PHONY : core/core_custom_logging.c.o

core/core_custom_logging.i: core/core_custom_logging.c.i
.PHONY : core/core_custom_logging.i

# target to preprocess a source file
core/core_custom_logging.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_custom_logging.dir/build.make examples/CMakeFiles/core_custom_logging.dir/core/core_custom_logging.c.i
.PHONY : core/core_custom_logging.c.i

core/core_custom_logging.s: core/core_custom_logging.c.s
.PHONY : core/core_custom_logging.s

# target to generate assembly for a file
core/core_custom_logging.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_custom_logging.dir/build.make examples/CMakeFiles/core_custom_logging.dir/core/core_custom_logging.c.s
.PHONY : core/core_custom_logging.c.s

core/core_drop_files.o: core/core_drop_files.c.o
.PHONY : core/core_drop_files.o

# target to build an object file
core/core_drop_files.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_drop_files.dir/build.make examples/CMakeFiles/core_drop_files.dir/core/core_drop_files.c.o
.PHONY : core/core_drop_files.c.o

core/core_drop_files.i: core/core_drop_files.c.i
.PHONY : core/core_drop_files.i

# target to preprocess a source file
core/core_drop_files.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_drop_files.dir/build.make examples/CMakeFiles/core_drop_files.dir/core/core_drop_files.c.i
.PHONY : core/core_drop_files.c.i

core/core_drop_files.s: core/core_drop_files.c.s
.PHONY : core/core_drop_files.s

# target to generate assembly for a file
core/core_drop_files.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_drop_files.dir/build.make examples/CMakeFiles/core_drop_files.dir/core/core_drop_files.c.s
.PHONY : core/core_drop_files.c.s

core/core_input_gamepad.o: core/core_input_gamepad.c.o
.PHONY : core/core_input_gamepad.o

# target to build an object file
core/core_input_gamepad.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_gamepad.dir/build.make examples/CMakeFiles/core_input_gamepad.dir/core/core_input_gamepad.c.o
.PHONY : core/core_input_gamepad.c.o

core/core_input_gamepad.i: core/core_input_gamepad.c.i
.PHONY : core/core_input_gamepad.i

# target to preprocess a source file
core/core_input_gamepad.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_gamepad.dir/build.make examples/CMakeFiles/core_input_gamepad.dir/core/core_input_gamepad.c.i
.PHONY : core/core_input_gamepad.c.i

core/core_input_gamepad.s: core/core_input_gamepad.c.s
.PHONY : core/core_input_gamepad.s

# target to generate assembly for a file
core/core_input_gamepad.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_gamepad.dir/build.make examples/CMakeFiles/core_input_gamepad.dir/core/core_input_gamepad.c.s
.PHONY : core/core_input_gamepad.c.s

core/core_input_gestures.o: core/core_input_gestures.c.o
.PHONY : core/core_input_gestures.o

# target to build an object file
core/core_input_gestures.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_gestures.dir/build.make examples/CMakeFiles/core_input_gestures.dir/core/core_input_gestures.c.o
.PHONY : core/core_input_gestures.c.o

core/core_input_gestures.i: core/core_input_gestures.c.i
.PHONY : core/core_input_gestures.i

# target to preprocess a source file
core/core_input_gestures.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_gestures.dir/build.make examples/CMakeFiles/core_input_gestures.dir/core/core_input_gestures.c.i
.PHONY : core/core_input_gestures.c.i

core/core_input_gestures.s: core/core_input_gestures.c.s
.PHONY : core/core_input_gestures.s

# target to generate assembly for a file
core/core_input_gestures.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_gestures.dir/build.make examples/CMakeFiles/core_input_gestures.dir/core/core_input_gestures.c.s
.PHONY : core/core_input_gestures.c.s

core/core_input_keys.o: core/core_input_keys.c.o
.PHONY : core/core_input_keys.o

# target to build an object file
core/core_input_keys.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_keys.dir/build.make examples/CMakeFiles/core_input_keys.dir/core/core_input_keys.c.o
.PHONY : core/core_input_keys.c.o

core/core_input_keys.i: core/core_input_keys.c.i
.PHONY : core/core_input_keys.i

# target to preprocess a source file
core/core_input_keys.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_keys.dir/build.make examples/CMakeFiles/core_input_keys.dir/core/core_input_keys.c.i
.PHONY : core/core_input_keys.c.i

core/core_input_keys.s: core/core_input_keys.c.s
.PHONY : core/core_input_keys.s

# target to generate assembly for a file
core/core_input_keys.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_keys.dir/build.make examples/CMakeFiles/core_input_keys.dir/core/core_input_keys.c.s
.PHONY : core/core_input_keys.c.s

core/core_input_mouse.o: core/core_input_mouse.c.o
.PHONY : core/core_input_mouse.o

# target to build an object file
core/core_input_mouse.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_mouse.dir/build.make examples/CMakeFiles/core_input_mouse.dir/core/core_input_mouse.c.o
.PHONY : core/core_input_mouse.c.o

core/core_input_mouse.i: core/core_input_mouse.c.i
.PHONY : core/core_input_mouse.i

# target to preprocess a source file
core/core_input_mouse.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_mouse.dir/build.make examples/CMakeFiles/core_input_mouse.dir/core/core_input_mouse.c.i
.PHONY : core/core_input_mouse.c.i

core/core_input_mouse.s: core/core_input_mouse.c.s
.PHONY : core/core_input_mouse.s

# target to generate assembly for a file
core/core_input_mouse.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_mouse.dir/build.make examples/CMakeFiles/core_input_mouse.dir/core/core_input_mouse.c.s
.PHONY : core/core_input_mouse.c.s

core/core_input_mouse_wheel.o: core/core_input_mouse_wheel.c.o
.PHONY : core/core_input_mouse_wheel.o

# target to build an object file
core/core_input_mouse_wheel.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_mouse_wheel.dir/build.make examples/CMakeFiles/core_input_mouse_wheel.dir/core/core_input_mouse_wheel.c.o
.PHONY : core/core_input_mouse_wheel.c.o

core/core_input_mouse_wheel.i: core/core_input_mouse_wheel.c.i
.PHONY : core/core_input_mouse_wheel.i

# target to preprocess a source file
core/core_input_mouse_wheel.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_mouse_wheel.dir/build.make examples/CMakeFiles/core_input_mouse_wheel.dir/core/core_input_mouse_wheel.c.i
.PHONY : core/core_input_mouse_wheel.c.i

core/core_input_mouse_wheel.s: core/core_input_mouse_wheel.c.s
.PHONY : core/core_input_mouse_wheel.s

# target to generate assembly for a file
core/core_input_mouse_wheel.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_mouse_wheel.dir/build.make examples/CMakeFiles/core_input_mouse_wheel.dir/core/core_input_mouse_wheel.c.s
.PHONY : core/core_input_mouse_wheel.c.s

core/core_input_multitouch.o: core/core_input_multitouch.c.o
.PHONY : core/core_input_multitouch.o

# target to build an object file
core/core_input_multitouch.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_multitouch.dir/build.make examples/CMakeFiles/core_input_multitouch.dir/core/core_input_multitouch.c.o
.PHONY : core/core_input_multitouch.c.o

core/core_input_multitouch.i: core/core_input_multitouch.c.i
.PHONY : core/core_input_multitouch.i

# target to preprocess a source file
core/core_input_multitouch.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_multitouch.dir/build.make examples/CMakeFiles/core_input_multitouch.dir/core/core_input_multitouch.c.i
.PHONY : core/core_input_multitouch.c.i

core/core_input_multitouch.s: core/core_input_multitouch.c.s
.PHONY : core/core_input_multitouch.s

# target to generate assembly for a file
core/core_input_multitouch.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_input_multitouch.dir/build.make examples/CMakeFiles/core_input_multitouch.dir/core/core_input_multitouch.c.s
.PHONY : core/core_input_multitouch.c.s

core/core_loading_thread.o: core/core_loading_thread.c.o
.PHONY : core/core_loading_thread.o

# target to build an object file
core/core_loading_thread.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_loading_thread.dir/build.make examples/CMakeFiles/core_loading_thread.dir/core/core_loading_thread.c.o
.PHONY : core/core_loading_thread.c.o

core/core_loading_thread.i: core/core_loading_thread.c.i
.PHONY : core/core_loading_thread.i

# target to preprocess a source file
core/core_loading_thread.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_loading_thread.dir/build.make examples/CMakeFiles/core_loading_thread.dir/core/core_loading_thread.c.i
.PHONY : core/core_loading_thread.c.i

core/core_loading_thread.s: core/core_loading_thread.c.s
.PHONY : core/core_loading_thread.s

# target to generate assembly for a file
core/core_loading_thread.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_loading_thread.dir/build.make examples/CMakeFiles/core_loading_thread.dir/core/core_loading_thread.c.s
.PHONY : core/core_loading_thread.c.s

core/core_random_values.o: core/core_random_values.c.o
.PHONY : core/core_random_values.o

# target to build an object file
core/core_random_values.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_random_values.dir/build.make examples/CMakeFiles/core_random_values.dir/core/core_random_values.c.o
.PHONY : core/core_random_values.c.o

core/core_random_values.i: core/core_random_values.c.i
.PHONY : core/core_random_values.i

# target to preprocess a source file
core/core_random_values.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_random_values.dir/build.make examples/CMakeFiles/core_random_values.dir/core/core_random_values.c.i
.PHONY : core/core_random_values.c.i

core/core_random_values.s: core/core_random_values.c.s
.PHONY : core/core_random_values.s

# target to generate assembly for a file
core/core_random_values.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_random_values.dir/build.make examples/CMakeFiles/core_random_values.dir/core/core_random_values.c.s
.PHONY : core/core_random_values.c.s

core/core_scissor_test.o: core/core_scissor_test.c.o
.PHONY : core/core_scissor_test.o

# target to build an object file
core/core_scissor_test.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_scissor_test.dir/build.make examples/CMakeFiles/core_scissor_test.dir/core/core_scissor_test.c.o
.PHONY : core/core_scissor_test.c.o

core/core_scissor_test.i: core/core_scissor_test.c.i
.PHONY : core/core_scissor_test.i

# target to preprocess a source file
core/core_scissor_test.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_scissor_test.dir/build.make examples/CMakeFiles/core_scissor_test.dir/core/core_scissor_test.c.i
.PHONY : core/core_scissor_test.c.i

core/core_scissor_test.s: core/core_scissor_test.c.s
.PHONY : core/core_scissor_test.s

# target to generate assembly for a file
core/core_scissor_test.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_scissor_test.dir/build.make examples/CMakeFiles/core_scissor_test.dir/core/core_scissor_test.c.s
.PHONY : core/core_scissor_test.c.s

core/core_smooth_pixelperfect.o: core/core_smooth_pixelperfect.c.o
.PHONY : core/core_smooth_pixelperfect.o

# target to build an object file
core/core_smooth_pixelperfect.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_smooth_pixelperfect.dir/build.make examples/CMakeFiles/core_smooth_pixelperfect.dir/core/core_smooth_pixelperfect.c.o
.PHONY : core/core_smooth_pixelperfect.c.o

core/core_smooth_pixelperfect.i: core/core_smooth_pixelperfect.c.i
.PHONY : core/core_smooth_pixelperfect.i

# target to preprocess a source file
core/core_smooth_pixelperfect.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_smooth_pixelperfect.dir/build.make examples/CMakeFiles/core_smooth_pixelperfect.dir/core/core_smooth_pixelperfect.c.i
.PHONY : core/core_smooth_pixelperfect.c.i

core/core_smooth_pixelperfect.s: core/core_smooth_pixelperfect.c.s
.PHONY : core/core_smooth_pixelperfect.s

# target to generate assembly for a file
core/core_smooth_pixelperfect.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_smooth_pixelperfect.dir/build.make examples/CMakeFiles/core_smooth_pixelperfect.dir/core/core_smooth_pixelperfect.c.s
.PHONY : core/core_smooth_pixelperfect.c.s

core/core_split_screen.o: core/core_split_screen.c.o
.PHONY : core/core_split_screen.o

# target to build an object file
core/core_split_screen.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_split_screen.dir/build.make examples/CMakeFiles/core_split_screen.dir/core/core_split_screen.c.o
.PHONY : core/core_split_screen.c.o

core/core_split_screen.i: core/core_split_screen.c.i
.PHONY : core/core_split_screen.i

# target to preprocess a source file
core/core_split_screen.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_split_screen.dir/build.make examples/CMakeFiles/core_split_screen.dir/core/core_split_screen.c.i
.PHONY : core/core_split_screen.c.i

core/core_split_screen.s: core/core_split_screen.c.s
.PHONY : core/core_split_screen.s

# target to generate assembly for a file
core/core_split_screen.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_split_screen.dir/build.make examples/CMakeFiles/core_split_screen.dir/core/core_split_screen.c.s
.PHONY : core/core_split_screen.c.s

core/core_storage_values.o: core/core_storage_values.c.o
.PHONY : core/core_storage_values.o

# target to build an object file
core/core_storage_values.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_storage_values.dir/build.make examples/CMakeFiles/core_storage_values.dir/core/core_storage_values.c.o
.PHONY : core/core_storage_values.c.o

core/core_storage_values.i: core/core_storage_values.c.i
.PHONY : core/core_storage_values.i

# target to preprocess a source file
core/core_storage_values.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_storage_values.dir/build.make examples/CMakeFiles/core_storage_values.dir/core/core_storage_values.c.i
.PHONY : core/core_storage_values.c.i

core/core_storage_values.s: core/core_storage_values.c.s
.PHONY : core/core_storage_values.s

# target to generate assembly for a file
core/core_storage_values.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_storage_values.dir/build.make examples/CMakeFiles/core_storage_values.dir/core/core_storage_values.c.s
.PHONY : core/core_storage_values.c.s

core/core_vr_simulator.o: core/core_vr_simulator.c.o
.PHONY : core/core_vr_simulator.o

# target to build an object file
core/core_vr_simulator.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_vr_simulator.dir/build.make examples/CMakeFiles/core_vr_simulator.dir/core/core_vr_simulator.c.o
.PHONY : core/core_vr_simulator.c.o

core/core_vr_simulator.i: core/core_vr_simulator.c.i
.PHONY : core/core_vr_simulator.i

# target to preprocess a source file
core/core_vr_simulator.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_vr_simulator.dir/build.make examples/CMakeFiles/core_vr_simulator.dir/core/core_vr_simulator.c.i
.PHONY : core/core_vr_simulator.c.i

core/core_vr_simulator.s: core/core_vr_simulator.c.s
.PHONY : core/core_vr_simulator.s

# target to generate assembly for a file
core/core_vr_simulator.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_vr_simulator.dir/build.make examples/CMakeFiles/core_vr_simulator.dir/core/core_vr_simulator.c.s
.PHONY : core/core_vr_simulator.c.s

core/core_window_flags.o: core/core_window_flags.c.o
.PHONY : core/core_window_flags.o

# target to build an object file
core/core_window_flags.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_flags.dir/build.make examples/CMakeFiles/core_window_flags.dir/core/core_window_flags.c.o
.PHONY : core/core_window_flags.c.o

core/core_window_flags.i: core/core_window_flags.c.i
.PHONY : core/core_window_flags.i

# target to preprocess a source file
core/core_window_flags.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_flags.dir/build.make examples/CMakeFiles/core_window_flags.dir/core/core_window_flags.c.i
.PHONY : core/core_window_flags.c.i

core/core_window_flags.s: core/core_window_flags.c.s
.PHONY : core/core_window_flags.s

# target to generate assembly for a file
core/core_window_flags.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_flags.dir/build.make examples/CMakeFiles/core_window_flags.dir/core/core_window_flags.c.s
.PHONY : core/core_window_flags.c.s

core/core_window_letterbox.o: core/core_window_letterbox.c.o
.PHONY : core/core_window_letterbox.o

# target to build an object file
core/core_window_letterbox.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_letterbox.dir/build.make examples/CMakeFiles/core_window_letterbox.dir/core/core_window_letterbox.c.o
.PHONY : core/core_window_letterbox.c.o

core/core_window_letterbox.i: core/core_window_letterbox.c.i
.PHONY : core/core_window_letterbox.i

# target to preprocess a source file
core/core_window_letterbox.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_letterbox.dir/build.make examples/CMakeFiles/core_window_letterbox.dir/core/core_window_letterbox.c.i
.PHONY : core/core_window_letterbox.c.i

core/core_window_letterbox.s: core/core_window_letterbox.c.s
.PHONY : core/core_window_letterbox.s

# target to generate assembly for a file
core/core_window_letterbox.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_letterbox.dir/build.make examples/CMakeFiles/core_window_letterbox.dir/core/core_window_letterbox.c.s
.PHONY : core/core_window_letterbox.c.s

core/core_window_should_close.o: core/core_window_should_close.c.o
.PHONY : core/core_window_should_close.o

# target to build an object file
core/core_window_should_close.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_should_close.dir/build.make examples/CMakeFiles/core_window_should_close.dir/core/core_window_should_close.c.o
.PHONY : core/core_window_should_close.c.o

core/core_window_should_close.i: core/core_window_should_close.c.i
.PHONY : core/core_window_should_close.i

# target to preprocess a source file
core/core_window_should_close.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_should_close.dir/build.make examples/CMakeFiles/core_window_should_close.dir/core/core_window_should_close.c.i
.PHONY : core/core_window_should_close.c.i

core/core_window_should_close.s: core/core_window_should_close.c.s
.PHONY : core/core_window_should_close.s

# target to generate assembly for a file
core/core_window_should_close.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_window_should_close.dir/build.make examples/CMakeFiles/core_window_should_close.dir/core/core_window_should_close.c.s
.PHONY : core/core_window_should_close.c.s

core/core_world_screen.o: core/core_world_screen.c.o
.PHONY : core/core_world_screen.o

# target to build an object file
core/core_world_screen.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_world_screen.dir/build.make examples/CMakeFiles/core_world_screen.dir/core/core_world_screen.c.o
.PHONY : core/core_world_screen.c.o

core/core_world_screen.i: core/core_world_screen.c.i
.PHONY : core/core_world_screen.i

# target to preprocess a source file
core/core_world_screen.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_world_screen.dir/build.make examples/CMakeFiles/core_world_screen.dir/core/core_world_screen.c.i
.PHONY : core/core_world_screen.c.i

core/core_world_screen.s: core/core_world_screen.c.s
.PHONY : core/core_world_screen.s

# target to generate assembly for a file
core/core_world_screen.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/core_world_screen.dir/build.make examples/CMakeFiles/core_world_screen.dir/core/core_world_screen.c.s
.PHONY : core/core_world_screen.c.s

models/models_animation.o: models/models_animation.c.o
.PHONY : models/models_animation.o

# target to build an object file
models/models_animation.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_animation.dir/build.make examples/CMakeFiles/models_animation.dir/models/models_animation.c.o
.PHONY : models/models_animation.c.o

models/models_animation.i: models/models_animation.c.i
.PHONY : models/models_animation.i

# target to preprocess a source file
models/models_animation.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_animation.dir/build.make examples/CMakeFiles/models_animation.dir/models/models_animation.c.i
.PHONY : models/models_animation.c.i

models/models_animation.s: models/models_animation.c.s
.PHONY : models/models_animation.s

# target to generate assembly for a file
models/models_animation.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_animation.dir/build.make examples/CMakeFiles/models_animation.dir/models/models_animation.c.s
.PHONY : models/models_animation.c.s

models/models_billboard.o: models/models_billboard.c.o
.PHONY : models/models_billboard.o

# target to build an object file
models/models_billboard.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_billboard.dir/build.make examples/CMakeFiles/models_billboard.dir/models/models_billboard.c.o
.PHONY : models/models_billboard.c.o

models/models_billboard.i: models/models_billboard.c.i
.PHONY : models/models_billboard.i

# target to preprocess a source file
models/models_billboard.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_billboard.dir/build.make examples/CMakeFiles/models_billboard.dir/models/models_billboard.c.i
.PHONY : models/models_billboard.c.i

models/models_billboard.s: models/models_billboard.c.s
.PHONY : models/models_billboard.s

# target to generate assembly for a file
models/models_billboard.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_billboard.dir/build.make examples/CMakeFiles/models_billboard.dir/models/models_billboard.c.s
.PHONY : models/models_billboard.c.s

models/models_box_collisions.o: models/models_box_collisions.c.o
.PHONY : models/models_box_collisions.o

# target to build an object file
models/models_box_collisions.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_box_collisions.dir/build.make examples/CMakeFiles/models_box_collisions.dir/models/models_box_collisions.c.o
.PHONY : models/models_box_collisions.c.o

models/models_box_collisions.i: models/models_box_collisions.c.i
.PHONY : models/models_box_collisions.i

# target to preprocess a source file
models/models_box_collisions.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_box_collisions.dir/build.make examples/CMakeFiles/models_box_collisions.dir/models/models_box_collisions.c.i
.PHONY : models/models_box_collisions.c.i

models/models_box_collisions.s: models/models_box_collisions.c.s
.PHONY : models/models_box_collisions.s

# target to generate assembly for a file
models/models_box_collisions.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_box_collisions.dir/build.make examples/CMakeFiles/models_box_collisions.dir/models/models_box_collisions.c.s
.PHONY : models/models_box_collisions.c.s

models/models_cubicmap.o: models/models_cubicmap.c.o
.PHONY : models/models_cubicmap.o

# target to build an object file
models/models_cubicmap.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_cubicmap.dir/build.make examples/CMakeFiles/models_cubicmap.dir/models/models_cubicmap.c.o
.PHONY : models/models_cubicmap.c.o

models/models_cubicmap.i: models/models_cubicmap.c.i
.PHONY : models/models_cubicmap.i

# target to preprocess a source file
models/models_cubicmap.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_cubicmap.dir/build.make examples/CMakeFiles/models_cubicmap.dir/models/models_cubicmap.c.i
.PHONY : models/models_cubicmap.c.i

models/models_cubicmap.s: models/models_cubicmap.c.s
.PHONY : models/models_cubicmap.s

# target to generate assembly for a file
models/models_cubicmap.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_cubicmap.dir/build.make examples/CMakeFiles/models_cubicmap.dir/models/models_cubicmap.c.s
.PHONY : models/models_cubicmap.c.s

models/models_draw_cube_texture.o: models/models_draw_cube_texture.c.o
.PHONY : models/models_draw_cube_texture.o

# target to build an object file
models/models_draw_cube_texture.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_draw_cube_texture.dir/build.make examples/CMakeFiles/models_draw_cube_texture.dir/models/models_draw_cube_texture.c.o
.PHONY : models/models_draw_cube_texture.c.o

models/models_draw_cube_texture.i: models/models_draw_cube_texture.c.i
.PHONY : models/models_draw_cube_texture.i

# target to preprocess a source file
models/models_draw_cube_texture.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_draw_cube_texture.dir/build.make examples/CMakeFiles/models_draw_cube_texture.dir/models/models_draw_cube_texture.c.i
.PHONY : models/models_draw_cube_texture.c.i

models/models_draw_cube_texture.s: models/models_draw_cube_texture.c.s
.PHONY : models/models_draw_cube_texture.s

# target to generate assembly for a file
models/models_draw_cube_texture.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_draw_cube_texture.dir/build.make examples/CMakeFiles/models_draw_cube_texture.dir/models/models_draw_cube_texture.c.s
.PHONY : models/models_draw_cube_texture.c.s

models/models_first_person_maze.o: models/models_first_person_maze.c.o
.PHONY : models/models_first_person_maze.o

# target to build an object file
models/models_first_person_maze.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_first_person_maze.dir/build.make examples/CMakeFiles/models_first_person_maze.dir/models/models_first_person_maze.c.o
.PHONY : models/models_first_person_maze.c.o

models/models_first_person_maze.i: models/models_first_person_maze.c.i
.PHONY : models/models_first_person_maze.i

# target to preprocess a source file
models/models_first_person_maze.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_first_person_maze.dir/build.make examples/CMakeFiles/models_first_person_maze.dir/models/models_first_person_maze.c.i
.PHONY : models/models_first_person_maze.c.i

models/models_first_person_maze.s: models/models_first_person_maze.c.s
.PHONY : models/models_first_person_maze.s

# target to generate assembly for a file
models/models_first_person_maze.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_first_person_maze.dir/build.make examples/CMakeFiles/models_first_person_maze.dir/models/models_first_person_maze.c.s
.PHONY : models/models_first_person_maze.c.s

models/models_geometric_shapes.o: models/models_geometric_shapes.c.o
.PHONY : models/models_geometric_shapes.o

# target to build an object file
models/models_geometric_shapes.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_geometric_shapes.dir/build.make examples/CMakeFiles/models_geometric_shapes.dir/models/models_geometric_shapes.c.o
.PHONY : models/models_geometric_shapes.c.o

models/models_geometric_shapes.i: models/models_geometric_shapes.c.i
.PHONY : models/models_geometric_shapes.i

# target to preprocess a source file
models/models_geometric_shapes.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_geometric_shapes.dir/build.make examples/CMakeFiles/models_geometric_shapes.dir/models/models_geometric_shapes.c.i
.PHONY : models/models_geometric_shapes.c.i

models/models_geometric_shapes.s: models/models_geometric_shapes.c.s
.PHONY : models/models_geometric_shapes.s

# target to generate assembly for a file
models/models_geometric_shapes.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_geometric_shapes.dir/build.make examples/CMakeFiles/models_geometric_shapes.dir/models/models_geometric_shapes.c.s
.PHONY : models/models_geometric_shapes.c.s

models/models_heightmap.o: models/models_heightmap.c.o
.PHONY : models/models_heightmap.o

# target to build an object file
models/models_heightmap.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_heightmap.dir/build.make examples/CMakeFiles/models_heightmap.dir/models/models_heightmap.c.o
.PHONY : models/models_heightmap.c.o

models/models_heightmap.i: models/models_heightmap.c.i
.PHONY : models/models_heightmap.i

# target to preprocess a source file
models/models_heightmap.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_heightmap.dir/build.make examples/CMakeFiles/models_heightmap.dir/models/models_heightmap.c.i
.PHONY : models/models_heightmap.c.i

models/models_heightmap.s: models/models_heightmap.c.s
.PHONY : models/models_heightmap.s

# target to generate assembly for a file
models/models_heightmap.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_heightmap.dir/build.make examples/CMakeFiles/models_heightmap.dir/models/models_heightmap.c.s
.PHONY : models/models_heightmap.c.s

models/models_loading.o: models/models_loading.c.o
.PHONY : models/models_loading.o

# target to build an object file
models/models_loading.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading.dir/build.make examples/CMakeFiles/models_loading.dir/models/models_loading.c.o
.PHONY : models/models_loading.c.o

models/models_loading.i: models/models_loading.c.i
.PHONY : models/models_loading.i

# target to preprocess a source file
models/models_loading.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading.dir/build.make examples/CMakeFiles/models_loading.dir/models/models_loading.c.i
.PHONY : models/models_loading.c.i

models/models_loading.s: models/models_loading.c.s
.PHONY : models/models_loading.s

# target to generate assembly for a file
models/models_loading.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading.dir/build.make examples/CMakeFiles/models_loading.dir/models/models_loading.c.s
.PHONY : models/models_loading.c.s

models/models_loading_gltf.o: models/models_loading_gltf.c.o
.PHONY : models/models_loading_gltf.o

# target to build an object file
models/models_loading_gltf.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_gltf.dir/build.make examples/CMakeFiles/models_loading_gltf.dir/models/models_loading_gltf.c.o
.PHONY : models/models_loading_gltf.c.o

models/models_loading_gltf.i: models/models_loading_gltf.c.i
.PHONY : models/models_loading_gltf.i

# target to preprocess a source file
models/models_loading_gltf.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_gltf.dir/build.make examples/CMakeFiles/models_loading_gltf.dir/models/models_loading_gltf.c.i
.PHONY : models/models_loading_gltf.c.i

models/models_loading_gltf.s: models/models_loading_gltf.c.s
.PHONY : models/models_loading_gltf.s

# target to generate assembly for a file
models/models_loading_gltf.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_gltf.dir/build.make examples/CMakeFiles/models_loading_gltf.dir/models/models_loading_gltf.c.s
.PHONY : models/models_loading_gltf.c.s

models/models_loading_m3d.o: models/models_loading_m3d.c.o
.PHONY : models/models_loading_m3d.o

# target to build an object file
models/models_loading_m3d.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_m3d.dir/build.make examples/CMakeFiles/models_loading_m3d.dir/models/models_loading_m3d.c.o
.PHONY : models/models_loading_m3d.c.o

models/models_loading_m3d.i: models/models_loading_m3d.c.i
.PHONY : models/models_loading_m3d.i

# target to preprocess a source file
models/models_loading_m3d.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_m3d.dir/build.make examples/CMakeFiles/models_loading_m3d.dir/models/models_loading_m3d.c.i
.PHONY : models/models_loading_m3d.c.i

models/models_loading_m3d.s: models/models_loading_m3d.c.s
.PHONY : models/models_loading_m3d.s

# target to generate assembly for a file
models/models_loading_m3d.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_m3d.dir/build.make examples/CMakeFiles/models_loading_m3d.dir/models/models_loading_m3d.c.s
.PHONY : models/models_loading_m3d.c.s

models/models_loading_vox.o: models/models_loading_vox.c.o
.PHONY : models/models_loading_vox.o

# target to build an object file
models/models_loading_vox.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_vox.dir/build.make examples/CMakeFiles/models_loading_vox.dir/models/models_loading_vox.c.o
.PHONY : models/models_loading_vox.c.o

models/models_loading_vox.i: models/models_loading_vox.c.i
.PHONY : models/models_loading_vox.i

# target to preprocess a source file
models/models_loading_vox.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_vox.dir/build.make examples/CMakeFiles/models_loading_vox.dir/models/models_loading_vox.c.i
.PHONY : models/models_loading_vox.c.i

models/models_loading_vox.s: models/models_loading_vox.c.s
.PHONY : models/models_loading_vox.s

# target to generate assembly for a file
models/models_loading_vox.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_loading_vox.dir/build.make examples/CMakeFiles/models_loading_vox.dir/models/models_loading_vox.c.s
.PHONY : models/models_loading_vox.c.s

models/models_mesh_generation.o: models/models_mesh_generation.c.o
.PHONY : models/models_mesh_generation.o

# target to build an object file
models/models_mesh_generation.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_mesh_generation.dir/build.make examples/CMakeFiles/models_mesh_generation.dir/models/models_mesh_generation.c.o
.PHONY : models/models_mesh_generation.c.o

models/models_mesh_generation.i: models/models_mesh_generation.c.i
.PHONY : models/models_mesh_generation.i

# target to preprocess a source file
models/models_mesh_generation.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_mesh_generation.dir/build.make examples/CMakeFiles/models_mesh_generation.dir/models/models_mesh_generation.c.i
.PHONY : models/models_mesh_generation.c.i

models/models_mesh_generation.s: models/models_mesh_generation.c.s
.PHONY : models/models_mesh_generation.s

# target to generate assembly for a file
models/models_mesh_generation.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_mesh_generation.dir/build.make examples/CMakeFiles/models_mesh_generation.dir/models/models_mesh_generation.c.s
.PHONY : models/models_mesh_generation.c.s

models/models_mesh_picking.o: models/models_mesh_picking.c.o
.PHONY : models/models_mesh_picking.o

# target to build an object file
models/models_mesh_picking.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_mesh_picking.dir/build.make examples/CMakeFiles/models_mesh_picking.dir/models/models_mesh_picking.c.o
.PHONY : models/models_mesh_picking.c.o

models/models_mesh_picking.i: models/models_mesh_picking.c.i
.PHONY : models/models_mesh_picking.i

# target to preprocess a source file
models/models_mesh_picking.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_mesh_picking.dir/build.make examples/CMakeFiles/models_mesh_picking.dir/models/models_mesh_picking.c.i
.PHONY : models/models_mesh_picking.c.i

models/models_mesh_picking.s: models/models_mesh_picking.c.s
.PHONY : models/models_mesh_picking.s

# target to generate assembly for a file
models/models_mesh_picking.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_mesh_picking.dir/build.make examples/CMakeFiles/models_mesh_picking.dir/models/models_mesh_picking.c.s
.PHONY : models/models_mesh_picking.c.s

models/models_orthographic_projection.o: models/models_orthographic_projection.c.o
.PHONY : models/models_orthographic_projection.o

# target to build an object file
models/models_orthographic_projection.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_orthographic_projection.dir/build.make examples/CMakeFiles/models_orthographic_projection.dir/models/models_orthographic_projection.c.o
.PHONY : models/models_orthographic_projection.c.o

models/models_orthographic_projection.i: models/models_orthographic_projection.c.i
.PHONY : models/models_orthographic_projection.i

# target to preprocess a source file
models/models_orthographic_projection.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_orthographic_projection.dir/build.make examples/CMakeFiles/models_orthographic_projection.dir/models/models_orthographic_projection.c.i
.PHONY : models/models_orthographic_projection.c.i

models/models_orthographic_projection.s: models/models_orthographic_projection.c.s
.PHONY : models/models_orthographic_projection.s

# target to generate assembly for a file
models/models_orthographic_projection.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_orthographic_projection.dir/build.make examples/CMakeFiles/models_orthographic_projection.dir/models/models_orthographic_projection.c.s
.PHONY : models/models_orthographic_projection.c.s

models/models_rlgl_solar_system.o: models/models_rlgl_solar_system.c.o
.PHONY : models/models_rlgl_solar_system.o

# target to build an object file
models/models_rlgl_solar_system.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_rlgl_solar_system.dir/build.make examples/CMakeFiles/models_rlgl_solar_system.dir/models/models_rlgl_solar_system.c.o
.PHONY : models/models_rlgl_solar_system.c.o

models/models_rlgl_solar_system.i: models/models_rlgl_solar_system.c.i
.PHONY : models/models_rlgl_solar_system.i

# target to preprocess a source file
models/models_rlgl_solar_system.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_rlgl_solar_system.dir/build.make examples/CMakeFiles/models_rlgl_solar_system.dir/models/models_rlgl_solar_system.c.i
.PHONY : models/models_rlgl_solar_system.c.i

models/models_rlgl_solar_system.s: models/models_rlgl_solar_system.c.s
.PHONY : models/models_rlgl_solar_system.s

# target to generate assembly for a file
models/models_rlgl_solar_system.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_rlgl_solar_system.dir/build.make examples/CMakeFiles/models_rlgl_solar_system.dir/models/models_rlgl_solar_system.c.s
.PHONY : models/models_rlgl_solar_system.c.s

models/models_skybox.o: models/models_skybox.c.o
.PHONY : models/models_skybox.o

# target to build an object file
models/models_skybox.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_skybox.dir/build.make examples/CMakeFiles/models_skybox.dir/models/models_skybox.c.o
.PHONY : models/models_skybox.c.o

models/models_skybox.i: models/models_skybox.c.i
.PHONY : models/models_skybox.i

# target to preprocess a source file
models/models_skybox.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_skybox.dir/build.make examples/CMakeFiles/models_skybox.dir/models/models_skybox.c.i
.PHONY : models/models_skybox.c.i

models/models_skybox.s: models/models_skybox.c.s
.PHONY : models/models_skybox.s

# target to generate assembly for a file
models/models_skybox.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_skybox.dir/build.make examples/CMakeFiles/models_skybox.dir/models/models_skybox.c.s
.PHONY : models/models_skybox.c.s

models/models_waving_cubes.o: models/models_waving_cubes.c.o
.PHONY : models/models_waving_cubes.o

# target to build an object file
models/models_waving_cubes.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_waving_cubes.dir/build.make examples/CMakeFiles/models_waving_cubes.dir/models/models_waving_cubes.c.o
.PHONY : models/models_waving_cubes.c.o

models/models_waving_cubes.i: models/models_waving_cubes.c.i
.PHONY : models/models_waving_cubes.i

# target to preprocess a source file
models/models_waving_cubes.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_waving_cubes.dir/build.make examples/CMakeFiles/models_waving_cubes.dir/models/models_waving_cubes.c.i
.PHONY : models/models_waving_cubes.c.i

models/models_waving_cubes.s: models/models_waving_cubes.c.s
.PHONY : models/models_waving_cubes.s

# target to generate assembly for a file
models/models_waving_cubes.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_waving_cubes.dir/build.make examples/CMakeFiles/models_waving_cubes.dir/models/models_waving_cubes.c.s
.PHONY : models/models_waving_cubes.c.s

models/models_yaw_pitch_roll.o: models/models_yaw_pitch_roll.c.o
.PHONY : models/models_yaw_pitch_roll.o

# target to build an object file
models/models_yaw_pitch_roll.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_yaw_pitch_roll.dir/build.make examples/CMakeFiles/models_yaw_pitch_roll.dir/models/models_yaw_pitch_roll.c.o
.PHONY : models/models_yaw_pitch_roll.c.o

models/models_yaw_pitch_roll.i: models/models_yaw_pitch_roll.c.i
.PHONY : models/models_yaw_pitch_roll.i

# target to preprocess a source file
models/models_yaw_pitch_roll.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_yaw_pitch_roll.dir/build.make examples/CMakeFiles/models_yaw_pitch_roll.dir/models/models_yaw_pitch_roll.c.i
.PHONY : models/models_yaw_pitch_roll.c.i

models/models_yaw_pitch_roll.s: models/models_yaw_pitch_roll.c.s
.PHONY : models/models_yaw_pitch_roll.s

# target to generate assembly for a file
models/models_yaw_pitch_roll.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/models_yaw_pitch_roll.dir/build.make examples/CMakeFiles/models_yaw_pitch_roll.dir/models/models_yaw_pitch_roll.c.s
.PHONY : models/models_yaw_pitch_roll.c.s

others/easings_testbed.o: others/easings_testbed.c.o
.PHONY : others/easings_testbed.o

# target to build an object file
others/easings_testbed.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/easings_testbed.dir/build.make examples/CMakeFiles/easings_testbed.dir/others/easings_testbed.c.o
.PHONY : others/easings_testbed.c.o

others/easings_testbed.i: others/easings_testbed.c.i
.PHONY : others/easings_testbed.i

# target to preprocess a source file
others/easings_testbed.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/easings_testbed.dir/build.make examples/CMakeFiles/easings_testbed.dir/others/easings_testbed.c.i
.PHONY : others/easings_testbed.c.i

others/easings_testbed.s: others/easings_testbed.c.s
.PHONY : others/easings_testbed.s

# target to generate assembly for a file
others/easings_testbed.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/easings_testbed.dir/build.make examples/CMakeFiles/easings_testbed.dir/others/easings_testbed.c.s
.PHONY : others/easings_testbed.c.s

others/embedded_files_loading.o: others/embedded_files_loading.c.o
.PHONY : others/embedded_files_loading.o

# target to build an object file
others/embedded_files_loading.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/embedded_files_loading.dir/build.make examples/CMakeFiles/embedded_files_loading.dir/others/embedded_files_loading.c.o
.PHONY : others/embedded_files_loading.c.o

others/embedded_files_loading.i: others/embedded_files_loading.c.i
.PHONY : others/embedded_files_loading.i

# target to preprocess a source file
others/embedded_files_loading.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/embedded_files_loading.dir/build.make examples/CMakeFiles/embedded_files_loading.dir/others/embedded_files_loading.c.i
.PHONY : others/embedded_files_loading.c.i

others/embedded_files_loading.s: others/embedded_files_loading.c.s
.PHONY : others/embedded_files_loading.s

# target to generate assembly for a file
others/embedded_files_loading.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/embedded_files_loading.dir/build.make examples/CMakeFiles/embedded_files_loading.dir/others/embedded_files_loading.c.s
.PHONY : others/embedded_files_loading.c.s

others/raylib_opengl_interop.o: others/raylib_opengl_interop.c.o
.PHONY : others/raylib_opengl_interop.o

# target to build an object file
others/raylib_opengl_interop.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/raylib_opengl_interop.dir/build.make examples/CMakeFiles/raylib_opengl_interop.dir/others/raylib_opengl_interop.c.o
.PHONY : others/raylib_opengl_interop.c.o

others/raylib_opengl_interop.i: others/raylib_opengl_interop.c.i
.PHONY : others/raylib_opengl_interop.i

# target to preprocess a source file
others/raylib_opengl_interop.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/raylib_opengl_interop.dir/build.make examples/CMakeFiles/raylib_opengl_interop.dir/others/raylib_opengl_interop.c.i
.PHONY : others/raylib_opengl_interop.c.i

others/raylib_opengl_interop.s: others/raylib_opengl_interop.c.s
.PHONY : others/raylib_opengl_interop.s

# target to generate assembly for a file
others/raylib_opengl_interop.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/raylib_opengl_interop.dir/build.make examples/CMakeFiles/raylib_opengl_interop.dir/others/raylib_opengl_interop.c.s
.PHONY : others/raylib_opengl_interop.c.s

others/rlgl_compute_shader.o: others/rlgl_compute_shader.c.o
.PHONY : others/rlgl_compute_shader.o

# target to build an object file
others/rlgl_compute_shader.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/rlgl_compute_shader.dir/build.make examples/CMakeFiles/rlgl_compute_shader.dir/others/rlgl_compute_shader.c.o
.PHONY : others/rlgl_compute_shader.c.o

others/rlgl_compute_shader.i: others/rlgl_compute_shader.c.i
.PHONY : others/rlgl_compute_shader.i

# target to preprocess a source file
others/rlgl_compute_shader.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/rlgl_compute_shader.dir/build.make examples/CMakeFiles/rlgl_compute_shader.dir/others/rlgl_compute_shader.c.i
.PHONY : others/rlgl_compute_shader.c.i

others/rlgl_compute_shader.s: others/rlgl_compute_shader.c.s
.PHONY : others/rlgl_compute_shader.s

# target to generate assembly for a file
others/rlgl_compute_shader.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/rlgl_compute_shader.dir/build.make examples/CMakeFiles/rlgl_compute_shader.dir/others/rlgl_compute_shader.c.s
.PHONY : others/rlgl_compute_shader.c.s

others/rlgl_standalone.o: others/rlgl_standalone.c.o
.PHONY : others/rlgl_standalone.o

# target to build an object file
others/rlgl_standalone.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/rlgl_standalone.dir/build.make examples/CMakeFiles/rlgl_standalone.dir/others/rlgl_standalone.c.o
.PHONY : others/rlgl_standalone.c.o

others/rlgl_standalone.i: others/rlgl_standalone.c.i
.PHONY : others/rlgl_standalone.i

# target to preprocess a source file
others/rlgl_standalone.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/rlgl_standalone.dir/build.make examples/CMakeFiles/rlgl_standalone.dir/others/rlgl_standalone.c.i
.PHONY : others/rlgl_standalone.c.i

others/rlgl_standalone.s: others/rlgl_standalone.c.s
.PHONY : others/rlgl_standalone.s

# target to generate assembly for a file
others/rlgl_standalone.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/rlgl_standalone.dir/build.make examples/CMakeFiles/rlgl_standalone.dir/others/rlgl_standalone.c.s
.PHONY : others/rlgl_standalone.c.s

shaders/shaders_basic_lighting.o: shaders/shaders_basic_lighting.c.o
.PHONY : shaders/shaders_basic_lighting.o

# target to build an object file
shaders/shaders_basic_lighting.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_basic_lighting.dir/build.make examples/CMakeFiles/shaders_basic_lighting.dir/shaders/shaders_basic_lighting.c.o
.PHONY : shaders/shaders_basic_lighting.c.o

shaders/shaders_basic_lighting.i: shaders/shaders_basic_lighting.c.i
.PHONY : shaders/shaders_basic_lighting.i

# target to preprocess a source file
shaders/shaders_basic_lighting.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_basic_lighting.dir/build.make examples/CMakeFiles/shaders_basic_lighting.dir/shaders/shaders_basic_lighting.c.i
.PHONY : shaders/shaders_basic_lighting.c.i

shaders/shaders_basic_lighting.s: shaders/shaders_basic_lighting.c.s
.PHONY : shaders/shaders_basic_lighting.s

# target to generate assembly for a file
shaders/shaders_basic_lighting.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_basic_lighting.dir/build.make examples/CMakeFiles/shaders_basic_lighting.dir/shaders/shaders_basic_lighting.c.s
.PHONY : shaders/shaders_basic_lighting.c.s

shaders/shaders_custom_uniform.o: shaders/shaders_custom_uniform.c.o
.PHONY : shaders/shaders_custom_uniform.o

# target to build an object file
shaders/shaders_custom_uniform.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_custom_uniform.dir/build.make examples/CMakeFiles/shaders_custom_uniform.dir/shaders/shaders_custom_uniform.c.o
.PHONY : shaders/shaders_custom_uniform.c.o

shaders/shaders_custom_uniform.i: shaders/shaders_custom_uniform.c.i
.PHONY : shaders/shaders_custom_uniform.i

# target to preprocess a source file
shaders/shaders_custom_uniform.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_custom_uniform.dir/build.make examples/CMakeFiles/shaders_custom_uniform.dir/shaders/shaders_custom_uniform.c.i
.PHONY : shaders/shaders_custom_uniform.c.i

shaders/shaders_custom_uniform.s: shaders/shaders_custom_uniform.c.s
.PHONY : shaders/shaders_custom_uniform.s

# target to generate assembly for a file
shaders/shaders_custom_uniform.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_custom_uniform.dir/build.make examples/CMakeFiles/shaders_custom_uniform.dir/shaders/shaders_custom_uniform.c.s
.PHONY : shaders/shaders_custom_uniform.c.s

shaders/shaders_eratosthenes.o: shaders/shaders_eratosthenes.c.o
.PHONY : shaders/shaders_eratosthenes.o

# target to build an object file
shaders/shaders_eratosthenes.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_eratosthenes.dir/build.make examples/CMakeFiles/shaders_eratosthenes.dir/shaders/shaders_eratosthenes.c.o
.PHONY : shaders/shaders_eratosthenes.c.o

shaders/shaders_eratosthenes.i: shaders/shaders_eratosthenes.c.i
.PHONY : shaders/shaders_eratosthenes.i

# target to preprocess a source file
shaders/shaders_eratosthenes.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_eratosthenes.dir/build.make examples/CMakeFiles/shaders_eratosthenes.dir/shaders/shaders_eratosthenes.c.i
.PHONY : shaders/shaders_eratosthenes.c.i

shaders/shaders_eratosthenes.s: shaders/shaders_eratosthenes.c.s
.PHONY : shaders/shaders_eratosthenes.s

# target to generate assembly for a file
shaders/shaders_eratosthenes.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_eratosthenes.dir/build.make examples/CMakeFiles/shaders_eratosthenes.dir/shaders/shaders_eratosthenes.c.s
.PHONY : shaders/shaders_eratosthenes.c.s

shaders/shaders_fog.o: shaders/shaders_fog.c.o
.PHONY : shaders/shaders_fog.o

# target to build an object file
shaders/shaders_fog.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_fog.dir/build.make examples/CMakeFiles/shaders_fog.dir/shaders/shaders_fog.c.o
.PHONY : shaders/shaders_fog.c.o

shaders/shaders_fog.i: shaders/shaders_fog.c.i
.PHONY : shaders/shaders_fog.i

# target to preprocess a source file
shaders/shaders_fog.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_fog.dir/build.make examples/CMakeFiles/shaders_fog.dir/shaders/shaders_fog.c.i
.PHONY : shaders/shaders_fog.c.i

shaders/shaders_fog.s: shaders/shaders_fog.c.s
.PHONY : shaders/shaders_fog.s

# target to generate assembly for a file
shaders/shaders_fog.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_fog.dir/build.make examples/CMakeFiles/shaders_fog.dir/shaders/shaders_fog.c.s
.PHONY : shaders/shaders_fog.c.s

shaders/shaders_hot_reloading.o: shaders/shaders_hot_reloading.c.o
.PHONY : shaders/shaders_hot_reloading.o

# target to build an object file
shaders/shaders_hot_reloading.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_hot_reloading.dir/build.make examples/CMakeFiles/shaders_hot_reloading.dir/shaders/shaders_hot_reloading.c.o
.PHONY : shaders/shaders_hot_reloading.c.o

shaders/shaders_hot_reloading.i: shaders/shaders_hot_reloading.c.i
.PHONY : shaders/shaders_hot_reloading.i

# target to preprocess a source file
shaders/shaders_hot_reloading.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_hot_reloading.dir/build.make examples/CMakeFiles/shaders_hot_reloading.dir/shaders/shaders_hot_reloading.c.i
.PHONY : shaders/shaders_hot_reloading.c.i

shaders/shaders_hot_reloading.s: shaders/shaders_hot_reloading.c.s
.PHONY : shaders/shaders_hot_reloading.s

# target to generate assembly for a file
shaders/shaders_hot_reloading.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_hot_reloading.dir/build.make examples/CMakeFiles/shaders_hot_reloading.dir/shaders/shaders_hot_reloading.c.s
.PHONY : shaders/shaders_hot_reloading.c.s

shaders/shaders_hybrid_render.o: shaders/shaders_hybrid_render.c.o
.PHONY : shaders/shaders_hybrid_render.o

# target to build an object file
shaders/shaders_hybrid_render.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_hybrid_render.dir/build.make examples/CMakeFiles/shaders_hybrid_render.dir/shaders/shaders_hybrid_render.c.o
.PHONY : shaders/shaders_hybrid_render.c.o

shaders/shaders_hybrid_render.i: shaders/shaders_hybrid_render.c.i
.PHONY : shaders/shaders_hybrid_render.i

# target to preprocess a source file
shaders/shaders_hybrid_render.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_hybrid_render.dir/build.make examples/CMakeFiles/shaders_hybrid_render.dir/shaders/shaders_hybrid_render.c.i
.PHONY : shaders/shaders_hybrid_render.c.i

shaders/shaders_hybrid_render.s: shaders/shaders_hybrid_render.c.s
.PHONY : shaders/shaders_hybrid_render.s

# target to generate assembly for a file
shaders/shaders_hybrid_render.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_hybrid_render.dir/build.make examples/CMakeFiles/shaders_hybrid_render.dir/shaders/shaders_hybrid_render.c.s
.PHONY : shaders/shaders_hybrid_render.c.s

shaders/shaders_julia_set.o: shaders/shaders_julia_set.c.o
.PHONY : shaders/shaders_julia_set.o

# target to build an object file
shaders/shaders_julia_set.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_julia_set.dir/build.make examples/CMakeFiles/shaders_julia_set.dir/shaders/shaders_julia_set.c.o
.PHONY : shaders/shaders_julia_set.c.o

shaders/shaders_julia_set.i: shaders/shaders_julia_set.c.i
.PHONY : shaders/shaders_julia_set.i

# target to preprocess a source file
shaders/shaders_julia_set.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_julia_set.dir/build.make examples/CMakeFiles/shaders_julia_set.dir/shaders/shaders_julia_set.c.i
.PHONY : shaders/shaders_julia_set.c.i

shaders/shaders_julia_set.s: shaders/shaders_julia_set.c.s
.PHONY : shaders/shaders_julia_set.s

# target to generate assembly for a file
shaders/shaders_julia_set.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_julia_set.dir/build.make examples/CMakeFiles/shaders_julia_set.dir/shaders/shaders_julia_set.c.s
.PHONY : shaders/shaders_julia_set.c.s

shaders/shaders_mesh_instancing.o: shaders/shaders_mesh_instancing.c.o
.PHONY : shaders/shaders_mesh_instancing.o

# target to build an object file
shaders/shaders_mesh_instancing.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_mesh_instancing.dir/build.make examples/CMakeFiles/shaders_mesh_instancing.dir/shaders/shaders_mesh_instancing.c.o
.PHONY : shaders/shaders_mesh_instancing.c.o

shaders/shaders_mesh_instancing.i: shaders/shaders_mesh_instancing.c.i
.PHONY : shaders/shaders_mesh_instancing.i

# target to preprocess a source file
shaders/shaders_mesh_instancing.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_mesh_instancing.dir/build.make examples/CMakeFiles/shaders_mesh_instancing.dir/shaders/shaders_mesh_instancing.c.i
.PHONY : shaders/shaders_mesh_instancing.c.i

shaders/shaders_mesh_instancing.s: shaders/shaders_mesh_instancing.c.s
.PHONY : shaders/shaders_mesh_instancing.s

# target to generate assembly for a file
shaders/shaders_mesh_instancing.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_mesh_instancing.dir/build.make examples/CMakeFiles/shaders_mesh_instancing.dir/shaders/shaders_mesh_instancing.c.s
.PHONY : shaders/shaders_mesh_instancing.c.s

shaders/shaders_model_shader.o: shaders/shaders_model_shader.c.o
.PHONY : shaders/shaders_model_shader.o

# target to build an object file
shaders/shaders_model_shader.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_model_shader.dir/build.make examples/CMakeFiles/shaders_model_shader.dir/shaders/shaders_model_shader.c.o
.PHONY : shaders/shaders_model_shader.c.o

shaders/shaders_model_shader.i: shaders/shaders_model_shader.c.i
.PHONY : shaders/shaders_model_shader.i

# target to preprocess a source file
shaders/shaders_model_shader.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_model_shader.dir/build.make examples/CMakeFiles/shaders_model_shader.dir/shaders/shaders_model_shader.c.i
.PHONY : shaders/shaders_model_shader.c.i

shaders/shaders_model_shader.s: shaders/shaders_model_shader.c.s
.PHONY : shaders/shaders_model_shader.s

# target to generate assembly for a file
shaders/shaders_model_shader.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_model_shader.dir/build.make examples/CMakeFiles/shaders_model_shader.dir/shaders/shaders_model_shader.c.s
.PHONY : shaders/shaders_model_shader.c.s

shaders/shaders_multi_sample2d.o: shaders/shaders_multi_sample2d.c.o
.PHONY : shaders/shaders_multi_sample2d.o

# target to build an object file
shaders/shaders_multi_sample2d.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_multi_sample2d.dir/build.make examples/CMakeFiles/shaders_multi_sample2d.dir/shaders/shaders_multi_sample2d.c.o
.PHONY : shaders/shaders_multi_sample2d.c.o

shaders/shaders_multi_sample2d.i: shaders/shaders_multi_sample2d.c.i
.PHONY : shaders/shaders_multi_sample2d.i

# target to preprocess a source file
shaders/shaders_multi_sample2d.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_multi_sample2d.dir/build.make examples/CMakeFiles/shaders_multi_sample2d.dir/shaders/shaders_multi_sample2d.c.i
.PHONY : shaders/shaders_multi_sample2d.c.i

shaders/shaders_multi_sample2d.s: shaders/shaders_multi_sample2d.c.s
.PHONY : shaders/shaders_multi_sample2d.s

# target to generate assembly for a file
shaders/shaders_multi_sample2d.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_multi_sample2d.dir/build.make examples/CMakeFiles/shaders_multi_sample2d.dir/shaders/shaders_multi_sample2d.c.s
.PHONY : shaders/shaders_multi_sample2d.c.s

shaders/shaders_palette_switch.o: shaders/shaders_palette_switch.c.o
.PHONY : shaders/shaders_palette_switch.o

# target to build an object file
shaders/shaders_palette_switch.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_palette_switch.dir/build.make examples/CMakeFiles/shaders_palette_switch.dir/shaders/shaders_palette_switch.c.o
.PHONY : shaders/shaders_palette_switch.c.o

shaders/shaders_palette_switch.i: shaders/shaders_palette_switch.c.i
.PHONY : shaders/shaders_palette_switch.i

# target to preprocess a source file
shaders/shaders_palette_switch.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_palette_switch.dir/build.make examples/CMakeFiles/shaders_palette_switch.dir/shaders/shaders_palette_switch.c.i
.PHONY : shaders/shaders_palette_switch.c.i

shaders/shaders_palette_switch.s: shaders/shaders_palette_switch.c.s
.PHONY : shaders/shaders_palette_switch.s

# target to generate assembly for a file
shaders/shaders_palette_switch.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_palette_switch.dir/build.make examples/CMakeFiles/shaders_palette_switch.dir/shaders/shaders_palette_switch.c.s
.PHONY : shaders/shaders_palette_switch.c.s

shaders/shaders_postprocessing.o: shaders/shaders_postprocessing.c.o
.PHONY : shaders/shaders_postprocessing.o

# target to build an object file
shaders/shaders_postprocessing.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_postprocessing.dir/build.make examples/CMakeFiles/shaders_postprocessing.dir/shaders/shaders_postprocessing.c.o
.PHONY : shaders/shaders_postprocessing.c.o

shaders/shaders_postprocessing.i: shaders/shaders_postprocessing.c.i
.PHONY : shaders/shaders_postprocessing.i

# target to preprocess a source file
shaders/shaders_postprocessing.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_postprocessing.dir/build.make examples/CMakeFiles/shaders_postprocessing.dir/shaders/shaders_postprocessing.c.i
.PHONY : shaders/shaders_postprocessing.c.i

shaders/shaders_postprocessing.s: shaders/shaders_postprocessing.c.s
.PHONY : shaders/shaders_postprocessing.s

# target to generate assembly for a file
shaders/shaders_postprocessing.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_postprocessing.dir/build.make examples/CMakeFiles/shaders_postprocessing.dir/shaders/shaders_postprocessing.c.s
.PHONY : shaders/shaders_postprocessing.c.s

shaders/shaders_raymarching.o: shaders/shaders_raymarching.c.o
.PHONY : shaders/shaders_raymarching.o

# target to build an object file
shaders/shaders_raymarching.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_raymarching.dir/build.make examples/CMakeFiles/shaders_raymarching.dir/shaders/shaders_raymarching.c.o
.PHONY : shaders/shaders_raymarching.c.o

shaders/shaders_raymarching.i: shaders/shaders_raymarching.c.i
.PHONY : shaders/shaders_raymarching.i

# target to preprocess a source file
shaders/shaders_raymarching.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_raymarching.dir/build.make examples/CMakeFiles/shaders_raymarching.dir/shaders/shaders_raymarching.c.i
.PHONY : shaders/shaders_raymarching.c.i

shaders/shaders_raymarching.s: shaders/shaders_raymarching.c.s
.PHONY : shaders/shaders_raymarching.s

# target to generate assembly for a file
shaders/shaders_raymarching.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_raymarching.dir/build.make examples/CMakeFiles/shaders_raymarching.dir/shaders/shaders_raymarching.c.s
.PHONY : shaders/shaders_raymarching.c.s

shaders/shaders_shapes_textures.o: shaders/shaders_shapes_textures.c.o
.PHONY : shaders/shaders_shapes_textures.o

# target to build an object file
shaders/shaders_shapes_textures.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_shapes_textures.dir/build.make examples/CMakeFiles/shaders_shapes_textures.dir/shaders/shaders_shapes_textures.c.o
.PHONY : shaders/shaders_shapes_textures.c.o

shaders/shaders_shapes_textures.i: shaders/shaders_shapes_textures.c.i
.PHONY : shaders/shaders_shapes_textures.i

# target to preprocess a source file
shaders/shaders_shapes_textures.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_shapes_textures.dir/build.make examples/CMakeFiles/shaders_shapes_textures.dir/shaders/shaders_shapes_textures.c.i
.PHONY : shaders/shaders_shapes_textures.c.i

shaders/shaders_shapes_textures.s: shaders/shaders_shapes_textures.c.s
.PHONY : shaders/shaders_shapes_textures.s

# target to generate assembly for a file
shaders/shaders_shapes_textures.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_shapes_textures.dir/build.make examples/CMakeFiles/shaders_shapes_textures.dir/shaders/shaders_shapes_textures.c.s
.PHONY : shaders/shaders_shapes_textures.c.s

shaders/shaders_simple_mask.o: shaders/shaders_simple_mask.c.o
.PHONY : shaders/shaders_simple_mask.o

# target to build an object file
shaders/shaders_simple_mask.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_simple_mask.dir/build.make examples/CMakeFiles/shaders_simple_mask.dir/shaders/shaders_simple_mask.c.o
.PHONY : shaders/shaders_simple_mask.c.o

shaders/shaders_simple_mask.i: shaders/shaders_simple_mask.c.i
.PHONY : shaders/shaders_simple_mask.i

# target to preprocess a source file
shaders/shaders_simple_mask.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_simple_mask.dir/build.make examples/CMakeFiles/shaders_simple_mask.dir/shaders/shaders_simple_mask.c.i
.PHONY : shaders/shaders_simple_mask.c.i

shaders/shaders_simple_mask.s: shaders/shaders_simple_mask.c.s
.PHONY : shaders/shaders_simple_mask.s

# target to generate assembly for a file
shaders/shaders_simple_mask.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_simple_mask.dir/build.make examples/CMakeFiles/shaders_simple_mask.dir/shaders/shaders_simple_mask.c.s
.PHONY : shaders/shaders_simple_mask.c.s

shaders/shaders_spotlight.o: shaders/shaders_spotlight.c.o
.PHONY : shaders/shaders_spotlight.o

# target to build an object file
shaders/shaders_spotlight.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_spotlight.dir/build.make examples/CMakeFiles/shaders_spotlight.dir/shaders/shaders_spotlight.c.o
.PHONY : shaders/shaders_spotlight.c.o

shaders/shaders_spotlight.i: shaders/shaders_spotlight.c.i
.PHONY : shaders/shaders_spotlight.i

# target to preprocess a source file
shaders/shaders_spotlight.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_spotlight.dir/build.make examples/CMakeFiles/shaders_spotlight.dir/shaders/shaders_spotlight.c.i
.PHONY : shaders/shaders_spotlight.c.i

shaders/shaders_spotlight.s: shaders/shaders_spotlight.c.s
.PHONY : shaders/shaders_spotlight.s

# target to generate assembly for a file
shaders/shaders_spotlight.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_spotlight.dir/build.make examples/CMakeFiles/shaders_spotlight.dir/shaders/shaders_spotlight.c.s
.PHONY : shaders/shaders_spotlight.c.s

shaders/shaders_texture_drawing.o: shaders/shaders_texture_drawing.c.o
.PHONY : shaders/shaders_texture_drawing.o

# target to build an object file
shaders/shaders_texture_drawing.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_drawing.dir/build.make examples/CMakeFiles/shaders_texture_drawing.dir/shaders/shaders_texture_drawing.c.o
.PHONY : shaders/shaders_texture_drawing.c.o

shaders/shaders_texture_drawing.i: shaders/shaders_texture_drawing.c.i
.PHONY : shaders/shaders_texture_drawing.i

# target to preprocess a source file
shaders/shaders_texture_drawing.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_drawing.dir/build.make examples/CMakeFiles/shaders_texture_drawing.dir/shaders/shaders_texture_drawing.c.i
.PHONY : shaders/shaders_texture_drawing.c.i

shaders/shaders_texture_drawing.s: shaders/shaders_texture_drawing.c.s
.PHONY : shaders/shaders_texture_drawing.s

# target to generate assembly for a file
shaders/shaders_texture_drawing.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_drawing.dir/build.make examples/CMakeFiles/shaders_texture_drawing.dir/shaders/shaders_texture_drawing.c.s
.PHONY : shaders/shaders_texture_drawing.c.s

shaders/shaders_texture_outline.o: shaders/shaders_texture_outline.c.o
.PHONY : shaders/shaders_texture_outline.o

# target to build an object file
shaders/shaders_texture_outline.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_outline.dir/build.make examples/CMakeFiles/shaders_texture_outline.dir/shaders/shaders_texture_outline.c.o
.PHONY : shaders/shaders_texture_outline.c.o

shaders/shaders_texture_outline.i: shaders/shaders_texture_outline.c.i
.PHONY : shaders/shaders_texture_outline.i

# target to preprocess a source file
shaders/shaders_texture_outline.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_outline.dir/build.make examples/CMakeFiles/shaders_texture_outline.dir/shaders/shaders_texture_outline.c.i
.PHONY : shaders/shaders_texture_outline.c.i

shaders/shaders_texture_outline.s: shaders/shaders_texture_outline.c.s
.PHONY : shaders/shaders_texture_outline.s

# target to generate assembly for a file
shaders/shaders_texture_outline.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_outline.dir/build.make examples/CMakeFiles/shaders_texture_outline.dir/shaders/shaders_texture_outline.c.s
.PHONY : shaders/shaders_texture_outline.c.s

shaders/shaders_texture_waves.o: shaders/shaders_texture_waves.c.o
.PHONY : shaders/shaders_texture_waves.o

# target to build an object file
shaders/shaders_texture_waves.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_waves.dir/build.make examples/CMakeFiles/shaders_texture_waves.dir/shaders/shaders_texture_waves.c.o
.PHONY : shaders/shaders_texture_waves.c.o

shaders/shaders_texture_waves.i: shaders/shaders_texture_waves.c.i
.PHONY : shaders/shaders_texture_waves.i

# target to preprocess a source file
shaders/shaders_texture_waves.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_waves.dir/build.make examples/CMakeFiles/shaders_texture_waves.dir/shaders/shaders_texture_waves.c.i
.PHONY : shaders/shaders_texture_waves.c.i

shaders/shaders_texture_waves.s: shaders/shaders_texture_waves.c.s
.PHONY : shaders/shaders_texture_waves.s

# target to generate assembly for a file
shaders/shaders_texture_waves.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_texture_waves.dir/build.make examples/CMakeFiles/shaders_texture_waves.dir/shaders/shaders_texture_waves.c.s
.PHONY : shaders/shaders_texture_waves.c.s

shaders/shaders_write_depth.o: shaders/shaders_write_depth.c.o
.PHONY : shaders/shaders_write_depth.o

# target to build an object file
shaders/shaders_write_depth.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_write_depth.dir/build.make examples/CMakeFiles/shaders_write_depth.dir/shaders/shaders_write_depth.c.o
.PHONY : shaders/shaders_write_depth.c.o

shaders/shaders_write_depth.i: shaders/shaders_write_depth.c.i
.PHONY : shaders/shaders_write_depth.i

# target to preprocess a source file
shaders/shaders_write_depth.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_write_depth.dir/build.make examples/CMakeFiles/shaders_write_depth.dir/shaders/shaders_write_depth.c.i
.PHONY : shaders/shaders_write_depth.c.i

shaders/shaders_write_depth.s: shaders/shaders_write_depth.c.s
.PHONY : shaders/shaders_write_depth.s

# target to generate assembly for a file
shaders/shaders_write_depth.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shaders_write_depth.dir/build.make examples/CMakeFiles/shaders_write_depth.dir/shaders/shaders_write_depth.c.s
.PHONY : shaders/shaders_write_depth.c.s

shapes/shapes_basic_shapes.o: shapes/shapes_basic_shapes.c.o
.PHONY : shapes/shapes_basic_shapes.o

# target to build an object file
shapes/shapes_basic_shapes.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_basic_shapes.dir/build.make examples/CMakeFiles/shapes_basic_shapes.dir/shapes/shapes_basic_shapes.c.o
.PHONY : shapes/shapes_basic_shapes.c.o

shapes/shapes_basic_shapes.i: shapes/shapes_basic_shapes.c.i
.PHONY : shapes/shapes_basic_shapes.i

# target to preprocess a source file
shapes/shapes_basic_shapes.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_basic_shapes.dir/build.make examples/CMakeFiles/shapes_basic_shapes.dir/shapes/shapes_basic_shapes.c.i
.PHONY : shapes/shapes_basic_shapes.c.i

shapes/shapes_basic_shapes.s: shapes/shapes_basic_shapes.c.s
.PHONY : shapes/shapes_basic_shapes.s

# target to generate assembly for a file
shapes/shapes_basic_shapes.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_basic_shapes.dir/build.make examples/CMakeFiles/shapes_basic_shapes.dir/shapes/shapes_basic_shapes.c.s
.PHONY : shapes/shapes_basic_shapes.c.s

shapes/shapes_bouncing_ball.o: shapes/shapes_bouncing_ball.c.o
.PHONY : shapes/shapes_bouncing_ball.o

# target to build an object file
shapes/shapes_bouncing_ball.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_bouncing_ball.dir/build.make examples/CMakeFiles/shapes_bouncing_ball.dir/shapes/shapes_bouncing_ball.c.o
.PHONY : shapes/shapes_bouncing_ball.c.o

shapes/shapes_bouncing_ball.i: shapes/shapes_bouncing_ball.c.i
.PHONY : shapes/shapes_bouncing_ball.i

# target to preprocess a source file
shapes/shapes_bouncing_ball.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_bouncing_ball.dir/build.make examples/CMakeFiles/shapes_bouncing_ball.dir/shapes/shapes_bouncing_ball.c.i
.PHONY : shapes/shapes_bouncing_ball.c.i

shapes/shapes_bouncing_ball.s: shapes/shapes_bouncing_ball.c.s
.PHONY : shapes/shapes_bouncing_ball.s

# target to generate assembly for a file
shapes/shapes_bouncing_ball.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_bouncing_ball.dir/build.make examples/CMakeFiles/shapes_bouncing_ball.dir/shapes/shapes_bouncing_ball.c.s
.PHONY : shapes/shapes_bouncing_ball.c.s

shapes/shapes_collision_area.o: shapes/shapes_collision_area.c.o
.PHONY : shapes/shapes_collision_area.o

# target to build an object file
shapes/shapes_collision_area.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_collision_area.dir/build.make examples/CMakeFiles/shapes_collision_area.dir/shapes/shapes_collision_area.c.o
.PHONY : shapes/shapes_collision_area.c.o

shapes/shapes_collision_area.i: shapes/shapes_collision_area.c.i
.PHONY : shapes/shapes_collision_area.i

# target to preprocess a source file
shapes/shapes_collision_area.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_collision_area.dir/build.make examples/CMakeFiles/shapes_collision_area.dir/shapes/shapes_collision_area.c.i
.PHONY : shapes/shapes_collision_area.c.i

shapes/shapes_collision_area.s: shapes/shapes_collision_area.c.s
.PHONY : shapes/shapes_collision_area.s

# target to generate assembly for a file
shapes/shapes_collision_area.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_collision_area.dir/build.make examples/CMakeFiles/shapes_collision_area.dir/shapes/shapes_collision_area.c.s
.PHONY : shapes/shapes_collision_area.c.s

shapes/shapes_colors_palette.o: shapes/shapes_colors_palette.c.o
.PHONY : shapes/shapes_colors_palette.o

# target to build an object file
shapes/shapes_colors_palette.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_colors_palette.dir/build.make examples/CMakeFiles/shapes_colors_palette.dir/shapes/shapes_colors_palette.c.o
.PHONY : shapes/shapes_colors_palette.c.o

shapes/shapes_colors_palette.i: shapes/shapes_colors_palette.c.i
.PHONY : shapes/shapes_colors_palette.i

# target to preprocess a source file
shapes/shapes_colors_palette.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_colors_palette.dir/build.make examples/CMakeFiles/shapes_colors_palette.dir/shapes/shapes_colors_palette.c.i
.PHONY : shapes/shapes_colors_palette.c.i

shapes/shapes_colors_palette.s: shapes/shapes_colors_palette.c.s
.PHONY : shapes/shapes_colors_palette.s

# target to generate assembly for a file
shapes/shapes_colors_palette.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_colors_palette.dir/build.make examples/CMakeFiles/shapes_colors_palette.dir/shapes/shapes_colors_palette.c.s
.PHONY : shapes/shapes_colors_palette.c.s

shapes/shapes_draw_circle_sector.o: shapes/shapes_draw_circle_sector.c.o
.PHONY : shapes/shapes_draw_circle_sector.o

# target to build an object file
shapes/shapes_draw_circle_sector.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_circle_sector.dir/build.make examples/CMakeFiles/shapes_draw_circle_sector.dir/shapes/shapes_draw_circle_sector.c.o
.PHONY : shapes/shapes_draw_circle_sector.c.o

shapes/shapes_draw_circle_sector.i: shapes/shapes_draw_circle_sector.c.i
.PHONY : shapes/shapes_draw_circle_sector.i

# target to preprocess a source file
shapes/shapes_draw_circle_sector.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_circle_sector.dir/build.make examples/CMakeFiles/shapes_draw_circle_sector.dir/shapes/shapes_draw_circle_sector.c.i
.PHONY : shapes/shapes_draw_circle_sector.c.i

shapes/shapes_draw_circle_sector.s: shapes/shapes_draw_circle_sector.c.s
.PHONY : shapes/shapes_draw_circle_sector.s

# target to generate assembly for a file
shapes/shapes_draw_circle_sector.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_circle_sector.dir/build.make examples/CMakeFiles/shapes_draw_circle_sector.dir/shapes/shapes_draw_circle_sector.c.s
.PHONY : shapes/shapes_draw_circle_sector.c.s

shapes/shapes_draw_rectangle_rounded.o: shapes/shapes_draw_rectangle_rounded.c.o
.PHONY : shapes/shapes_draw_rectangle_rounded.o

# target to build an object file
shapes/shapes_draw_rectangle_rounded.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/build.make examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/shapes/shapes_draw_rectangle_rounded.c.o
.PHONY : shapes/shapes_draw_rectangle_rounded.c.o

shapes/shapes_draw_rectangle_rounded.i: shapes/shapes_draw_rectangle_rounded.c.i
.PHONY : shapes/shapes_draw_rectangle_rounded.i

# target to preprocess a source file
shapes/shapes_draw_rectangle_rounded.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/build.make examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/shapes/shapes_draw_rectangle_rounded.c.i
.PHONY : shapes/shapes_draw_rectangle_rounded.c.i

shapes/shapes_draw_rectangle_rounded.s: shapes/shapes_draw_rectangle_rounded.c.s
.PHONY : shapes/shapes_draw_rectangle_rounded.s

# target to generate assembly for a file
shapes/shapes_draw_rectangle_rounded.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/build.make examples/CMakeFiles/shapes_draw_rectangle_rounded.dir/shapes/shapes_draw_rectangle_rounded.c.s
.PHONY : shapes/shapes_draw_rectangle_rounded.c.s

shapes/shapes_draw_ring.o: shapes/shapes_draw_ring.c.o
.PHONY : shapes/shapes_draw_ring.o

# target to build an object file
shapes/shapes_draw_ring.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_ring.dir/build.make examples/CMakeFiles/shapes_draw_ring.dir/shapes/shapes_draw_ring.c.o
.PHONY : shapes/shapes_draw_ring.c.o

shapes/shapes_draw_ring.i: shapes/shapes_draw_ring.c.i
.PHONY : shapes/shapes_draw_ring.i

# target to preprocess a source file
shapes/shapes_draw_ring.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_ring.dir/build.make examples/CMakeFiles/shapes_draw_ring.dir/shapes/shapes_draw_ring.c.i
.PHONY : shapes/shapes_draw_ring.c.i

shapes/shapes_draw_ring.s: shapes/shapes_draw_ring.c.s
.PHONY : shapes/shapes_draw_ring.s

# target to generate assembly for a file
shapes/shapes_draw_ring.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_draw_ring.dir/build.make examples/CMakeFiles/shapes_draw_ring.dir/shapes/shapes_draw_ring.c.s
.PHONY : shapes/shapes_draw_ring.c.s

shapes/shapes_easings_ball_anim.o: shapes/shapes_easings_ball_anim.c.o
.PHONY : shapes/shapes_easings_ball_anim.o

# target to build an object file
shapes/shapes_easings_ball_anim.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_ball_anim.dir/build.make examples/CMakeFiles/shapes_easings_ball_anim.dir/shapes/shapes_easings_ball_anim.c.o
.PHONY : shapes/shapes_easings_ball_anim.c.o

shapes/shapes_easings_ball_anim.i: shapes/shapes_easings_ball_anim.c.i
.PHONY : shapes/shapes_easings_ball_anim.i

# target to preprocess a source file
shapes/shapes_easings_ball_anim.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_ball_anim.dir/build.make examples/CMakeFiles/shapes_easings_ball_anim.dir/shapes/shapes_easings_ball_anim.c.i
.PHONY : shapes/shapes_easings_ball_anim.c.i

shapes/shapes_easings_ball_anim.s: shapes/shapes_easings_ball_anim.c.s
.PHONY : shapes/shapes_easings_ball_anim.s

# target to generate assembly for a file
shapes/shapes_easings_ball_anim.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_ball_anim.dir/build.make examples/CMakeFiles/shapes_easings_ball_anim.dir/shapes/shapes_easings_ball_anim.c.s
.PHONY : shapes/shapes_easings_ball_anim.c.s

shapes/shapes_easings_box_anim.o: shapes/shapes_easings_box_anim.c.o
.PHONY : shapes/shapes_easings_box_anim.o

# target to build an object file
shapes/shapes_easings_box_anim.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_box_anim.dir/build.make examples/CMakeFiles/shapes_easings_box_anim.dir/shapes/shapes_easings_box_anim.c.o
.PHONY : shapes/shapes_easings_box_anim.c.o

shapes/shapes_easings_box_anim.i: shapes/shapes_easings_box_anim.c.i
.PHONY : shapes/shapes_easings_box_anim.i

# target to preprocess a source file
shapes/shapes_easings_box_anim.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_box_anim.dir/build.make examples/CMakeFiles/shapes_easings_box_anim.dir/shapes/shapes_easings_box_anim.c.i
.PHONY : shapes/shapes_easings_box_anim.c.i

shapes/shapes_easings_box_anim.s: shapes/shapes_easings_box_anim.c.s
.PHONY : shapes/shapes_easings_box_anim.s

# target to generate assembly for a file
shapes/shapes_easings_box_anim.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_box_anim.dir/build.make examples/CMakeFiles/shapes_easings_box_anim.dir/shapes/shapes_easings_box_anim.c.s
.PHONY : shapes/shapes_easings_box_anim.c.s

shapes/shapes_easings_rectangle_array.o: shapes/shapes_easings_rectangle_array.c.o
.PHONY : shapes/shapes_easings_rectangle_array.o

# target to build an object file
shapes/shapes_easings_rectangle_array.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_rectangle_array.dir/build.make examples/CMakeFiles/shapes_easings_rectangle_array.dir/shapes/shapes_easings_rectangle_array.c.o
.PHONY : shapes/shapes_easings_rectangle_array.c.o

shapes/shapes_easings_rectangle_array.i: shapes/shapes_easings_rectangle_array.c.i
.PHONY : shapes/shapes_easings_rectangle_array.i

# target to preprocess a source file
shapes/shapes_easings_rectangle_array.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_rectangle_array.dir/build.make examples/CMakeFiles/shapes_easings_rectangle_array.dir/shapes/shapes_easings_rectangle_array.c.i
.PHONY : shapes/shapes_easings_rectangle_array.c.i

shapes/shapes_easings_rectangle_array.s: shapes/shapes_easings_rectangle_array.c.s
.PHONY : shapes/shapes_easings_rectangle_array.s

# target to generate assembly for a file
shapes/shapes_easings_rectangle_array.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_easings_rectangle_array.dir/build.make examples/CMakeFiles/shapes_easings_rectangle_array.dir/shapes/shapes_easings_rectangle_array.c.s
.PHONY : shapes/shapes_easings_rectangle_array.c.s

shapes/shapes_following_eyes.o: shapes/shapes_following_eyes.c.o
.PHONY : shapes/shapes_following_eyes.o

# target to build an object file
shapes/shapes_following_eyes.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_following_eyes.dir/build.make examples/CMakeFiles/shapes_following_eyes.dir/shapes/shapes_following_eyes.c.o
.PHONY : shapes/shapes_following_eyes.c.o

shapes/shapes_following_eyes.i: shapes/shapes_following_eyes.c.i
.PHONY : shapes/shapes_following_eyes.i

# target to preprocess a source file
shapes/shapes_following_eyes.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_following_eyes.dir/build.make examples/CMakeFiles/shapes_following_eyes.dir/shapes/shapes_following_eyes.c.i
.PHONY : shapes/shapes_following_eyes.c.i

shapes/shapes_following_eyes.s: shapes/shapes_following_eyes.c.s
.PHONY : shapes/shapes_following_eyes.s

# target to generate assembly for a file
shapes/shapes_following_eyes.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_following_eyes.dir/build.make examples/CMakeFiles/shapes_following_eyes.dir/shapes/shapes_following_eyes.c.s
.PHONY : shapes/shapes_following_eyes.c.s

shapes/shapes_lines_bezier.o: shapes/shapes_lines_bezier.c.o
.PHONY : shapes/shapes_lines_bezier.o

# target to build an object file
shapes/shapes_lines_bezier.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_lines_bezier.dir/build.make examples/CMakeFiles/shapes_lines_bezier.dir/shapes/shapes_lines_bezier.c.o
.PHONY : shapes/shapes_lines_bezier.c.o

shapes/shapes_lines_bezier.i: shapes/shapes_lines_bezier.c.i
.PHONY : shapes/shapes_lines_bezier.i

# target to preprocess a source file
shapes/shapes_lines_bezier.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_lines_bezier.dir/build.make examples/CMakeFiles/shapes_lines_bezier.dir/shapes/shapes_lines_bezier.c.i
.PHONY : shapes/shapes_lines_bezier.c.i

shapes/shapes_lines_bezier.s: shapes/shapes_lines_bezier.c.s
.PHONY : shapes/shapes_lines_bezier.s

# target to generate assembly for a file
shapes/shapes_lines_bezier.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_lines_bezier.dir/build.make examples/CMakeFiles/shapes_lines_bezier.dir/shapes/shapes_lines_bezier.c.s
.PHONY : shapes/shapes_lines_bezier.c.s

shapes/shapes_logo_raylib.o: shapes/shapes_logo_raylib.c.o
.PHONY : shapes/shapes_logo_raylib.o

# target to build an object file
shapes/shapes_logo_raylib.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_logo_raylib.dir/build.make examples/CMakeFiles/shapes_logo_raylib.dir/shapes/shapes_logo_raylib.c.o
.PHONY : shapes/shapes_logo_raylib.c.o

shapes/shapes_logo_raylib.i: shapes/shapes_logo_raylib.c.i
.PHONY : shapes/shapes_logo_raylib.i

# target to preprocess a source file
shapes/shapes_logo_raylib.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_logo_raylib.dir/build.make examples/CMakeFiles/shapes_logo_raylib.dir/shapes/shapes_logo_raylib.c.i
.PHONY : shapes/shapes_logo_raylib.c.i

shapes/shapes_logo_raylib.s: shapes/shapes_logo_raylib.c.s
.PHONY : shapes/shapes_logo_raylib.s

# target to generate assembly for a file
shapes/shapes_logo_raylib.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_logo_raylib.dir/build.make examples/CMakeFiles/shapes_logo_raylib.dir/shapes/shapes_logo_raylib.c.s
.PHONY : shapes/shapes_logo_raylib.c.s

shapes/shapes_logo_raylib_anim.o: shapes/shapes_logo_raylib_anim.c.o
.PHONY : shapes/shapes_logo_raylib_anim.o

# target to build an object file
shapes/shapes_logo_raylib_anim.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_logo_raylib_anim.dir/build.make examples/CMakeFiles/shapes_logo_raylib_anim.dir/shapes/shapes_logo_raylib_anim.c.o
.PHONY : shapes/shapes_logo_raylib_anim.c.o

shapes/shapes_logo_raylib_anim.i: shapes/shapes_logo_raylib_anim.c.i
.PHONY : shapes/shapes_logo_raylib_anim.i

# target to preprocess a source file
shapes/shapes_logo_raylib_anim.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_logo_raylib_anim.dir/build.make examples/CMakeFiles/shapes_logo_raylib_anim.dir/shapes/shapes_logo_raylib_anim.c.i
.PHONY : shapes/shapes_logo_raylib_anim.c.i

shapes/shapes_logo_raylib_anim.s: shapes/shapes_logo_raylib_anim.c.s
.PHONY : shapes/shapes_logo_raylib_anim.s

# target to generate assembly for a file
shapes/shapes_logo_raylib_anim.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_logo_raylib_anim.dir/build.make examples/CMakeFiles/shapes_logo_raylib_anim.dir/shapes/shapes_logo_raylib_anim.c.s
.PHONY : shapes/shapes_logo_raylib_anim.c.s

shapes/shapes_rectangle_scaling.o: shapes/shapes_rectangle_scaling.c.o
.PHONY : shapes/shapes_rectangle_scaling.o

# target to build an object file
shapes/shapes_rectangle_scaling.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_rectangle_scaling.dir/build.make examples/CMakeFiles/shapes_rectangle_scaling.dir/shapes/shapes_rectangle_scaling.c.o
.PHONY : shapes/shapes_rectangle_scaling.c.o

shapes/shapes_rectangle_scaling.i: shapes/shapes_rectangle_scaling.c.i
.PHONY : shapes/shapes_rectangle_scaling.i

# target to preprocess a source file
shapes/shapes_rectangle_scaling.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_rectangle_scaling.dir/build.make examples/CMakeFiles/shapes_rectangle_scaling.dir/shapes/shapes_rectangle_scaling.c.i
.PHONY : shapes/shapes_rectangle_scaling.c.i

shapes/shapes_rectangle_scaling.s: shapes/shapes_rectangle_scaling.c.s
.PHONY : shapes/shapes_rectangle_scaling.s

# target to generate assembly for a file
shapes/shapes_rectangle_scaling.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_rectangle_scaling.dir/build.make examples/CMakeFiles/shapes_rectangle_scaling.dir/shapes/shapes_rectangle_scaling.c.s
.PHONY : shapes/shapes_rectangle_scaling.c.s

shapes/shapes_top_down_lights.o: shapes/shapes_top_down_lights.c.o
.PHONY : shapes/shapes_top_down_lights.o

# target to build an object file
shapes/shapes_top_down_lights.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_top_down_lights.dir/build.make examples/CMakeFiles/shapes_top_down_lights.dir/shapes/shapes_top_down_lights.c.o
.PHONY : shapes/shapes_top_down_lights.c.o

shapes/shapes_top_down_lights.i: shapes/shapes_top_down_lights.c.i
.PHONY : shapes/shapes_top_down_lights.i

# target to preprocess a source file
shapes/shapes_top_down_lights.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_top_down_lights.dir/build.make examples/CMakeFiles/shapes_top_down_lights.dir/shapes/shapes_top_down_lights.c.i
.PHONY : shapes/shapes_top_down_lights.c.i

shapes/shapes_top_down_lights.s: shapes/shapes_top_down_lights.c.s
.PHONY : shapes/shapes_top_down_lights.s

# target to generate assembly for a file
shapes/shapes_top_down_lights.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/shapes_top_down_lights.dir/build.make examples/CMakeFiles/shapes_top_down_lights.dir/shapes/shapes_top_down_lights.c.s
.PHONY : shapes/shapes_top_down_lights.c.s

text/text_codepoints_loading.o: text/text_codepoints_loading.c.o
.PHONY : text/text_codepoints_loading.o

# target to build an object file
text/text_codepoints_loading.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_codepoints_loading.dir/build.make examples/CMakeFiles/text_codepoints_loading.dir/text/text_codepoints_loading.c.o
.PHONY : text/text_codepoints_loading.c.o

text/text_codepoints_loading.i: text/text_codepoints_loading.c.i
.PHONY : text/text_codepoints_loading.i

# target to preprocess a source file
text/text_codepoints_loading.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_codepoints_loading.dir/build.make examples/CMakeFiles/text_codepoints_loading.dir/text/text_codepoints_loading.c.i
.PHONY : text/text_codepoints_loading.c.i

text/text_codepoints_loading.s: text/text_codepoints_loading.c.s
.PHONY : text/text_codepoints_loading.s

# target to generate assembly for a file
text/text_codepoints_loading.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_codepoints_loading.dir/build.make examples/CMakeFiles/text_codepoints_loading.dir/text/text_codepoints_loading.c.s
.PHONY : text/text_codepoints_loading.c.s

text/text_draw_3d.o: text/text_draw_3d.c.o
.PHONY : text/text_draw_3d.o

# target to build an object file
text/text_draw_3d.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_draw_3d.dir/build.make examples/CMakeFiles/text_draw_3d.dir/text/text_draw_3d.c.o
.PHONY : text/text_draw_3d.c.o

text/text_draw_3d.i: text/text_draw_3d.c.i
.PHONY : text/text_draw_3d.i

# target to preprocess a source file
text/text_draw_3d.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_draw_3d.dir/build.make examples/CMakeFiles/text_draw_3d.dir/text/text_draw_3d.c.i
.PHONY : text/text_draw_3d.c.i

text/text_draw_3d.s: text/text_draw_3d.c.s
.PHONY : text/text_draw_3d.s

# target to generate assembly for a file
text/text_draw_3d.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_draw_3d.dir/build.make examples/CMakeFiles/text_draw_3d.dir/text/text_draw_3d.c.s
.PHONY : text/text_draw_3d.c.s

text/text_font_filters.o: text/text_font_filters.c.o
.PHONY : text/text_font_filters.o

# target to build an object file
text/text_font_filters.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_filters.dir/build.make examples/CMakeFiles/text_font_filters.dir/text/text_font_filters.c.o
.PHONY : text/text_font_filters.c.o

text/text_font_filters.i: text/text_font_filters.c.i
.PHONY : text/text_font_filters.i

# target to preprocess a source file
text/text_font_filters.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_filters.dir/build.make examples/CMakeFiles/text_font_filters.dir/text/text_font_filters.c.i
.PHONY : text/text_font_filters.c.i

text/text_font_filters.s: text/text_font_filters.c.s
.PHONY : text/text_font_filters.s

# target to generate assembly for a file
text/text_font_filters.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_filters.dir/build.make examples/CMakeFiles/text_font_filters.dir/text/text_font_filters.c.s
.PHONY : text/text_font_filters.c.s

text/text_font_loading.o: text/text_font_loading.c.o
.PHONY : text/text_font_loading.o

# target to build an object file
text/text_font_loading.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_loading.dir/build.make examples/CMakeFiles/text_font_loading.dir/text/text_font_loading.c.o
.PHONY : text/text_font_loading.c.o

text/text_font_loading.i: text/text_font_loading.c.i
.PHONY : text/text_font_loading.i

# target to preprocess a source file
text/text_font_loading.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_loading.dir/build.make examples/CMakeFiles/text_font_loading.dir/text/text_font_loading.c.i
.PHONY : text/text_font_loading.c.i

text/text_font_loading.s: text/text_font_loading.c.s
.PHONY : text/text_font_loading.s

# target to generate assembly for a file
text/text_font_loading.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_loading.dir/build.make examples/CMakeFiles/text_font_loading.dir/text/text_font_loading.c.s
.PHONY : text/text_font_loading.c.s

text/text_font_sdf.o: text/text_font_sdf.c.o
.PHONY : text/text_font_sdf.o

# target to build an object file
text/text_font_sdf.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_sdf.dir/build.make examples/CMakeFiles/text_font_sdf.dir/text/text_font_sdf.c.o
.PHONY : text/text_font_sdf.c.o

text/text_font_sdf.i: text/text_font_sdf.c.i
.PHONY : text/text_font_sdf.i

# target to preprocess a source file
text/text_font_sdf.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_sdf.dir/build.make examples/CMakeFiles/text_font_sdf.dir/text/text_font_sdf.c.i
.PHONY : text/text_font_sdf.c.i

text/text_font_sdf.s: text/text_font_sdf.c.s
.PHONY : text/text_font_sdf.s

# target to generate assembly for a file
text/text_font_sdf.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_sdf.dir/build.make examples/CMakeFiles/text_font_sdf.dir/text/text_font_sdf.c.s
.PHONY : text/text_font_sdf.c.s

text/text_font_spritefont.o: text/text_font_spritefont.c.o
.PHONY : text/text_font_spritefont.o

# target to build an object file
text/text_font_spritefont.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_spritefont.dir/build.make examples/CMakeFiles/text_font_spritefont.dir/text/text_font_spritefont.c.o
.PHONY : text/text_font_spritefont.c.o

text/text_font_spritefont.i: text/text_font_spritefont.c.i
.PHONY : text/text_font_spritefont.i

# target to preprocess a source file
text/text_font_spritefont.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_spritefont.dir/build.make examples/CMakeFiles/text_font_spritefont.dir/text/text_font_spritefont.c.i
.PHONY : text/text_font_spritefont.c.i

text/text_font_spritefont.s: text/text_font_spritefont.c.s
.PHONY : text/text_font_spritefont.s

# target to generate assembly for a file
text/text_font_spritefont.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_font_spritefont.dir/build.make examples/CMakeFiles/text_font_spritefont.dir/text/text_font_spritefont.c.s
.PHONY : text/text_font_spritefont.c.s

text/text_format_text.o: text/text_format_text.c.o
.PHONY : text/text_format_text.o

# target to build an object file
text/text_format_text.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_format_text.dir/build.make examples/CMakeFiles/text_format_text.dir/text/text_format_text.c.o
.PHONY : text/text_format_text.c.o

text/text_format_text.i: text/text_format_text.c.i
.PHONY : text/text_format_text.i

# target to preprocess a source file
text/text_format_text.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_format_text.dir/build.make examples/CMakeFiles/text_format_text.dir/text/text_format_text.c.i
.PHONY : text/text_format_text.c.i

text/text_format_text.s: text/text_format_text.c.s
.PHONY : text/text_format_text.s

# target to generate assembly for a file
text/text_format_text.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_format_text.dir/build.make examples/CMakeFiles/text_format_text.dir/text/text_format_text.c.s
.PHONY : text/text_format_text.c.s

text/text_input_box.o: text/text_input_box.c.o
.PHONY : text/text_input_box.o

# target to build an object file
text/text_input_box.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_input_box.dir/build.make examples/CMakeFiles/text_input_box.dir/text/text_input_box.c.o
.PHONY : text/text_input_box.c.o

text/text_input_box.i: text/text_input_box.c.i
.PHONY : text/text_input_box.i

# target to preprocess a source file
text/text_input_box.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_input_box.dir/build.make examples/CMakeFiles/text_input_box.dir/text/text_input_box.c.i
.PHONY : text/text_input_box.c.i

text/text_input_box.s: text/text_input_box.c.s
.PHONY : text/text_input_box.s

# target to generate assembly for a file
text/text_input_box.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_input_box.dir/build.make examples/CMakeFiles/text_input_box.dir/text/text_input_box.c.s
.PHONY : text/text_input_box.c.s

text/text_raylib_fonts.o: text/text_raylib_fonts.c.o
.PHONY : text/text_raylib_fonts.o

# target to build an object file
text/text_raylib_fonts.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_raylib_fonts.dir/build.make examples/CMakeFiles/text_raylib_fonts.dir/text/text_raylib_fonts.c.o
.PHONY : text/text_raylib_fonts.c.o

text/text_raylib_fonts.i: text/text_raylib_fonts.c.i
.PHONY : text/text_raylib_fonts.i

# target to preprocess a source file
text/text_raylib_fonts.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_raylib_fonts.dir/build.make examples/CMakeFiles/text_raylib_fonts.dir/text/text_raylib_fonts.c.i
.PHONY : text/text_raylib_fonts.c.i

text/text_raylib_fonts.s: text/text_raylib_fonts.c.s
.PHONY : text/text_raylib_fonts.s

# target to generate assembly for a file
text/text_raylib_fonts.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_raylib_fonts.dir/build.make examples/CMakeFiles/text_raylib_fonts.dir/text/text_raylib_fonts.c.s
.PHONY : text/text_raylib_fonts.c.s

text/text_rectangle_bounds.o: text/text_rectangle_bounds.c.o
.PHONY : text/text_rectangle_bounds.o

# target to build an object file
text/text_rectangle_bounds.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_rectangle_bounds.dir/build.make examples/CMakeFiles/text_rectangle_bounds.dir/text/text_rectangle_bounds.c.o
.PHONY : text/text_rectangle_bounds.c.o

text/text_rectangle_bounds.i: text/text_rectangle_bounds.c.i
.PHONY : text/text_rectangle_bounds.i

# target to preprocess a source file
text/text_rectangle_bounds.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_rectangle_bounds.dir/build.make examples/CMakeFiles/text_rectangle_bounds.dir/text/text_rectangle_bounds.c.i
.PHONY : text/text_rectangle_bounds.c.i

text/text_rectangle_bounds.s: text/text_rectangle_bounds.c.s
.PHONY : text/text_rectangle_bounds.s

# target to generate assembly for a file
text/text_rectangle_bounds.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_rectangle_bounds.dir/build.make examples/CMakeFiles/text_rectangle_bounds.dir/text/text_rectangle_bounds.c.s
.PHONY : text/text_rectangle_bounds.c.s

text/text_unicode.o: text/text_unicode.c.o
.PHONY : text/text_unicode.o

# target to build an object file
text/text_unicode.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_unicode.dir/build.make examples/CMakeFiles/text_unicode.dir/text/text_unicode.c.o
.PHONY : text/text_unicode.c.o

text/text_unicode.i: text/text_unicode.c.i
.PHONY : text/text_unicode.i

# target to preprocess a source file
text/text_unicode.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_unicode.dir/build.make examples/CMakeFiles/text_unicode.dir/text/text_unicode.c.i
.PHONY : text/text_unicode.c.i

text/text_unicode.s: text/text_unicode.c.s
.PHONY : text/text_unicode.s

# target to generate assembly for a file
text/text_unicode.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_unicode.dir/build.make examples/CMakeFiles/text_unicode.dir/text/text_unicode.c.s
.PHONY : text/text_unicode.c.s

text/text_writing_anim.o: text/text_writing_anim.c.o
.PHONY : text/text_writing_anim.o

# target to build an object file
text/text_writing_anim.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_writing_anim.dir/build.make examples/CMakeFiles/text_writing_anim.dir/text/text_writing_anim.c.o
.PHONY : text/text_writing_anim.c.o

text/text_writing_anim.i: text/text_writing_anim.c.i
.PHONY : text/text_writing_anim.i

# target to preprocess a source file
text/text_writing_anim.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_writing_anim.dir/build.make examples/CMakeFiles/text_writing_anim.dir/text/text_writing_anim.c.i
.PHONY : text/text_writing_anim.c.i

text/text_writing_anim.s: text/text_writing_anim.c.s
.PHONY : text/text_writing_anim.s

# target to generate assembly for a file
text/text_writing_anim.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/text_writing_anim.dir/build.make examples/CMakeFiles/text_writing_anim.dir/text/text_writing_anim.c.s
.PHONY : text/text_writing_anim.c.s

textures/textures_background_scrolling.o: textures/textures_background_scrolling.c.o
.PHONY : textures/textures_background_scrolling.o

# target to build an object file
textures/textures_background_scrolling.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_background_scrolling.dir/build.make examples/CMakeFiles/textures_background_scrolling.dir/textures/textures_background_scrolling.c.o
.PHONY : textures/textures_background_scrolling.c.o

textures/textures_background_scrolling.i: textures/textures_background_scrolling.c.i
.PHONY : textures/textures_background_scrolling.i

# target to preprocess a source file
textures/textures_background_scrolling.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_background_scrolling.dir/build.make examples/CMakeFiles/textures_background_scrolling.dir/textures/textures_background_scrolling.c.i
.PHONY : textures/textures_background_scrolling.c.i

textures/textures_background_scrolling.s: textures/textures_background_scrolling.c.s
.PHONY : textures/textures_background_scrolling.s

# target to generate assembly for a file
textures/textures_background_scrolling.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_background_scrolling.dir/build.make examples/CMakeFiles/textures_background_scrolling.dir/textures/textures_background_scrolling.c.s
.PHONY : textures/textures_background_scrolling.c.s

textures/textures_blend_modes.o: textures/textures_blend_modes.c.o
.PHONY : textures/textures_blend_modes.o

# target to build an object file
textures/textures_blend_modes.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_blend_modes.dir/build.make examples/CMakeFiles/textures_blend_modes.dir/textures/textures_blend_modes.c.o
.PHONY : textures/textures_blend_modes.c.o

textures/textures_blend_modes.i: textures/textures_blend_modes.c.i
.PHONY : textures/textures_blend_modes.i

# target to preprocess a source file
textures/textures_blend_modes.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_blend_modes.dir/build.make examples/CMakeFiles/textures_blend_modes.dir/textures/textures_blend_modes.c.i
.PHONY : textures/textures_blend_modes.c.i

textures/textures_blend_modes.s: textures/textures_blend_modes.c.s
.PHONY : textures/textures_blend_modes.s

# target to generate assembly for a file
textures/textures_blend_modes.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_blend_modes.dir/build.make examples/CMakeFiles/textures_blend_modes.dir/textures/textures_blend_modes.c.s
.PHONY : textures/textures_blend_modes.c.s

textures/textures_bunnymark.o: textures/textures_bunnymark.c.o
.PHONY : textures/textures_bunnymark.o

# target to build an object file
textures/textures_bunnymark.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_bunnymark.dir/build.make examples/CMakeFiles/textures_bunnymark.dir/textures/textures_bunnymark.c.o
.PHONY : textures/textures_bunnymark.c.o

textures/textures_bunnymark.i: textures/textures_bunnymark.c.i
.PHONY : textures/textures_bunnymark.i

# target to preprocess a source file
textures/textures_bunnymark.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_bunnymark.dir/build.make examples/CMakeFiles/textures_bunnymark.dir/textures/textures_bunnymark.c.i
.PHONY : textures/textures_bunnymark.c.i

textures/textures_bunnymark.s: textures/textures_bunnymark.c.s
.PHONY : textures/textures_bunnymark.s

# target to generate assembly for a file
textures/textures_bunnymark.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_bunnymark.dir/build.make examples/CMakeFiles/textures_bunnymark.dir/textures/textures_bunnymark.c.s
.PHONY : textures/textures_bunnymark.c.s

textures/textures_draw_tiled.o: textures/textures_draw_tiled.c.o
.PHONY : textures/textures_draw_tiled.o

# target to build an object file
textures/textures_draw_tiled.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_draw_tiled.dir/build.make examples/CMakeFiles/textures_draw_tiled.dir/textures/textures_draw_tiled.c.o
.PHONY : textures/textures_draw_tiled.c.o

textures/textures_draw_tiled.i: textures/textures_draw_tiled.c.i
.PHONY : textures/textures_draw_tiled.i

# target to preprocess a source file
textures/textures_draw_tiled.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_draw_tiled.dir/build.make examples/CMakeFiles/textures_draw_tiled.dir/textures/textures_draw_tiled.c.i
.PHONY : textures/textures_draw_tiled.c.i

textures/textures_draw_tiled.s: textures/textures_draw_tiled.c.s
.PHONY : textures/textures_draw_tiled.s

# target to generate assembly for a file
textures/textures_draw_tiled.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_draw_tiled.dir/build.make examples/CMakeFiles/textures_draw_tiled.dir/textures/textures_draw_tiled.c.s
.PHONY : textures/textures_draw_tiled.c.s

textures/textures_fog_of_war.o: textures/textures_fog_of_war.c.o
.PHONY : textures/textures_fog_of_war.o

# target to build an object file
textures/textures_fog_of_war.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_fog_of_war.dir/build.make examples/CMakeFiles/textures_fog_of_war.dir/textures/textures_fog_of_war.c.o
.PHONY : textures/textures_fog_of_war.c.o

textures/textures_fog_of_war.i: textures/textures_fog_of_war.c.i
.PHONY : textures/textures_fog_of_war.i

# target to preprocess a source file
textures/textures_fog_of_war.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_fog_of_war.dir/build.make examples/CMakeFiles/textures_fog_of_war.dir/textures/textures_fog_of_war.c.i
.PHONY : textures/textures_fog_of_war.c.i

textures/textures_fog_of_war.s: textures/textures_fog_of_war.c.s
.PHONY : textures/textures_fog_of_war.s

# target to generate assembly for a file
textures/textures_fog_of_war.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_fog_of_war.dir/build.make examples/CMakeFiles/textures_fog_of_war.dir/textures/textures_fog_of_war.c.s
.PHONY : textures/textures_fog_of_war.c.s

textures/textures_gif_player.o: textures/textures_gif_player.c.o
.PHONY : textures/textures_gif_player.o

# target to build an object file
textures/textures_gif_player.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_gif_player.dir/build.make examples/CMakeFiles/textures_gif_player.dir/textures/textures_gif_player.c.o
.PHONY : textures/textures_gif_player.c.o

textures/textures_gif_player.i: textures/textures_gif_player.c.i
.PHONY : textures/textures_gif_player.i

# target to preprocess a source file
textures/textures_gif_player.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_gif_player.dir/build.make examples/CMakeFiles/textures_gif_player.dir/textures/textures_gif_player.c.i
.PHONY : textures/textures_gif_player.c.i

textures/textures_gif_player.s: textures/textures_gif_player.c.s
.PHONY : textures/textures_gif_player.s

# target to generate assembly for a file
textures/textures_gif_player.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_gif_player.dir/build.make examples/CMakeFiles/textures_gif_player.dir/textures/textures_gif_player.c.s
.PHONY : textures/textures_gif_player.c.s

textures/textures_image_drawing.o: textures/textures_image_drawing.c.o
.PHONY : textures/textures_image_drawing.o

# target to build an object file
textures/textures_image_drawing.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_drawing.dir/build.make examples/CMakeFiles/textures_image_drawing.dir/textures/textures_image_drawing.c.o
.PHONY : textures/textures_image_drawing.c.o

textures/textures_image_drawing.i: textures/textures_image_drawing.c.i
.PHONY : textures/textures_image_drawing.i

# target to preprocess a source file
textures/textures_image_drawing.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_drawing.dir/build.make examples/CMakeFiles/textures_image_drawing.dir/textures/textures_image_drawing.c.i
.PHONY : textures/textures_image_drawing.c.i

textures/textures_image_drawing.s: textures/textures_image_drawing.c.s
.PHONY : textures/textures_image_drawing.s

# target to generate assembly for a file
textures/textures_image_drawing.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_drawing.dir/build.make examples/CMakeFiles/textures_image_drawing.dir/textures/textures_image_drawing.c.s
.PHONY : textures/textures_image_drawing.c.s

textures/textures_image_generation.o: textures/textures_image_generation.c.o
.PHONY : textures/textures_image_generation.o

# target to build an object file
textures/textures_image_generation.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_generation.dir/build.make examples/CMakeFiles/textures_image_generation.dir/textures/textures_image_generation.c.o
.PHONY : textures/textures_image_generation.c.o

textures/textures_image_generation.i: textures/textures_image_generation.c.i
.PHONY : textures/textures_image_generation.i

# target to preprocess a source file
textures/textures_image_generation.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_generation.dir/build.make examples/CMakeFiles/textures_image_generation.dir/textures/textures_image_generation.c.i
.PHONY : textures/textures_image_generation.c.i

textures/textures_image_generation.s: textures/textures_image_generation.c.s
.PHONY : textures/textures_image_generation.s

# target to generate assembly for a file
textures/textures_image_generation.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_generation.dir/build.make examples/CMakeFiles/textures_image_generation.dir/textures/textures_image_generation.c.s
.PHONY : textures/textures_image_generation.c.s

textures/textures_image_loading.o: textures/textures_image_loading.c.o
.PHONY : textures/textures_image_loading.o

# target to build an object file
textures/textures_image_loading.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_loading.dir/build.make examples/CMakeFiles/textures_image_loading.dir/textures/textures_image_loading.c.o
.PHONY : textures/textures_image_loading.c.o

textures/textures_image_loading.i: textures/textures_image_loading.c.i
.PHONY : textures/textures_image_loading.i

# target to preprocess a source file
textures/textures_image_loading.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_loading.dir/build.make examples/CMakeFiles/textures_image_loading.dir/textures/textures_image_loading.c.i
.PHONY : textures/textures_image_loading.c.i

textures/textures_image_loading.s: textures/textures_image_loading.c.s
.PHONY : textures/textures_image_loading.s

# target to generate assembly for a file
textures/textures_image_loading.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_loading.dir/build.make examples/CMakeFiles/textures_image_loading.dir/textures/textures_image_loading.c.s
.PHONY : textures/textures_image_loading.c.s

textures/textures_image_processing.o: textures/textures_image_processing.c.o
.PHONY : textures/textures_image_processing.o

# target to build an object file
textures/textures_image_processing.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_processing.dir/build.make examples/CMakeFiles/textures_image_processing.dir/textures/textures_image_processing.c.o
.PHONY : textures/textures_image_processing.c.o

textures/textures_image_processing.i: textures/textures_image_processing.c.i
.PHONY : textures/textures_image_processing.i

# target to preprocess a source file
textures/textures_image_processing.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_processing.dir/build.make examples/CMakeFiles/textures_image_processing.dir/textures/textures_image_processing.c.i
.PHONY : textures/textures_image_processing.c.i

textures/textures_image_processing.s: textures/textures_image_processing.c.s
.PHONY : textures/textures_image_processing.s

# target to generate assembly for a file
textures/textures_image_processing.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_processing.dir/build.make examples/CMakeFiles/textures_image_processing.dir/textures/textures_image_processing.c.s
.PHONY : textures/textures_image_processing.c.s

textures/textures_image_text.o: textures/textures_image_text.c.o
.PHONY : textures/textures_image_text.o

# target to build an object file
textures/textures_image_text.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_text.dir/build.make examples/CMakeFiles/textures_image_text.dir/textures/textures_image_text.c.o
.PHONY : textures/textures_image_text.c.o

textures/textures_image_text.i: textures/textures_image_text.c.i
.PHONY : textures/textures_image_text.i

# target to preprocess a source file
textures/textures_image_text.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_text.dir/build.make examples/CMakeFiles/textures_image_text.dir/textures/textures_image_text.c.i
.PHONY : textures/textures_image_text.c.i

textures/textures_image_text.s: textures/textures_image_text.c.s
.PHONY : textures/textures_image_text.s

# target to generate assembly for a file
textures/textures_image_text.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_image_text.dir/build.make examples/CMakeFiles/textures_image_text.dir/textures/textures_image_text.c.s
.PHONY : textures/textures_image_text.c.s

textures/textures_logo_raylib.o: textures/textures_logo_raylib.c.o
.PHONY : textures/textures_logo_raylib.o

# target to build an object file
textures/textures_logo_raylib.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_logo_raylib.dir/build.make examples/CMakeFiles/textures_logo_raylib.dir/textures/textures_logo_raylib.c.o
.PHONY : textures/textures_logo_raylib.c.o

textures/textures_logo_raylib.i: textures/textures_logo_raylib.c.i
.PHONY : textures/textures_logo_raylib.i

# target to preprocess a source file
textures/textures_logo_raylib.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_logo_raylib.dir/build.make examples/CMakeFiles/textures_logo_raylib.dir/textures/textures_logo_raylib.c.i
.PHONY : textures/textures_logo_raylib.c.i

textures/textures_logo_raylib.s: textures/textures_logo_raylib.c.s
.PHONY : textures/textures_logo_raylib.s

# target to generate assembly for a file
textures/textures_logo_raylib.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_logo_raylib.dir/build.make examples/CMakeFiles/textures_logo_raylib.dir/textures/textures_logo_raylib.c.s
.PHONY : textures/textures_logo_raylib.c.s

textures/textures_mouse_painting.o: textures/textures_mouse_painting.c.o
.PHONY : textures/textures_mouse_painting.o

# target to build an object file
textures/textures_mouse_painting.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_mouse_painting.dir/build.make examples/CMakeFiles/textures_mouse_painting.dir/textures/textures_mouse_painting.c.o
.PHONY : textures/textures_mouse_painting.c.o

textures/textures_mouse_painting.i: textures/textures_mouse_painting.c.i
.PHONY : textures/textures_mouse_painting.i

# target to preprocess a source file
textures/textures_mouse_painting.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_mouse_painting.dir/build.make examples/CMakeFiles/textures_mouse_painting.dir/textures/textures_mouse_painting.c.i
.PHONY : textures/textures_mouse_painting.c.i

textures/textures_mouse_painting.s: textures/textures_mouse_painting.c.s
.PHONY : textures/textures_mouse_painting.s

# target to generate assembly for a file
textures/textures_mouse_painting.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_mouse_painting.dir/build.make examples/CMakeFiles/textures_mouse_painting.dir/textures/textures_mouse_painting.c.s
.PHONY : textures/textures_mouse_painting.c.s

textures/textures_npatch_drawing.o: textures/textures_npatch_drawing.c.o
.PHONY : textures/textures_npatch_drawing.o

# target to build an object file
textures/textures_npatch_drawing.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_npatch_drawing.dir/build.make examples/CMakeFiles/textures_npatch_drawing.dir/textures/textures_npatch_drawing.c.o
.PHONY : textures/textures_npatch_drawing.c.o

textures/textures_npatch_drawing.i: textures/textures_npatch_drawing.c.i
.PHONY : textures/textures_npatch_drawing.i

# target to preprocess a source file
textures/textures_npatch_drawing.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_npatch_drawing.dir/build.make examples/CMakeFiles/textures_npatch_drawing.dir/textures/textures_npatch_drawing.c.i
.PHONY : textures/textures_npatch_drawing.c.i

textures/textures_npatch_drawing.s: textures/textures_npatch_drawing.c.s
.PHONY : textures/textures_npatch_drawing.s

# target to generate assembly for a file
textures/textures_npatch_drawing.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_npatch_drawing.dir/build.make examples/CMakeFiles/textures_npatch_drawing.dir/textures/textures_npatch_drawing.c.s
.PHONY : textures/textures_npatch_drawing.c.s

textures/textures_particles_blending.o: textures/textures_particles_blending.c.o
.PHONY : textures/textures_particles_blending.o

# target to build an object file
textures/textures_particles_blending.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_particles_blending.dir/build.make examples/CMakeFiles/textures_particles_blending.dir/textures/textures_particles_blending.c.o
.PHONY : textures/textures_particles_blending.c.o

textures/textures_particles_blending.i: textures/textures_particles_blending.c.i
.PHONY : textures/textures_particles_blending.i

# target to preprocess a source file
textures/textures_particles_blending.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_particles_blending.dir/build.make examples/CMakeFiles/textures_particles_blending.dir/textures/textures_particles_blending.c.i
.PHONY : textures/textures_particles_blending.c.i

textures/textures_particles_blending.s: textures/textures_particles_blending.c.s
.PHONY : textures/textures_particles_blending.s

# target to generate assembly for a file
textures/textures_particles_blending.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_particles_blending.dir/build.make examples/CMakeFiles/textures_particles_blending.dir/textures/textures_particles_blending.c.s
.PHONY : textures/textures_particles_blending.c.s

textures/textures_polygon.o: textures/textures_polygon.c.o
.PHONY : textures/textures_polygon.o

# target to build an object file
textures/textures_polygon.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_polygon.dir/build.make examples/CMakeFiles/textures_polygon.dir/textures/textures_polygon.c.o
.PHONY : textures/textures_polygon.c.o

textures/textures_polygon.i: textures/textures_polygon.c.i
.PHONY : textures/textures_polygon.i

# target to preprocess a source file
textures/textures_polygon.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_polygon.dir/build.make examples/CMakeFiles/textures_polygon.dir/textures/textures_polygon.c.i
.PHONY : textures/textures_polygon.c.i

textures/textures_polygon.s: textures/textures_polygon.c.s
.PHONY : textures/textures_polygon.s

# target to generate assembly for a file
textures/textures_polygon.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_polygon.dir/build.make examples/CMakeFiles/textures_polygon.dir/textures/textures_polygon.c.s
.PHONY : textures/textures_polygon.c.s

textures/textures_raw_data.o: textures/textures_raw_data.c.o
.PHONY : textures/textures_raw_data.o

# target to build an object file
textures/textures_raw_data.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_raw_data.dir/build.make examples/CMakeFiles/textures_raw_data.dir/textures/textures_raw_data.c.o
.PHONY : textures/textures_raw_data.c.o

textures/textures_raw_data.i: textures/textures_raw_data.c.i
.PHONY : textures/textures_raw_data.i

# target to preprocess a source file
textures/textures_raw_data.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_raw_data.dir/build.make examples/CMakeFiles/textures_raw_data.dir/textures/textures_raw_data.c.i
.PHONY : textures/textures_raw_data.c.i

textures/textures_raw_data.s: textures/textures_raw_data.c.s
.PHONY : textures/textures_raw_data.s

# target to generate assembly for a file
textures/textures_raw_data.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_raw_data.dir/build.make examples/CMakeFiles/textures_raw_data.dir/textures/textures_raw_data.c.s
.PHONY : textures/textures_raw_data.c.s

textures/textures_sprite_anim.o: textures/textures_sprite_anim.c.o
.PHONY : textures/textures_sprite_anim.o

# target to build an object file
textures/textures_sprite_anim.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_anim.dir/build.make examples/CMakeFiles/textures_sprite_anim.dir/textures/textures_sprite_anim.c.o
.PHONY : textures/textures_sprite_anim.c.o

textures/textures_sprite_anim.i: textures/textures_sprite_anim.c.i
.PHONY : textures/textures_sprite_anim.i

# target to preprocess a source file
textures/textures_sprite_anim.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_anim.dir/build.make examples/CMakeFiles/textures_sprite_anim.dir/textures/textures_sprite_anim.c.i
.PHONY : textures/textures_sprite_anim.c.i

textures/textures_sprite_anim.s: textures/textures_sprite_anim.c.s
.PHONY : textures/textures_sprite_anim.s

# target to generate assembly for a file
textures/textures_sprite_anim.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_anim.dir/build.make examples/CMakeFiles/textures_sprite_anim.dir/textures/textures_sprite_anim.c.s
.PHONY : textures/textures_sprite_anim.c.s

textures/textures_sprite_button.o: textures/textures_sprite_button.c.o
.PHONY : textures/textures_sprite_button.o

# target to build an object file
textures/textures_sprite_button.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_button.dir/build.make examples/CMakeFiles/textures_sprite_button.dir/textures/textures_sprite_button.c.o
.PHONY : textures/textures_sprite_button.c.o

textures/textures_sprite_button.i: textures/textures_sprite_button.c.i
.PHONY : textures/textures_sprite_button.i

# target to preprocess a source file
textures/textures_sprite_button.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_button.dir/build.make examples/CMakeFiles/textures_sprite_button.dir/textures/textures_sprite_button.c.i
.PHONY : textures/textures_sprite_button.c.i

textures/textures_sprite_button.s: textures/textures_sprite_button.c.s
.PHONY : textures/textures_sprite_button.s

# target to generate assembly for a file
textures/textures_sprite_button.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_button.dir/build.make examples/CMakeFiles/textures_sprite_button.dir/textures/textures_sprite_button.c.s
.PHONY : textures/textures_sprite_button.c.s

textures/textures_sprite_explosion.o: textures/textures_sprite_explosion.c.o
.PHONY : textures/textures_sprite_explosion.o

# target to build an object file
textures/textures_sprite_explosion.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_explosion.dir/build.make examples/CMakeFiles/textures_sprite_explosion.dir/textures/textures_sprite_explosion.c.o
.PHONY : textures/textures_sprite_explosion.c.o

textures/textures_sprite_explosion.i: textures/textures_sprite_explosion.c.i
.PHONY : textures/textures_sprite_explosion.i

# target to preprocess a source file
textures/textures_sprite_explosion.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_explosion.dir/build.make examples/CMakeFiles/textures_sprite_explosion.dir/textures/textures_sprite_explosion.c.i
.PHONY : textures/textures_sprite_explosion.c.i

textures/textures_sprite_explosion.s: textures/textures_sprite_explosion.c.s
.PHONY : textures/textures_sprite_explosion.s

# target to generate assembly for a file
textures/textures_sprite_explosion.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_sprite_explosion.dir/build.make examples/CMakeFiles/textures_sprite_explosion.dir/textures/textures_sprite_explosion.c.s
.PHONY : textures/textures_sprite_explosion.c.s

textures/textures_srcrec_dstrec.o: textures/textures_srcrec_dstrec.c.o
.PHONY : textures/textures_srcrec_dstrec.o

# target to build an object file
textures/textures_srcrec_dstrec.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_srcrec_dstrec.dir/build.make examples/CMakeFiles/textures_srcrec_dstrec.dir/textures/textures_srcrec_dstrec.c.o
.PHONY : textures/textures_srcrec_dstrec.c.o

textures/textures_srcrec_dstrec.i: textures/textures_srcrec_dstrec.c.i
.PHONY : textures/textures_srcrec_dstrec.i

# target to preprocess a source file
textures/textures_srcrec_dstrec.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_srcrec_dstrec.dir/build.make examples/CMakeFiles/textures_srcrec_dstrec.dir/textures/textures_srcrec_dstrec.c.i
.PHONY : textures/textures_srcrec_dstrec.c.i

textures/textures_srcrec_dstrec.s: textures/textures_srcrec_dstrec.c.s
.PHONY : textures/textures_srcrec_dstrec.s

# target to generate assembly for a file
textures/textures_srcrec_dstrec.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_srcrec_dstrec.dir/build.make examples/CMakeFiles/textures_srcrec_dstrec.dir/textures/textures_srcrec_dstrec.c.s
.PHONY : textures/textures_srcrec_dstrec.c.s

textures/textures_textured_curve.o: textures/textures_textured_curve.c.o
.PHONY : textures/textures_textured_curve.o

# target to build an object file
textures/textures_textured_curve.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_textured_curve.dir/build.make examples/CMakeFiles/textures_textured_curve.dir/textures/textures_textured_curve.c.o
.PHONY : textures/textures_textured_curve.c.o

textures/textures_textured_curve.i: textures/textures_textured_curve.c.i
.PHONY : textures/textures_textured_curve.i

# target to preprocess a source file
textures/textures_textured_curve.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_textured_curve.dir/build.make examples/CMakeFiles/textures_textured_curve.dir/textures/textures_textured_curve.c.i
.PHONY : textures/textures_textured_curve.c.i

textures/textures_textured_curve.s: textures/textures_textured_curve.c.s
.PHONY : textures/textures_textured_curve.s

# target to generate assembly for a file
textures/textures_textured_curve.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_textured_curve.dir/build.make examples/CMakeFiles/textures_textured_curve.dir/textures/textures_textured_curve.c.s
.PHONY : textures/textures_textured_curve.c.s

textures/textures_to_image.o: textures/textures_to_image.c.o
.PHONY : textures/textures_to_image.o

# target to build an object file
textures/textures_to_image.c.o:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_to_image.dir/build.make examples/CMakeFiles/textures_to_image.dir/textures/textures_to_image.c.o
.PHONY : textures/textures_to_image.c.o

textures/textures_to_image.i: textures/textures_to_image.c.i
.PHONY : textures/textures_to_image.i

# target to preprocess a source file
textures/textures_to_image.c.i:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_to_image.dir/build.make examples/CMakeFiles/textures_to_image.dir/textures/textures_to_image.c.i
.PHONY : textures/textures_to_image.c.i

textures/textures_to_image.s: textures/textures_to_image.c.s
.PHONY : textures/textures_to_image.s

# target to generate assembly for a file
textures/textures_to_image.c.s:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(MAKE) $(MAKESILENT) -f examples/CMakeFiles/textures_to_image.dir/build.make examples/CMakeFiles/textures_to_image.dir/textures/textures_to_image.c.s
.PHONY : textures/textures_to_image.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... audio_mixed_processor"
	@echo "... audio_module_playing"
	@echo "... audio_music_stream"
	@echo "... audio_raw_stream"
	@echo "... audio_sound_loading"
	@echo "... audio_stream_effects"
	@echo "... core_2d_camera"
	@echo "... core_2d_camera_mouse_zoom"
	@echo "... core_2d_camera_platformer"
	@echo "... core_3d_camera_first_person"
	@echo "... core_3d_camera_free"
	@echo "... core_3d_camera_mode"
	@echo "... core_3d_picking"
	@echo "... core_basic_screen_manager"
	@echo "... core_basic_window"
	@echo "... core_basic_window_web"
	@echo "... core_custom_frame_control"
	@echo "... core_custom_logging"
	@echo "... core_drop_files"
	@echo "... core_input_gamepad"
	@echo "... core_input_gestures"
	@echo "... core_input_keys"
	@echo "... core_input_mouse"
	@echo "... core_input_mouse_wheel"
	@echo "... core_input_multitouch"
	@echo "... core_loading_thread"
	@echo "... core_random_values"
	@echo "... core_scissor_test"
	@echo "... core_smooth_pixelperfect"
	@echo "... core_split_screen"
	@echo "... core_storage_values"
	@echo "... core_vr_simulator"
	@echo "... core_window_flags"
	@echo "... core_window_letterbox"
	@echo "... core_window_should_close"
	@echo "... core_world_screen"
	@echo "... easings_testbed"
	@echo "... embedded_files_loading"
	@echo "... models_animation"
	@echo "... models_billboard"
	@echo "... models_box_collisions"
	@echo "... models_cubicmap"
	@echo "... models_draw_cube_texture"
	@echo "... models_first_person_maze"
	@echo "... models_geometric_shapes"
	@echo "... models_heightmap"
	@echo "... models_loading"
	@echo "... models_loading_gltf"
	@echo "... models_loading_m3d"
	@echo "... models_loading_vox"
	@echo "... models_mesh_generation"
	@echo "... models_mesh_picking"
	@echo "... models_orthographic_projection"
	@echo "... models_rlgl_solar_system"
	@echo "... models_skybox"
	@echo "... models_waving_cubes"
	@echo "... models_yaw_pitch_roll"
	@echo "... raylib_opengl_interop"
	@echo "... rlgl_compute_shader"
	@echo "... rlgl_standalone"
	@echo "... shaders_basic_lighting"
	@echo "... shaders_custom_uniform"
	@echo "... shaders_eratosthenes"
	@echo "... shaders_fog"
	@echo "... shaders_hot_reloading"
	@echo "... shaders_hybrid_render"
	@echo "... shaders_julia_set"
	@echo "... shaders_mesh_instancing"
	@echo "... shaders_model_shader"
	@echo "... shaders_multi_sample2d"
	@echo "... shaders_palette_switch"
	@echo "... shaders_postprocessing"
	@echo "... shaders_raymarching"
	@echo "... shaders_shapes_textures"
	@echo "... shaders_simple_mask"
	@echo "... shaders_spotlight"
	@echo "... shaders_texture_drawing"
	@echo "... shaders_texture_outline"
	@echo "... shaders_texture_waves"
	@echo "... shaders_write_depth"
	@echo "... shapes_basic_shapes"
	@echo "... shapes_bouncing_ball"
	@echo "... shapes_collision_area"
	@echo "... shapes_colors_palette"
	@echo "... shapes_draw_circle_sector"
	@echo "... shapes_draw_rectangle_rounded"
	@echo "... shapes_draw_ring"
	@echo "... shapes_easings_ball_anim"
	@echo "... shapes_easings_box_anim"
	@echo "... shapes_easings_rectangle_array"
	@echo "... shapes_following_eyes"
	@echo "... shapes_lines_bezier"
	@echo "... shapes_logo_raylib"
	@echo "... shapes_logo_raylib_anim"
	@echo "... shapes_rectangle_scaling"
	@echo "... shapes_top_down_lights"
	@echo "... text_codepoints_loading"
	@echo "... text_draw_3d"
	@echo "... text_font_filters"
	@echo "... text_font_loading"
	@echo "... text_font_sdf"
	@echo "... text_font_spritefont"
	@echo "... text_format_text"
	@echo "... text_input_box"
	@echo "... text_raylib_fonts"
	@echo "... text_rectangle_bounds"
	@echo "... text_unicode"
	@echo "... text_writing_anim"
	@echo "... textures_background_scrolling"
	@echo "... textures_blend_modes"
	@echo "... textures_bunnymark"
	@echo "... textures_draw_tiled"
	@echo "... textures_fog_of_war"
	@echo "... textures_gif_player"
	@echo "... textures_image_drawing"
	@echo "... textures_image_generation"
	@echo "... textures_image_loading"
	@echo "... textures_image_processing"
	@echo "... textures_image_text"
	@echo "... textures_logo_raylib"
	@echo "... textures_mouse_painting"
	@echo "... textures_npatch_drawing"
	@echo "... textures_particles_blending"
	@echo "... textures_polygon"
	@echo "... textures_raw_data"
	@echo "... textures_sprite_anim"
	@echo "... textures_sprite_button"
	@echo "... textures_sprite_explosion"
	@echo "... textures_srcrec_dstrec"
	@echo "... textures_textured_curve"
	@echo "... textures_to_image"
	@echo "... audio/audio_mixed_processor.o"
	@echo "... audio/audio_mixed_processor.i"
	@echo "... audio/audio_mixed_processor.s"
	@echo "... audio/audio_module_playing.o"
	@echo "... audio/audio_module_playing.i"
	@echo "... audio/audio_module_playing.s"
	@echo "... audio/audio_music_stream.o"
	@echo "... audio/audio_music_stream.i"
	@echo "... audio/audio_music_stream.s"
	@echo "... audio/audio_raw_stream.o"
	@echo "... audio/audio_raw_stream.i"
	@echo "... audio/audio_raw_stream.s"
	@echo "... audio/audio_sound_loading.o"
	@echo "... audio/audio_sound_loading.i"
	@echo "... audio/audio_sound_loading.s"
	@echo "... audio/audio_stream_effects.o"
	@echo "... audio/audio_stream_effects.i"
	@echo "... audio/audio_stream_effects.s"
	@echo "... core/core_2d_camera.o"
	@echo "... core/core_2d_camera.i"
	@echo "... core/core_2d_camera.s"
	@echo "... core/core_2d_camera_mouse_zoom.o"
	@echo "... core/core_2d_camera_mouse_zoom.i"
	@echo "... core/core_2d_camera_mouse_zoom.s"
	@echo "... core/core_2d_camera_platformer.o"
	@echo "... core/core_2d_camera_platformer.i"
	@echo "... core/core_2d_camera_platformer.s"
	@echo "... core/core_3d_camera_first_person.o"
	@echo "... core/core_3d_camera_first_person.i"
	@echo "... core/core_3d_camera_first_person.s"
	@echo "... core/core_3d_camera_free.o"
	@echo "... core/core_3d_camera_free.i"
	@echo "... core/core_3d_camera_free.s"
	@echo "... core/core_3d_camera_mode.o"
	@echo "... core/core_3d_camera_mode.i"
	@echo "... core/core_3d_camera_mode.s"
	@echo "... core/core_3d_picking.o"
	@echo "... core/core_3d_picking.i"
	@echo "... core/core_3d_picking.s"
	@echo "... core/core_basic_screen_manager.o"
	@echo "... core/core_basic_screen_manager.i"
	@echo "... core/core_basic_screen_manager.s"
	@echo "... core/core_basic_window.o"
	@echo "... core/core_basic_window.i"
	@echo "... core/core_basic_window.s"
	@echo "... core/core_basic_window_web.o"
	@echo "... core/core_basic_window_web.i"
	@echo "... core/core_basic_window_web.s"
	@echo "... core/core_custom_frame_control.o"
	@echo "... core/core_custom_frame_control.i"
	@echo "... core/core_custom_frame_control.s"
	@echo "... core/core_custom_logging.o"
	@echo "... core/core_custom_logging.i"
	@echo "... core/core_custom_logging.s"
	@echo "... core/core_drop_files.o"
	@echo "... core/core_drop_files.i"
	@echo "... core/core_drop_files.s"
	@echo "... core/core_input_gamepad.o"
	@echo "... core/core_input_gamepad.i"
	@echo "... core/core_input_gamepad.s"
	@echo "... core/core_input_gestures.o"
	@echo "... core/core_input_gestures.i"
	@echo "... core/core_input_gestures.s"
	@echo "... core/core_input_keys.o"
	@echo "... core/core_input_keys.i"
	@echo "... core/core_input_keys.s"
	@echo "... core/core_input_mouse.o"
	@echo "... core/core_input_mouse.i"
	@echo "... core/core_input_mouse.s"
	@echo "... core/core_input_mouse_wheel.o"
	@echo "... core/core_input_mouse_wheel.i"
	@echo "... core/core_input_mouse_wheel.s"
	@echo "... core/core_input_multitouch.o"
	@echo "... core/core_input_multitouch.i"
	@echo "... core/core_input_multitouch.s"
	@echo "... core/core_loading_thread.o"
	@echo "... core/core_loading_thread.i"
	@echo "... core/core_loading_thread.s"
	@echo "... core/core_random_values.o"
	@echo "... core/core_random_values.i"
	@echo "... core/core_random_values.s"
	@echo "... core/core_scissor_test.o"
	@echo "... core/core_scissor_test.i"
	@echo "... core/core_scissor_test.s"
	@echo "... core/core_smooth_pixelperfect.o"
	@echo "... core/core_smooth_pixelperfect.i"
	@echo "... core/core_smooth_pixelperfect.s"
	@echo "... core/core_split_screen.o"
	@echo "... core/core_split_screen.i"
	@echo "... core/core_split_screen.s"
	@echo "... core/core_storage_values.o"
	@echo "... core/core_storage_values.i"
	@echo "... core/core_storage_values.s"
	@echo "... core/core_vr_simulator.o"
	@echo "... core/core_vr_simulator.i"
	@echo "... core/core_vr_simulator.s"
	@echo "... core/core_window_flags.o"
	@echo "... core/core_window_flags.i"
	@echo "... core/core_window_flags.s"
	@echo "... core/core_window_letterbox.o"
	@echo "... core/core_window_letterbox.i"
	@echo "... core/core_window_letterbox.s"
	@echo "... core/core_window_should_close.o"
	@echo "... core/core_window_should_close.i"
	@echo "... core/core_window_should_close.s"
	@echo "... core/core_world_screen.o"
	@echo "... core/core_world_screen.i"
	@echo "... core/core_world_screen.s"
	@echo "... models/models_animation.o"
	@echo "... models/models_animation.i"
	@echo "... models/models_animation.s"
	@echo "... models/models_billboard.o"
	@echo "... models/models_billboard.i"
	@echo "... models/models_billboard.s"
	@echo "... models/models_box_collisions.o"
	@echo "... models/models_box_collisions.i"
	@echo "... models/models_box_collisions.s"
	@echo "... models/models_cubicmap.o"
	@echo "... models/models_cubicmap.i"
	@echo "... models/models_cubicmap.s"
	@echo "... models/models_draw_cube_texture.o"
	@echo "... models/models_draw_cube_texture.i"
	@echo "... models/models_draw_cube_texture.s"
	@echo "... models/models_first_person_maze.o"
	@echo "... models/models_first_person_maze.i"
	@echo "... models/models_first_person_maze.s"
	@echo "... models/models_geometric_shapes.o"
	@echo "... models/models_geometric_shapes.i"
	@echo "... models/models_geometric_shapes.s"
	@echo "... models/models_heightmap.o"
	@echo "... models/models_heightmap.i"
	@echo "... models/models_heightmap.s"
	@echo "... models/models_loading.o"
	@echo "... models/models_loading.i"
	@echo "... models/models_loading.s"
	@echo "... models/models_loading_gltf.o"
	@echo "... models/models_loading_gltf.i"
	@echo "... models/models_loading_gltf.s"
	@echo "... models/models_loading_m3d.o"
	@echo "... models/models_loading_m3d.i"
	@echo "... models/models_loading_m3d.s"
	@echo "... models/models_loading_vox.o"
	@echo "... models/models_loading_vox.i"
	@echo "... models/models_loading_vox.s"
	@echo "... models/models_mesh_generation.o"
	@echo "... models/models_mesh_generation.i"
	@echo "... models/models_mesh_generation.s"
	@echo "... models/models_mesh_picking.o"
	@echo "... models/models_mesh_picking.i"
	@echo "... models/models_mesh_picking.s"
	@echo "... models/models_orthographic_projection.o"
	@echo "... models/models_orthographic_projection.i"
	@echo "... models/models_orthographic_projection.s"
	@echo "... models/models_rlgl_solar_system.o"
	@echo "... models/models_rlgl_solar_system.i"
	@echo "... models/models_rlgl_solar_system.s"
	@echo "... models/models_skybox.o"
	@echo "... models/models_skybox.i"
	@echo "... models/models_skybox.s"
	@echo "... models/models_waving_cubes.o"
	@echo "... models/models_waving_cubes.i"
	@echo "... models/models_waving_cubes.s"
	@echo "... models/models_yaw_pitch_roll.o"
	@echo "... models/models_yaw_pitch_roll.i"
	@echo "... models/models_yaw_pitch_roll.s"
	@echo "... others/easings_testbed.o"
	@echo "... others/easings_testbed.i"
	@echo "... others/easings_testbed.s"
	@echo "... others/embedded_files_loading.o"
	@echo "... others/embedded_files_loading.i"
	@echo "... others/embedded_files_loading.s"
	@echo "... others/raylib_opengl_interop.o"
	@echo "... others/raylib_opengl_interop.i"
	@echo "... others/raylib_opengl_interop.s"
	@echo "... others/rlgl_compute_shader.o"
	@echo "... others/rlgl_compute_shader.i"
	@echo "... others/rlgl_compute_shader.s"
	@echo "... others/rlgl_standalone.o"
	@echo "... others/rlgl_standalone.i"
	@echo "... others/rlgl_standalone.s"
	@echo "... shaders/shaders_basic_lighting.o"
	@echo "... shaders/shaders_basic_lighting.i"
	@echo "... shaders/shaders_basic_lighting.s"
	@echo "... shaders/shaders_custom_uniform.o"
	@echo "... shaders/shaders_custom_uniform.i"
	@echo "... shaders/shaders_custom_uniform.s"
	@echo "... shaders/shaders_eratosthenes.o"
	@echo "... shaders/shaders_eratosthenes.i"
	@echo "... shaders/shaders_eratosthenes.s"
	@echo "... shaders/shaders_fog.o"
	@echo "... shaders/shaders_fog.i"
	@echo "... shaders/shaders_fog.s"
	@echo "... shaders/shaders_hot_reloading.o"
	@echo "... shaders/shaders_hot_reloading.i"
	@echo "... shaders/shaders_hot_reloading.s"
	@echo "... shaders/shaders_hybrid_render.o"
	@echo "... shaders/shaders_hybrid_render.i"
	@echo "... shaders/shaders_hybrid_render.s"
	@echo "... shaders/shaders_julia_set.o"
	@echo "... shaders/shaders_julia_set.i"
	@echo "... shaders/shaders_julia_set.s"
	@echo "... shaders/shaders_mesh_instancing.o"
	@echo "... shaders/shaders_mesh_instancing.i"
	@echo "... shaders/shaders_mesh_instancing.s"
	@echo "... shaders/shaders_model_shader.o"
	@echo "... shaders/shaders_model_shader.i"
	@echo "... shaders/shaders_model_shader.s"
	@echo "... shaders/shaders_multi_sample2d.o"
	@echo "... shaders/shaders_multi_sample2d.i"
	@echo "... shaders/shaders_multi_sample2d.s"
	@echo "... shaders/shaders_palette_switch.o"
	@echo "... shaders/shaders_palette_switch.i"
	@echo "... shaders/shaders_palette_switch.s"
	@echo "... shaders/shaders_postprocessing.o"
	@echo "... shaders/shaders_postprocessing.i"
	@echo "... shaders/shaders_postprocessing.s"
	@echo "... shaders/shaders_raymarching.o"
	@echo "... shaders/shaders_raymarching.i"
	@echo "... shaders/shaders_raymarching.s"
	@echo "... shaders/shaders_shapes_textures.o"
	@echo "... shaders/shaders_shapes_textures.i"
	@echo "... shaders/shaders_shapes_textures.s"
	@echo "... shaders/shaders_simple_mask.o"
	@echo "... shaders/shaders_simple_mask.i"
	@echo "... shaders/shaders_simple_mask.s"
	@echo "... shaders/shaders_spotlight.o"
	@echo "... shaders/shaders_spotlight.i"
	@echo "... shaders/shaders_spotlight.s"
	@echo "... shaders/shaders_texture_drawing.o"
	@echo "... shaders/shaders_texture_drawing.i"
	@echo "... shaders/shaders_texture_drawing.s"
	@echo "... shaders/shaders_texture_outline.o"
	@echo "... shaders/shaders_texture_outline.i"
	@echo "... shaders/shaders_texture_outline.s"
	@echo "... shaders/shaders_texture_waves.o"
	@echo "... shaders/shaders_texture_waves.i"
	@echo "... shaders/shaders_texture_waves.s"
	@echo "... shaders/shaders_write_depth.o"
	@echo "... shaders/shaders_write_depth.i"
	@echo "... shaders/shaders_write_depth.s"
	@echo "... shapes/shapes_basic_shapes.o"
	@echo "... shapes/shapes_basic_shapes.i"
	@echo "... shapes/shapes_basic_shapes.s"
	@echo "... shapes/shapes_bouncing_ball.o"
	@echo "... shapes/shapes_bouncing_ball.i"
	@echo "... shapes/shapes_bouncing_ball.s"
	@echo "... shapes/shapes_collision_area.o"
	@echo "... shapes/shapes_collision_area.i"
	@echo "... shapes/shapes_collision_area.s"
	@echo "... shapes/shapes_colors_palette.o"
	@echo "... shapes/shapes_colors_palette.i"
	@echo "... shapes/shapes_colors_palette.s"
	@echo "... shapes/shapes_draw_circle_sector.o"
	@echo "... shapes/shapes_draw_circle_sector.i"
	@echo "... shapes/shapes_draw_circle_sector.s"
	@echo "... shapes/shapes_draw_rectangle_rounded.o"
	@echo "... shapes/shapes_draw_rectangle_rounded.i"
	@echo "... shapes/shapes_draw_rectangle_rounded.s"
	@echo "... shapes/shapes_draw_ring.o"
	@echo "... shapes/shapes_draw_ring.i"
	@echo "... shapes/shapes_draw_ring.s"
	@echo "... shapes/shapes_easings_ball_anim.o"
	@echo "... shapes/shapes_easings_ball_anim.i"
	@echo "... shapes/shapes_easings_ball_anim.s"
	@echo "... shapes/shapes_easings_box_anim.o"
	@echo "... shapes/shapes_easings_box_anim.i"
	@echo "... shapes/shapes_easings_box_anim.s"
	@echo "... shapes/shapes_easings_rectangle_array.o"
	@echo "... shapes/shapes_easings_rectangle_array.i"
	@echo "... shapes/shapes_easings_rectangle_array.s"
	@echo "... shapes/shapes_following_eyes.o"
	@echo "... shapes/shapes_following_eyes.i"
	@echo "... shapes/shapes_following_eyes.s"
	@echo "... shapes/shapes_lines_bezier.o"
	@echo "... shapes/shapes_lines_bezier.i"
	@echo "... shapes/shapes_lines_bezier.s"
	@echo "... shapes/shapes_logo_raylib.o"
	@echo "... shapes/shapes_logo_raylib.i"
	@echo "... shapes/shapes_logo_raylib.s"
	@echo "... shapes/shapes_logo_raylib_anim.o"
	@echo "... shapes/shapes_logo_raylib_anim.i"
	@echo "... shapes/shapes_logo_raylib_anim.s"
	@echo "... shapes/shapes_rectangle_scaling.o"
	@echo "... shapes/shapes_rectangle_scaling.i"
	@echo "... shapes/shapes_rectangle_scaling.s"
	@echo "... shapes/shapes_top_down_lights.o"
	@echo "... shapes/shapes_top_down_lights.i"
	@echo "... shapes/shapes_top_down_lights.s"
	@echo "... text/text_codepoints_loading.o"
	@echo "... text/text_codepoints_loading.i"
	@echo "... text/text_codepoints_loading.s"
	@echo "... text/text_draw_3d.o"
	@echo "... text/text_draw_3d.i"
	@echo "... text/text_draw_3d.s"
	@echo "... text/text_font_filters.o"
	@echo "... text/text_font_filters.i"
	@echo "... text/text_font_filters.s"
	@echo "... text/text_font_loading.o"
	@echo "... text/text_font_loading.i"
	@echo "... text/text_font_loading.s"
	@echo "... text/text_font_sdf.o"
	@echo "... text/text_font_sdf.i"
	@echo "... text/text_font_sdf.s"
	@echo "... text/text_font_spritefont.o"
	@echo "... text/text_font_spritefont.i"
	@echo "... text/text_font_spritefont.s"
	@echo "... text/text_format_text.o"
	@echo "... text/text_format_text.i"
	@echo "... text/text_format_text.s"
	@echo "... text/text_input_box.o"
	@echo "... text/text_input_box.i"
	@echo "... text/text_input_box.s"
	@echo "... text/text_raylib_fonts.o"
	@echo "... text/text_raylib_fonts.i"
	@echo "... text/text_raylib_fonts.s"
	@echo "... text/text_rectangle_bounds.o"
	@echo "... text/text_rectangle_bounds.i"
	@echo "... text/text_rectangle_bounds.s"
	@echo "... text/text_unicode.o"
	@echo "... text/text_unicode.i"
	@echo "... text/text_unicode.s"
	@echo "... text/text_writing_anim.o"
	@echo "... text/text_writing_anim.i"
	@echo "... text/text_writing_anim.s"
	@echo "... textures/textures_background_scrolling.o"
	@echo "... textures/textures_background_scrolling.i"
	@echo "... textures/textures_background_scrolling.s"
	@echo "... textures/textures_blend_modes.o"
	@echo "... textures/textures_blend_modes.i"
	@echo "... textures/textures_blend_modes.s"
	@echo "... textures/textures_bunnymark.o"
	@echo "... textures/textures_bunnymark.i"
	@echo "... textures/textures_bunnymark.s"
	@echo "... textures/textures_draw_tiled.o"
	@echo "... textures/textures_draw_tiled.i"
	@echo "... textures/textures_draw_tiled.s"
	@echo "... textures/textures_fog_of_war.o"
	@echo "... textures/textures_fog_of_war.i"
	@echo "... textures/textures_fog_of_war.s"
	@echo "... textures/textures_gif_player.o"
	@echo "... textures/textures_gif_player.i"
	@echo "... textures/textures_gif_player.s"
	@echo "... textures/textures_image_drawing.o"
	@echo "... textures/textures_image_drawing.i"
	@echo "... textures/textures_image_drawing.s"
	@echo "... textures/textures_image_generation.o"
	@echo "... textures/textures_image_generation.i"
	@echo "... textures/textures_image_generation.s"
	@echo "... textures/textures_image_loading.o"
	@echo "... textures/textures_image_loading.i"
	@echo "... textures/textures_image_loading.s"
	@echo "... textures/textures_image_processing.o"
	@echo "... textures/textures_image_processing.i"
	@echo "... textures/textures_image_processing.s"
	@echo "... textures/textures_image_text.o"
	@echo "... textures/textures_image_text.i"
	@echo "... textures/textures_image_text.s"
	@echo "... textures/textures_logo_raylib.o"
	@echo "... textures/textures_logo_raylib.i"
	@echo "... textures/textures_logo_raylib.s"
	@echo "... textures/textures_mouse_painting.o"
	@echo "... textures/textures_mouse_painting.i"
	@echo "... textures/textures_mouse_painting.s"
	@echo "... textures/textures_npatch_drawing.o"
	@echo "... textures/textures_npatch_drawing.i"
	@echo "... textures/textures_npatch_drawing.s"
	@echo "... textures/textures_particles_blending.o"
	@echo "... textures/textures_particles_blending.i"
	@echo "... textures/textures_particles_blending.s"
	@echo "... textures/textures_polygon.o"
	@echo "... textures/textures_polygon.i"
	@echo "... textures/textures_polygon.s"
	@echo "... textures/textures_raw_data.o"
	@echo "... textures/textures_raw_data.i"
	@echo "... textures/textures_raw_data.s"
	@echo "... textures/textures_sprite_anim.o"
	@echo "... textures/textures_sprite_anim.i"
	@echo "... textures/textures_sprite_anim.s"
	@echo "... textures/textures_sprite_button.o"
	@echo "... textures/textures_sprite_button.i"
	@echo "... textures/textures_sprite_button.s"
	@echo "... textures/textures_sprite_explosion.o"
	@echo "... textures/textures_sprite_explosion.i"
	@echo "... textures/textures_sprite_explosion.s"
	@echo "... textures/textures_srcrec_dstrec.o"
	@echo "... textures/textures_srcrec_dstrec.i"
	@echo "... textures/textures_srcrec_dstrec.s"
	@echo "... textures/textures_textured_curve.o"
	@echo "... textures/textures_textured_curve.i"
	@echo "... textures/textures_textured_curve.s"
	@echo "... textures/textures_to_image.o"
	@echo "... textures/textures_to_image.i"
	@echo "... textures/textures_to_image.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/projects/cpp/external/raylib/build && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

