# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/projects/cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/projects/cpp/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/RaylibApp.dir/all
all: external/raylib/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall: external/raylib/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/RaylibApp.dir/clean
clean: external/raylib/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory external/raylib

# Recursive "all" directory target.
external/raylib/all: external/raylib/raylib/all
.PHONY : external/raylib/all

# Recursive "preinstall" directory target.
external/raylib/preinstall: external/raylib/raylib/preinstall
.PHONY : external/raylib/preinstall

# Recursive "clean" directory target.
external/raylib/clean: external/raylib/raylib/clean
.PHONY : external/raylib/clean

#=============================================================================
# Directory level rules for directory external/raylib/raylib

# Recursive "all" directory target.
external/raylib/raylib/all: external/raylib/raylib/CMakeFiles/raylib.dir/all
external/raylib/raylib/all: external/raylib/raylib/external/glfw/all
.PHONY : external/raylib/raylib/all

# Recursive "preinstall" directory target.
external/raylib/raylib/preinstall: external/raylib/raylib/external/glfw/preinstall
.PHONY : external/raylib/raylib/preinstall

# Recursive "clean" directory target.
external/raylib/raylib/clean: external/raylib/raylib/CMakeFiles/raylib.dir/clean
external/raylib/raylib/clean: external/raylib/raylib/external/glfw/clean
.PHONY : external/raylib/raylib/clean

#=============================================================================
# Directory level rules for directory external/raylib/raylib/external/glfw

# Recursive "all" directory target.
external/raylib/raylib/external/glfw/all: external/raylib/raylib/external/glfw/src/all
.PHONY : external/raylib/raylib/external/glfw/all

# Recursive "preinstall" directory target.
external/raylib/raylib/external/glfw/preinstall: external/raylib/raylib/external/glfw/src/preinstall
.PHONY : external/raylib/raylib/external/glfw/preinstall

# Recursive "clean" directory target.
external/raylib/raylib/external/glfw/clean: external/raylib/raylib/external/glfw/src/clean
.PHONY : external/raylib/raylib/external/glfw/clean

#=============================================================================
# Directory level rules for directory external/raylib/raylib/external/glfw/src

# Recursive "all" directory target.
external/raylib/raylib/external/glfw/src/all: external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/all
.PHONY : external/raylib/raylib/external/glfw/src/all

# Recursive "preinstall" directory target.
external/raylib/raylib/external/glfw/src/preinstall:
.PHONY : external/raylib/raylib/external/glfw/src/preinstall

# Recursive "clean" directory target.
external/raylib/raylib/external/glfw/src/clean: external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/clean
external/raylib/raylib/external/glfw/src/clean: external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/clean
.PHONY : external/raylib/raylib/external/glfw/src/clean

#=============================================================================
# Target rules for target CMakeFiles/RaylibApp.dir

# All Build rule for target.
CMakeFiles/RaylibApp.dir/all: external/raylib/raylib/CMakeFiles/raylib.dir/all
CMakeFiles/RaylibApp.dir/all: external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RaylibApp.dir/build.make CMakeFiles/RaylibApp.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RaylibApp.dir/build.make CMakeFiles/RaylibApp.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/projects/cpp/build/CMakeFiles --progress-num=1,2,3 "Built target RaylibApp"
.PHONY : CMakeFiles/RaylibApp.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/RaylibApp.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/build/CMakeFiles 35
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/RaylibApp.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/build/CMakeFiles 0
.PHONY : CMakeFiles/RaylibApp.dir/rule

# Convenience name for target.
RaylibApp: CMakeFiles/RaylibApp.dir/rule
.PHONY : RaylibApp

# clean rule for target.
CMakeFiles/RaylibApp.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/RaylibApp.dir/build.make CMakeFiles/RaylibApp.dir/clean
.PHONY : CMakeFiles/RaylibApp.dir/clean

#=============================================================================
# Target rules for target external/raylib/raylib/CMakeFiles/raylib.dir

# All Build rule for target.
external/raylib/raylib/CMakeFiles/raylib.dir/all: external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/all
	$(MAKE) $(MAKESILENT) -f external/raylib/raylib/CMakeFiles/raylib.dir/build.make external/raylib/raylib/CMakeFiles/raylib.dir/depend
	$(MAKE) $(MAKESILENT) -f external/raylib/raylib/CMakeFiles/raylib.dir/build.make external/raylib/raylib/CMakeFiles/raylib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/projects/cpp/build/CMakeFiles --progress-num=28,29,30,31,32,33,34,35 "Built target raylib"
.PHONY : external/raylib/raylib/CMakeFiles/raylib.dir/all

# Build rule for subdir invocation for target.
external/raylib/raylib/CMakeFiles/raylib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/build/CMakeFiles 32
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 external/raylib/raylib/CMakeFiles/raylib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/build/CMakeFiles 0
.PHONY : external/raylib/raylib/CMakeFiles/raylib.dir/rule

# Convenience name for target.
raylib: external/raylib/raylib/CMakeFiles/raylib.dir/rule
.PHONY : raylib

# clean rule for target.
external/raylib/raylib/CMakeFiles/raylib.dir/clean:
	$(MAKE) $(MAKESILENT) -f external/raylib/raylib/CMakeFiles/raylib.dir/build.make external/raylib/raylib/CMakeFiles/raylib.dir/clean
.PHONY : external/raylib/raylib/CMakeFiles/raylib.dir/clean

#=============================================================================
# Target rules for target external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir

# All Build rule for target.
external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/all:
	$(MAKE) $(MAKESILENT) -f external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/build.make external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/depend
	$(MAKE) $(MAKESILENT) -f external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/build.make external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/projects/cpp/build/CMakeFiles --progress-num=4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27 "Built target glfw"
.PHONY : external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/all

# Build rule for subdir invocation for target.
external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/build/CMakeFiles 24
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/build/CMakeFiles 0
.PHONY : external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/rule

# Convenience name for target.
glfw: external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/rule
.PHONY : glfw

# clean rule for target.
external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/clean:
	$(MAKE) $(MAKESILENT) -f external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/build.make external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/clean
.PHONY : external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/clean

#=============================================================================
# Target rules for target external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir

# All Build rule for target.
external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/all:
	$(MAKE) $(MAKESILENT) -f external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/build.make external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/depend
	$(MAKE) $(MAKESILENT) -f external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/build.make external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/projects/cpp/build/CMakeFiles --progress-num=36 "Built target update_mappings"
.PHONY : external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/all

# Build rule for subdir invocation for target.
external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/build/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/projects/cpp/build/CMakeFiles 0
.PHONY : external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/rule

# Convenience name for target.
update_mappings: external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/rule
.PHONY : update_mappings

# clean rule for target.
external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/clean:
	$(MAKE) $(MAKESILENT) -f external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/build.make external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/clean
.PHONY : external/raylib/raylib/external/glfw/src/CMakeFiles/update_mappings.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

