# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/projects/cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/projects/cpp/build

# Include any dependencies generated for this target.
include CMakeFiles/RaylibApp.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/RaylibApp.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/RaylibApp.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/RaylibApp.dir/flags.make

CMakeFiles/RaylibApp.dir/src/Entity.cpp.o: CMakeFiles/RaylibApp.dir/flags.make
CMakeFiles/RaylibApp.dir/src/Entity.cpp.o: ../src/Entity.cpp
CMakeFiles/RaylibApp.dir/src/Entity.cpp.o: CMakeFiles/RaylibApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/projects/cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/RaylibApp.dir/src/Entity.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RaylibApp.dir/src/Entity.cpp.o -MF CMakeFiles/RaylibApp.dir/src/Entity.cpp.o.d -o CMakeFiles/RaylibApp.dir/src/Entity.cpp.o -c /home/<USER>/projects/cpp/src/Entity.cpp

CMakeFiles/RaylibApp.dir/src/Entity.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/RaylibApp.dir/src/Entity.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/projects/cpp/src/Entity.cpp > CMakeFiles/RaylibApp.dir/src/Entity.cpp.i

CMakeFiles/RaylibApp.dir/src/Entity.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/RaylibApp.dir/src/Entity.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/projects/cpp/src/Entity.cpp -o CMakeFiles/RaylibApp.dir/src/Entity.cpp.s

CMakeFiles/RaylibApp.dir/src/main.cpp.o: CMakeFiles/RaylibApp.dir/flags.make
CMakeFiles/RaylibApp.dir/src/main.cpp.o: ../src/main.cpp
CMakeFiles/RaylibApp.dir/src/main.cpp.o: CMakeFiles/RaylibApp.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/projects/cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/RaylibApp.dir/src/main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/RaylibApp.dir/src/main.cpp.o -MF CMakeFiles/RaylibApp.dir/src/main.cpp.o.d -o CMakeFiles/RaylibApp.dir/src/main.cpp.o -c /home/<USER>/projects/cpp/src/main.cpp

CMakeFiles/RaylibApp.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/RaylibApp.dir/src/main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/projects/cpp/src/main.cpp > CMakeFiles/RaylibApp.dir/src/main.cpp.i

CMakeFiles/RaylibApp.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/RaylibApp.dir/src/main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/projects/cpp/src/main.cpp -o CMakeFiles/RaylibApp.dir/src/main.cpp.s

# Object files for target RaylibApp
RaylibApp_OBJECTS = \
"CMakeFiles/RaylibApp.dir/src/Entity.cpp.o" \
"CMakeFiles/RaylibApp.dir/src/main.cpp.o"

# External object files for target RaylibApp
RaylibApp_EXTERNAL_OBJECTS =

RaylibApp: CMakeFiles/RaylibApp.dir/src/Entity.cpp.o
RaylibApp: CMakeFiles/RaylibApp.dir/src/main.cpp.o
RaylibApp: CMakeFiles/RaylibApp.dir/build.make
RaylibApp: external/raylib/raylib/libraylib.a
RaylibApp: /usr/lib/x86_64-linux-gnu/libOpenGL.so
RaylibApp: /usr/lib/x86_64-linux-gnu/libGLX.so
RaylibApp: /usr/lib/x86_64-linux-gnu/libGLU.so
RaylibApp: external/raylib/raylib/external/glfw/src/libglfw3.a
RaylibApp: /usr/lib/x86_64-linux-gnu/librt.a
RaylibApp: /usr/lib/x86_64-linux-gnu/libm.so
RaylibApp: CMakeFiles/RaylibApp.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/projects/cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable RaylibApp"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/RaylibApp.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/RaylibApp.dir/build: RaylibApp
.PHONY : CMakeFiles/RaylibApp.dir/build

CMakeFiles/RaylibApp.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/RaylibApp.dir/cmake_clean.cmake
.PHONY : CMakeFiles/RaylibApp.dir/clean

CMakeFiles/RaylibApp.dir/depend:
	cd /home/<USER>/projects/cpp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/projects/cpp /home/<USER>/projects/cpp /home/<USER>/projects/cpp/build /home/<USER>/projects/cpp/build /home/<USER>/projects/cpp/build/CMakeFiles/RaylibApp.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/RaylibApp.dir/depend

