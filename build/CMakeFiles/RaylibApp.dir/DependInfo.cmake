
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/home/<USER>/projects/cpp/src/Entity.cpp" "CMakeFiles/RaylibApp.dir/src/Entity.cpp.o" "gcc" "CMakeFiles/RaylibApp.dir/src/Entity.cpp.o.d"
  "/home/<USER>/projects/cpp/src/main.cpp" "CMakeFiles/RaylibApp.dir/src/main.cpp.o" "gcc" "CMakeFiles/RaylibApp.dir/src/main.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  "/home/<USER>/projects/cpp/build/external/raylib/raylib/CMakeFiles/raylib.dir/DependInfo.cmake"
  "/home/<USER>/projects/cpp/build/external/raylib/raylib/external/glfw/src/CMakeFiles/glfw.dir/DependInfo.cmake"
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
