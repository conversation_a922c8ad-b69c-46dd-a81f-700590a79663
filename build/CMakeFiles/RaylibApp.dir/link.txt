/usr/bin/c++ -g -O0 -Wall -Wextra CMakeFiles/RaylibApp.dir/src/Entity.cpp.o CMakeFiles/RaylibApp.dir/src/main.cpp.o -o RaylibApp  external/raylib/raylib/libraylib.a -lm -lpthread -ldl -lrt -lX11 -latomic -lpthread /usr/lib/x86_64-linux-gnu/libOpenGL.so /usr/lib/x86_64-linux-gnu/libGLX.so /usr/lib/x86_64-linux-gnu/libGLU.so external/raylib/raylib/external/glfw/src/libglfw3.a /usr/lib/x86_64-linux-gnu/librt.a -lm -ldl 
